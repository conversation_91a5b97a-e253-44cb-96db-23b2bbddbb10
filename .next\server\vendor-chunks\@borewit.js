"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@borewit";
exports.ids = ["vendor-chunks/@borewit"];
exports.modules = {

/***/ "(rsc)/./node_modules/@borewit/text-codec/lib/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/@borewit/text-codec/lib/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   textDecode: () => (/* binding */ textDecode),\n/* harmony export */   textEncode: () => (/* binding */ textEncode)\n/* harmony export */ });\n// text-polyfill.ts\n// Minimal encode/decode for utf-8, utf-16le, ascii, latin1, windows-1252\nconst WINDOWS_1252_EXTRA = {\n    0x80: \"€\", 0x82: \"‚\", 0x83: \"ƒ\", 0x84: \"„\", 0x85: \"…\", 0x86: \"†\",\n    0x87: \"‡\", 0x88: \"ˆ\", 0x89: \"‰\", 0x8a: \"Š\", 0x8b: \"‹\", 0x8c: \"Œ\",\n    0x8e: \"Ž\", 0x91: \"‘\", 0x92: \"’\", 0x93: \"“\", 0x94: \"”\", 0x95: \"•\",\n    0x96: \"–\", 0x97: \"—\", 0x98: \"˜\", 0x99: \"™\", 0x9a: \"š\", 0x9b: \"›\",\n    0x9c: \"œ\", 0x9e: \"ž\", 0x9f: \"Ÿ\",\n};\nconst WINDOWS_1252_REVERSE = {};\nfor (const [code, char] of Object.entries(WINDOWS_1252_EXTRA)) {\n    WINDOWS_1252_REVERSE[char] = Number.parseInt(code);\n}\n/**\n * Decode text from binary data\n * @param bytes Binary data\n * @param encoding Encoding\n */\nfunction textDecode(bytes, encoding = \"utf-8\") {\n    switch (encoding.toLowerCase()) {\n        case \"utf-8\":\n        case \"utf8\":\n            if (typeof globalThis.TextDecoder !== \"undefined\") {\n                return new globalThis.TextDecoder(\"utf-8\").decode(bytes);\n            }\n            return decodeUTF8(bytes);\n        case \"utf-16le\":\n            return decodeUTF16LE(bytes);\n        case \"ascii\":\n            return decodeASCII(bytes);\n        case \"latin1\":\n        case \"iso-8859-1\":\n            return decodeLatin1(bytes);\n        case \"windows-1252\":\n            return decodeWindows1252(bytes);\n        default:\n            throw new RangeError(`Encoding '${encoding}' not supported`);\n    }\n}\nfunction textEncode(input = \"\", encoding = \"utf-8\") {\n    switch (encoding.toLowerCase()) {\n        case \"utf-8\":\n        case \"utf8\":\n            if (typeof globalThis.TextEncoder !== \"undefined\") {\n                return new globalThis.TextEncoder().encode(input);\n            }\n            return encodeUTF8(input);\n        case \"utf-16le\":\n            return encodeUTF16LE(input);\n        case \"ascii\":\n            return encodeASCII(input);\n        case \"latin1\":\n        case \"iso-8859-1\":\n            return encodeLatin1(input);\n        case \"windows-1252\":\n            return encodeWindows1252(input);\n        default:\n            throw new RangeError(`Encoding '${encoding}' not supported`);\n    }\n}\n// --- Internal helpers ---\nfunction decodeUTF8(bytes) {\n    let out = \"\";\n    let i = 0;\n    while (i < bytes.length) {\n        const b1 = bytes[i++];\n        if (b1 < 0x80) {\n            out += String.fromCharCode(b1);\n        }\n        else if (b1 < 0xe0) {\n            const b2 = bytes[i++] & 0x3f;\n            out += String.fromCharCode(((b1 & 0x1f) << 6) | b2);\n        }\n        else if (b1 < 0xf0) {\n            const b2 = bytes[i++] & 0x3f;\n            const b3 = bytes[i++] & 0x3f;\n            out += String.fromCharCode(((b1 & 0x0f) << 12) | (b2 << 6) | b3);\n        }\n        else {\n            const b2 = bytes[i++] & 0x3f;\n            const b3 = bytes[i++] & 0x3f;\n            const b4 = bytes[i++] & 0x3f;\n            let cp = ((b1 & 0x07) << 18) |\n                (b2 << 12) |\n                (b3 << 6) |\n                b4;\n            cp -= 0x10000;\n            out += String.fromCharCode(0xd800 + ((cp >> 10) & 0x3ff), 0xdc00 + (cp & 0x3ff));\n        }\n    }\n    return out;\n}\nfunction decodeUTF16LE(bytes) {\n    let out = \"\";\n    for (let i = 0; i < bytes.length; i += 2) {\n        out += String.fromCharCode(bytes[i] | (bytes[i + 1] << 8));\n    }\n    return out;\n}\nfunction decodeASCII(bytes) {\n    return String.fromCharCode(...bytes.map((b) => b & 0x7f));\n}\nfunction decodeLatin1(bytes) {\n    return String.fromCharCode(...bytes);\n}\nfunction decodeWindows1252(bytes) {\n    let out = \"\";\n    for (const b of bytes) {\n        if (b >= 0x80 && b <= 0x9f && WINDOWS_1252_EXTRA[b]) {\n            out += WINDOWS_1252_EXTRA[b];\n        }\n        else {\n            out += String.fromCharCode(b);\n        }\n    }\n    return out;\n}\nfunction encodeUTF8(str) {\n    const out = [];\n    for (let i = 0; i < str.length; i++) {\n        const cp = str.charCodeAt(i);\n        if (cp < 0x80) {\n            out.push(cp);\n        }\n        else if (cp < 0x800) {\n            out.push(0xc0 | (cp >> 6), 0x80 | (cp & 0x3f));\n        }\n        else if (cp < 0x10000) {\n            out.push(0xe0 | (cp >> 12), 0x80 | ((cp >> 6) & 0x3f), 0x80 | (cp & 0x3f));\n        }\n        else {\n            out.push(0xf0 | (cp >> 18), 0x80 | ((cp >> 12) & 0x3f), 0x80 | ((cp >> 6) & 0x3f), 0x80 | (cp & 0x3f));\n        }\n    }\n    return new Uint8Array(out);\n}\nfunction encodeUTF16LE(str) {\n    const out = new Uint8Array(str.length * 2);\n    for (let i = 0; i < str.length; i++) {\n        const code = str.charCodeAt(i);\n        out[i * 2] = code & 0xff;\n        out[i * 2 + 1] = code >> 8;\n    }\n    return out;\n}\nfunction encodeASCII(str) {\n    return new Uint8Array([...str].map((ch) => ch.charCodeAt(0) & 0x7f));\n}\nfunction encodeLatin1(str) {\n    return new Uint8Array([...str].map((ch) => ch.charCodeAt(0) & 0xff));\n}\nfunction encodeWindows1252(str) {\n    return new Uint8Array([...str].map((ch) => {\n        const code = ch.charCodeAt(0);\n        if (code <= 0xff)\n            return code;\n        if (WINDOWS_1252_REVERSE[ch] !== undefined)\n            return WINDOWS_1252_REVERSE[ch];\n        return 0x3f; // '?'\n    }));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@borewit/text-codec/lib/index.js\n");

/***/ })

};
;