"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/peek-readable";
exports.ids = ["vendor-chunks/peek-readable"];
exports.modules = {

/***/ "(rsc)/./node_modules/peek-readable/lib/AbstractStreamReader.js":
/*!****************************************************************!*\
  !*** ./node_modules/peek-readable/lib/AbstractStreamReader.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AbstractStreamReader: () => (/* binding */ AbstractStreamReader)\n/* harmony export */ });\n/* harmony import */ var _EndOfStreamError_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./EndOfStreamError.js */ \"(rsc)/./node_modules/peek-readable/lib/EndOfStreamError.js\");\n\nclass AbstractStreamReader {\n    constructor() {\n        /**\n         * Maximum request length on read-stream operation\n         */\n        this.maxStreamReadSize = 1 * 1024 * 1024;\n        this.endOfStream = false;\n        /**\n         * Store peeked data\n         * @type {Array}\n         */\n        this.peekQueue = [];\n    }\n    async peek(uint8Array, offset, length) {\n        const bytesRead = await this.read(uint8Array, offset, length);\n        this.peekQueue.push(uint8Array.subarray(offset, offset + bytesRead)); // Put read data back to peek buffer\n        return bytesRead;\n    }\n    async read(buffer, offset, length) {\n        if (length === 0) {\n            return 0;\n        }\n        let bytesRead = this.readFromPeekBuffer(buffer, offset, length);\n        bytesRead += await this.readRemainderFromStream(buffer, offset + bytesRead, length - bytesRead);\n        if (bytesRead === 0) {\n            throw new _EndOfStreamError_js__WEBPACK_IMPORTED_MODULE_0__.EndOfStreamError();\n        }\n        return bytesRead;\n    }\n    /**\n     * Read chunk from stream\n     * @param buffer - Target Uint8Array (or Buffer) to store data read from stream in\n     * @param offset - Offset target\n     * @param length - Number of bytes to read\n     * @returns Number of bytes read\n     */\n    readFromPeekBuffer(buffer, offset, length) {\n        let remaining = length;\n        let bytesRead = 0;\n        // consume peeked data first\n        while (this.peekQueue.length > 0 && remaining > 0) {\n            const peekData = this.peekQueue.pop(); // Front of queue\n            if (!peekData)\n                throw new Error('peekData should be defined');\n            const lenCopy = Math.min(peekData.length, remaining);\n            buffer.set(peekData.subarray(0, lenCopy), offset + bytesRead);\n            bytesRead += lenCopy;\n            remaining -= lenCopy;\n            if (lenCopy < peekData.length) {\n                // remainder back to queue\n                this.peekQueue.push(peekData.subarray(lenCopy));\n            }\n        }\n        return bytesRead;\n    }\n    async readRemainderFromStream(buffer, offset, initialRemaining) {\n        let remaining = initialRemaining;\n        let bytesRead = 0;\n        // Continue reading from stream if required\n        while (remaining > 0 && !this.endOfStream) {\n            const reqLen = Math.min(remaining, this.maxStreamReadSize);\n            const chunkLen = await this.readFromStream(buffer, offset + bytesRead, reqLen);\n            if (chunkLen === 0)\n                break;\n            bytesRead += chunkLen;\n            remaining -= chunkLen;\n        }\n        return bytesRead;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/peek-readable/lib/AbstractStreamReader.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/peek-readable/lib/Deferred.js":
/*!****************************************************!*\
  !*** ./node_modules/peek-readable/lib/Deferred.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Deferred: () => (/* binding */ Deferred)\n/* harmony export */ });\nclass Deferred {\n    constructor() {\n        this.resolve = () => null;\n        this.reject = () => null;\n        this.promise = new Promise((resolve, reject) => {\n            this.reject = reject;\n            this.resolve = resolve;\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcGVlay1yZWFkYWJsZS9saWIvRGVmZXJyZWQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxXb3JrXFxQYXlsb2FkXFx0ZXN0LXBheWxvYWQtanNvblxcY29ycG9yYXRlLXdlYnNpdGVcXG5vZGVfbW9kdWxlc1xccGVlay1yZWFkYWJsZVxcbGliXFxEZWZlcnJlZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY2xhc3MgRGVmZXJyZWQge1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICB0aGlzLnJlc29sdmUgPSAoKSA9PiBudWxsO1xuICAgICAgICB0aGlzLnJlamVjdCA9ICgpID0+IG51bGw7XG4gICAgICAgIHRoaXMucHJvbWlzZSA9IG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHtcbiAgICAgICAgICAgIHRoaXMucmVqZWN0ID0gcmVqZWN0O1xuICAgICAgICAgICAgdGhpcy5yZXNvbHZlID0gcmVzb2x2ZTtcbiAgICAgICAgfSk7XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/peek-readable/lib/Deferred.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/peek-readable/lib/EndOfStreamError.js":
/*!************************************************************!*\
  !*** ./node_modules/peek-readable/lib/EndOfStreamError.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EndOfStreamError: () => (/* binding */ EndOfStreamError),\n/* harmony export */   defaultMessages: () => (/* binding */ defaultMessages)\n/* harmony export */ });\nconst defaultMessages = 'End-Of-Stream';\n/**\n * Thrown on read operation of the end of file or stream has been reached\n */\nclass EndOfStreamError extends Error {\n    constructor() {\n        super(defaultMessages);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcGVlay1yZWFkYWJsZS9saWIvRW5kT2ZTdHJlYW1FcnJvci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXFdvcmtcXFBheWxvYWRcXHRlc3QtcGF5bG9hZC1qc29uXFxjb3Jwb3JhdGUtd2Vic2l0ZVxcbm9kZV9tb2R1bGVzXFxwZWVrLXJlYWRhYmxlXFxsaWJcXEVuZE9mU3RyZWFtRXJyb3IuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGRlZmF1bHRNZXNzYWdlcyA9ICdFbmQtT2YtU3RyZWFtJztcbi8qKlxuICogVGhyb3duIG9uIHJlYWQgb3BlcmF0aW9uIG9mIHRoZSBlbmQgb2YgZmlsZSBvciBzdHJlYW0gaGFzIGJlZW4gcmVhY2hlZFxuICovXG5leHBvcnQgY2xhc3MgRW5kT2ZTdHJlYW1FcnJvciBleHRlbmRzIEVycm9yIHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgc3VwZXIoZGVmYXVsdE1lc3NhZ2VzKTtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/peek-readable/lib/EndOfStreamError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/peek-readable/lib/StreamReader.js":
/*!********************************************************!*\
  !*** ./node_modules/peek-readable/lib/StreamReader.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EndOfStreamError: () => (/* reexport safe */ _EndOfStreamError_js__WEBPACK_IMPORTED_MODULE_0__.EndOfStreamError),\n/* harmony export */   StreamReader: () => (/* binding */ StreamReader)\n/* harmony export */ });\n/* harmony import */ var _EndOfStreamError_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./EndOfStreamError.js */ \"(rsc)/./node_modules/peek-readable/lib/EndOfStreamError.js\");\n/* harmony import */ var _Deferred_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Deferred.js */ \"(rsc)/./node_modules/peek-readable/lib/Deferred.js\");\n/* harmony import */ var _AbstractStreamReader_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AbstractStreamReader.js */ \"(rsc)/./node_modules/peek-readable/lib/AbstractStreamReader.js\");\n\n\n\n\n/**\n * Node.js Readable Stream Reader\n * Ref: https://nodejs.org/api/stream.html#readable-streams\n */\nclass StreamReader extends _AbstractStreamReader_js__WEBPACK_IMPORTED_MODULE_2__.AbstractStreamReader {\n    constructor(s) {\n        super();\n        this.s = s;\n        /**\n         * Deferred used for postponed read request (as not data is yet available to read)\n         */\n        this.deferred = null;\n        if (!s.read || !s.once) {\n            throw new Error('Expected an instance of stream.Readable');\n        }\n        this.s.once('end', () => this.reject(new _EndOfStreamError_js__WEBPACK_IMPORTED_MODULE_0__.EndOfStreamError()));\n        this.s.once('error', err => this.reject(err));\n        this.s.once('close', () => this.reject(new Error('Stream closed')));\n    }\n    /**\n     * Read chunk from stream\n     * @param buffer Target Uint8Array (or Buffer) to store data read from stream in\n     * @param offset Offset target\n     * @param length Number of bytes to read\n     * @returns Number of bytes read\n     */\n    async readFromStream(buffer, offset, length) {\n        if (this.endOfStream) {\n            return 0;\n        }\n        const readBuffer = this.s.read(length);\n        if (readBuffer) {\n            buffer.set(readBuffer, offset);\n            return readBuffer.length;\n        }\n        const request = {\n            buffer,\n            offset,\n            length,\n            deferred: new _Deferred_js__WEBPACK_IMPORTED_MODULE_1__.Deferred()\n        };\n        this.deferred = request.deferred;\n        this.s.once('readable', () => {\n            this.readDeferred(request);\n        });\n        return request.deferred.promise;\n    }\n    /**\n     * Process deferred read request\n     * @param request Deferred read request\n     */\n    readDeferred(request) {\n        const readBuffer = this.s.read(request.length);\n        if (readBuffer) {\n            request.buffer.set(readBuffer, request.offset);\n            request.deferred.resolve(readBuffer.length);\n            this.deferred = null;\n        }\n        else {\n            this.s.once('readable', () => {\n                this.readDeferred(request);\n            });\n        }\n    }\n    reject(err) {\n        this.endOfStream = true;\n        if (this.deferred) {\n            this.deferred.reject(err);\n            this.deferred = null;\n        }\n    }\n    async abort() {\n        this.reject(new Error('abort'));\n    }\n    async close() {\n        return this.abort();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/peek-readable/lib/StreamReader.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/peek-readable/lib/WebStreamReader.js":
/*!***********************************************************!*\
  !*** ./node_modules/peek-readable/lib/WebStreamReader.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EndOfStreamError: () => (/* reexport safe */ _EndOfStreamError_js__WEBPACK_IMPORTED_MODULE_0__.EndOfStreamError),\n/* harmony export */   WebStreamReader: () => (/* binding */ WebStreamReader)\n/* harmony export */ });\n/* harmony import */ var _EndOfStreamError_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./EndOfStreamError.js */ \"(rsc)/./node_modules/peek-readable/lib/EndOfStreamError.js\");\n/* harmony import */ var _AbstractStreamReader_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./AbstractStreamReader.js */ \"(rsc)/./node_modules/peek-readable/lib/AbstractStreamReader.js\");\n\n\n\n/**\n * Read from a WebStream\n * Reference: https://nodejs.org/api/webstreams.html#class-readablestreambyobreader\n */\nclass WebStreamReader extends _AbstractStreamReader_js__WEBPACK_IMPORTED_MODULE_1__.AbstractStreamReader {\n    constructor(stream) {\n        super();\n        this.reader = stream.getReader({ mode: 'byob' });\n    }\n    async readFromStream(buffer, offset, length) {\n        if (this.endOfStream) {\n            throw new _EndOfStreamError_js__WEBPACK_IMPORTED_MODULE_0__.EndOfStreamError();\n        }\n        const result = await this.reader.read(new Uint8Array(length));\n        if (result.done) {\n            this.endOfStream = result.done;\n        }\n        if (result.value) {\n            buffer.set(result.value, offset);\n            return result.value.byteLength;\n        }\n        return 0;\n    }\n    abort() {\n        return this.reader.cancel(); // Signals a loss of interest in the stream by a consumer\n    }\n    async close() {\n        await this.abort();\n        this.reader.releaseLock();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/peek-readable/lib/WebStreamReader.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/peek-readable/lib/index.js":
/*!*************************************************!*\
  !*** ./node_modules/peek-readable/lib/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EndOfStreamError: () => (/* reexport safe */ _EndOfStreamError_js__WEBPACK_IMPORTED_MODULE_0__.EndOfStreamError),\n/* harmony export */   StreamReader: () => (/* reexport safe */ _StreamReader_js__WEBPACK_IMPORTED_MODULE_1__.StreamReader),\n/* harmony export */   WebStreamReader: () => (/* reexport safe */ _WebStreamReader_js__WEBPACK_IMPORTED_MODULE_2__.WebStreamReader)\n/* harmony export */ });\n/* harmony import */ var _EndOfStreamError_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./EndOfStreamError.js */ \"(rsc)/./node_modules/peek-readable/lib/EndOfStreamError.js\");\n/* harmony import */ var _StreamReader_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./StreamReader.js */ \"(rsc)/./node_modules/peek-readable/lib/StreamReader.js\");\n/* harmony import */ var _WebStreamReader_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./WebStreamReader.js */ \"(rsc)/./node_modules/peek-readable/lib/WebStreamReader.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcGVlay1yZWFkYWJsZS9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXlEO0FBQ1I7QUFDTSIsInNvdXJjZXMiOlsiRDpcXFdvcmtcXFBheWxvYWRcXHRlc3QtcGF5bG9hZC1qc29uXFxjb3Jwb3JhdGUtd2Vic2l0ZVxcbm9kZV9tb2R1bGVzXFxwZWVrLXJlYWRhYmxlXFxsaWJcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IEVuZE9mU3RyZWFtRXJyb3IgfSBmcm9tICcuL0VuZE9mU3RyZWFtRXJyb3IuanMnO1xuZXhwb3J0IHsgU3RyZWFtUmVhZGVyIH0gZnJvbSAnLi9TdHJlYW1SZWFkZXIuanMnO1xuZXhwb3J0IHsgV2ViU3RyZWFtUmVhZGVyIH0gZnJvbSAnLi9XZWJTdHJlYW1SZWFkZXIuanMnO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/peek-readable/lib/index.js\n");

/***/ })

};
;