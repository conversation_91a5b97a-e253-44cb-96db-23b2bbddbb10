import { withPayload } from '@payloadcms/next/withPayload'

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Основные настройки Next.js
  experimental: {
    reactCompiler: false,
  },

  // Настройки изображений
  images: {
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '3000',
        pathname: '/media/**',
      },
      {
        protocol: 'https',
        hostname: 'your-domain.com',
        pathname: '/media/**',
      },
    ],
  },

  // Настройки для статических файлов
  async rewrites() {
    return [
      {
        source: '/media/:path*',
        destination: '/api/media/:path*',
      },
    ]
  },

  // Настройки для редиректов
  async redirects() {
    return [
      // Убираем автоматический редирект, чтобы пользователь мог войти в систему
    ]
  },

  // Настройки для заголовков безопасности
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ]
  },

  // Настройки для сжатия
  compress: true,

  // Настройки для PoweredByHeader
  poweredByHeader: false,

  // Настройки для трейлинг слешей
  trailingSlash: false,

  // Настройки для строгого режима React
  reactStrictMode: true,

  // Настройки для TypeScript
  typescript: {
    ignoreBuildErrors: false,
  },

  // Настройки для ESLint
  eslint: {
    ignoreDuringBuilds: false,
  },
}

export default withPayload(nextConfig)
