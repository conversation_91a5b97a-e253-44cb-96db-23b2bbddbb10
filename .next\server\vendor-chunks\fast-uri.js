"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fast-uri";
exports.ids = ["vendor-chunks/fast-uri"];
exports.modules = {

/***/ "(rsc)/./node_modules/fast-uri/index.js":
/*!****************************************!*\
  !*** ./node_modules/fast-uri/index.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { normalizeIPv6, removeDotSegments, recomposeAuthority, normalizeComponentEncoding, isIPv4, nonSimpleDomain } = __webpack_require__(/*! ./lib/utils */ \"(rsc)/./node_modules/fast-uri/lib/utils.js\")\nconst { SCHEMES, getSchemeHandler } = __webpack_require__(/*! ./lib/schemes */ \"(rsc)/./node_modules/fast-uri/lib/schemes.js\")\n\n/**\n * @template {import('./types/index').URIComponent|string} T\n * @param {T} uri\n * @param {import('./types/index').Options} [options]\n * @returns {T}\n */\nfunction normalize (uri, options) {\n  if (typeof uri === 'string') {\n    uri = /** @type {T} */ (serialize(parse(uri, options), options))\n  } else if (typeof uri === 'object') {\n    uri = /** @type {T} */ (parse(serialize(uri, options), options))\n  }\n  return uri\n}\n\n/**\n * @param {string} baseURI\n * @param {string} relativeURI\n * @param {import('./types/index').Options} [options]\n * @returns {string}\n */\nfunction resolve (baseURI, relativeURI, options) {\n  const schemelessOptions = options ? Object.assign({ scheme: 'null' }, options) : { scheme: 'null' }\n  const resolved = resolveComponent(parse(baseURI, schemelessOptions), parse(relativeURI, schemelessOptions), schemelessOptions, true)\n  schemelessOptions.skipEscape = true\n  return serialize(resolved, schemelessOptions)\n}\n\n/**\n * @param {import ('./types/index').URIComponent} base\n * @param {import ('./types/index').URIComponent} relative\n * @param {import('./types/index').Options} [options]\n * @param {boolean} [skipNormalization=false]\n * @returns {import ('./types/index').URIComponent}\n */\nfunction resolveComponent (base, relative, options, skipNormalization) {\n  /** @type {import('./types/index').URIComponent} */\n  const target = {}\n  if (!skipNormalization) {\n    base = parse(serialize(base, options), options) // normalize base component\n    relative = parse(serialize(relative, options), options) // normalize relative component\n  }\n  options = options || {}\n\n  if (!options.tolerant && relative.scheme) {\n    target.scheme = relative.scheme\n    // target.authority = relative.authority;\n    target.userinfo = relative.userinfo\n    target.host = relative.host\n    target.port = relative.port\n    target.path = removeDotSegments(relative.path || '')\n    target.query = relative.query\n  } else {\n    if (relative.userinfo !== undefined || relative.host !== undefined || relative.port !== undefined) {\n      // target.authority = relative.authority;\n      target.userinfo = relative.userinfo\n      target.host = relative.host\n      target.port = relative.port\n      target.path = removeDotSegments(relative.path || '')\n      target.query = relative.query\n    } else {\n      if (!relative.path) {\n        target.path = base.path\n        if (relative.query !== undefined) {\n          target.query = relative.query\n        } else {\n          target.query = base.query\n        }\n      } else {\n        if (relative.path[0] === '/') {\n          target.path = removeDotSegments(relative.path)\n        } else {\n          if ((base.userinfo !== undefined || base.host !== undefined || base.port !== undefined) && !base.path) {\n            target.path = '/' + relative.path\n          } else if (!base.path) {\n            target.path = relative.path\n          } else {\n            target.path = base.path.slice(0, base.path.lastIndexOf('/') + 1) + relative.path\n          }\n          target.path = removeDotSegments(target.path)\n        }\n        target.query = relative.query\n      }\n      // target.authority = base.authority;\n      target.userinfo = base.userinfo\n      target.host = base.host\n      target.port = base.port\n    }\n    target.scheme = base.scheme\n  }\n\n  target.fragment = relative.fragment\n\n  return target\n}\n\n/**\n * @param {import ('./types/index').URIComponent|string} uriA\n * @param {import ('./types/index').URIComponent|string} uriB\n * @param {import ('./types/index').Options} options\n * @returns {boolean}\n */\nfunction equal (uriA, uriB, options) {\n  if (typeof uriA === 'string') {\n    uriA = unescape(uriA)\n    uriA = serialize(normalizeComponentEncoding(parse(uriA, options), true), { ...options, skipEscape: true })\n  } else if (typeof uriA === 'object') {\n    uriA = serialize(normalizeComponentEncoding(uriA, true), { ...options, skipEscape: true })\n  }\n\n  if (typeof uriB === 'string') {\n    uriB = unescape(uriB)\n    uriB = serialize(normalizeComponentEncoding(parse(uriB, options), true), { ...options, skipEscape: true })\n  } else if (typeof uriB === 'object') {\n    uriB = serialize(normalizeComponentEncoding(uriB, true), { ...options, skipEscape: true })\n  }\n\n  return uriA.toLowerCase() === uriB.toLowerCase()\n}\n\n/**\n * @param {Readonly<import('./types/index').URIComponent>} cmpts\n * @param {import('./types/index').Options} [opts]\n * @returns {string}\n */\nfunction serialize (cmpts, opts) {\n  const component = {\n    host: cmpts.host,\n    scheme: cmpts.scheme,\n    userinfo: cmpts.userinfo,\n    port: cmpts.port,\n    path: cmpts.path,\n    query: cmpts.query,\n    nid: cmpts.nid,\n    nss: cmpts.nss,\n    uuid: cmpts.uuid,\n    fragment: cmpts.fragment,\n    reference: cmpts.reference,\n    resourceName: cmpts.resourceName,\n    secure: cmpts.secure,\n    error: ''\n  }\n  const options = Object.assign({}, opts)\n  const uriTokens = []\n\n  // find scheme handler\n  const schemeHandler = getSchemeHandler(options.scheme || component.scheme)\n\n  // perform scheme specific serialization\n  if (schemeHandler && schemeHandler.serialize) schemeHandler.serialize(component, options)\n\n  if (component.path !== undefined) {\n    if (!options.skipEscape) {\n      component.path = escape(component.path)\n\n      if (component.scheme !== undefined) {\n        component.path = component.path.split('%3A').join(':')\n      }\n    } else {\n      component.path = unescape(component.path)\n    }\n  }\n\n  if (options.reference !== 'suffix' && component.scheme) {\n    uriTokens.push(component.scheme, ':')\n  }\n\n  const authority = recomposeAuthority(component)\n  if (authority !== undefined) {\n    if (options.reference !== 'suffix') {\n      uriTokens.push('//')\n    }\n\n    uriTokens.push(authority)\n\n    if (component.path && component.path[0] !== '/') {\n      uriTokens.push('/')\n    }\n  }\n  if (component.path !== undefined) {\n    let s = component.path\n\n    if (!options.absolutePath && (!schemeHandler || !schemeHandler.absolutePath)) {\n      s = removeDotSegments(s)\n    }\n\n    if (\n      authority === undefined &&\n      s[0] === '/' &&\n      s[1] === '/'\n    ) {\n      // don't allow the path to start with \"//\"\n      s = '/%2F' + s.slice(2)\n    }\n\n    uriTokens.push(s)\n  }\n\n  if (component.query !== undefined) {\n    uriTokens.push('?', component.query)\n  }\n\n  if (component.fragment !== undefined) {\n    uriTokens.push('#', component.fragment)\n  }\n  return uriTokens.join('')\n}\n\nconst URI_PARSE = /^(?:([^#/:?]+):)?(?:\\/\\/((?:([^#/?@]*)@)?(\\[[^#/?\\]]+\\]|[^#/:?]*)(?::(\\d*))?))?([^#?]*)(?:\\?([^#]*))?(?:#((?:.|[\\n\\r])*))?/u\n\n/**\n * @param {string} uri\n * @param {import('./types/index').Options} [opts]\n * @returns\n */\nfunction parse (uri, opts) {\n  const options = Object.assign({}, opts)\n  /** @type {import('./types/index').URIComponent} */\n  const parsed = {\n    scheme: undefined,\n    userinfo: undefined,\n    host: '',\n    port: undefined,\n    path: '',\n    query: undefined,\n    fragment: undefined\n  }\n\n  let isIP = false\n  if (options.reference === 'suffix') {\n    if (options.scheme) {\n      uri = options.scheme + ':' + uri\n    } else {\n      uri = '//' + uri\n    }\n  }\n\n  const matches = uri.match(URI_PARSE)\n\n  if (matches) {\n    // store each component\n    parsed.scheme = matches[1]\n    parsed.userinfo = matches[3]\n    parsed.host = matches[4]\n    parsed.port = parseInt(matches[5], 10)\n    parsed.path = matches[6] || ''\n    parsed.query = matches[7]\n    parsed.fragment = matches[8]\n\n    // fix port number\n    if (isNaN(parsed.port)) {\n      parsed.port = matches[5]\n    }\n    if (parsed.host) {\n      const ipv4result = isIPv4(parsed.host)\n      if (ipv4result === false) {\n        const ipv6result = normalizeIPv6(parsed.host)\n        parsed.host = ipv6result.host.toLowerCase()\n        isIP = ipv6result.isIPV6\n      } else {\n        isIP = true\n      }\n    }\n    if (parsed.scheme === undefined && parsed.userinfo === undefined && parsed.host === undefined && parsed.port === undefined && parsed.query === undefined && !parsed.path) {\n      parsed.reference = 'same-document'\n    } else if (parsed.scheme === undefined) {\n      parsed.reference = 'relative'\n    } else if (parsed.fragment === undefined) {\n      parsed.reference = 'absolute'\n    } else {\n      parsed.reference = 'uri'\n    }\n\n    // check for reference errors\n    if (options.reference && options.reference !== 'suffix' && options.reference !== parsed.reference) {\n      parsed.error = parsed.error || 'URI is not a ' + options.reference + ' reference.'\n    }\n\n    // find scheme handler\n    const schemeHandler = getSchemeHandler(options.scheme || parsed.scheme)\n\n    // check if scheme can't handle IRIs\n    if (!options.unicodeSupport && (!schemeHandler || !schemeHandler.unicodeSupport)) {\n      // if host component is a domain name\n      if (parsed.host && (options.domainHost || (schemeHandler && schemeHandler.domainHost)) && isIP === false && nonSimpleDomain(parsed.host)) {\n        // convert Unicode IDN -> ASCII IDN\n        try {\n          parsed.host = URL.domainToASCII(parsed.host.toLowerCase())\n        } catch (e) {\n          parsed.error = parsed.error || \"Host's domain name can not be converted to ASCII: \" + e\n        }\n      }\n      // convert IRI -> URI\n    }\n\n    if (!schemeHandler || (schemeHandler && !schemeHandler.skipNormalize)) {\n      if (uri.indexOf('%') !== -1) {\n        if (parsed.scheme !== undefined) {\n          parsed.scheme = unescape(parsed.scheme)\n        }\n        if (parsed.host !== undefined) {\n          parsed.host = unescape(parsed.host)\n        }\n      }\n      if (parsed.path) {\n        parsed.path = escape(unescape(parsed.path))\n      }\n      if (parsed.fragment) {\n        parsed.fragment = encodeURI(decodeURIComponent(parsed.fragment))\n      }\n    }\n\n    // perform scheme specific parsing\n    if (schemeHandler && schemeHandler.parse) {\n      schemeHandler.parse(parsed, options)\n    }\n  } else {\n    parsed.error = parsed.error || 'URI can not be parsed.'\n  }\n  return parsed\n}\n\nconst fastUri = {\n  SCHEMES,\n  normalize,\n  resolve,\n  resolveComponent,\n  equal,\n  serialize,\n  parse\n}\n\nmodule.exports = fastUri\nmodule.exports[\"default\"] = fastUri\nmodule.exports.fastUri = fastUri\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fast-uri/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fast-uri/lib/schemes.js":
/*!**********************************************!*\
  !*** ./node_modules/fast-uri/lib/schemes.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { isUUID } = __webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/fast-uri/lib/utils.js\")\nconst URN_REG = /([\\da-z][\\d\\-a-z]{0,31}):((?:[\\w!$'()*+,\\-.:;=@]|%[\\da-f]{2})+)/iu\n\nconst supportedSchemeNames = /** @type {const} */ (['http', 'https', 'ws',\n  'wss', 'urn', 'urn:uuid'])\n\n/** @typedef {supportedSchemeNames[number]} SchemeName */\n\n/**\n * @param {string} name\n * @returns {name is SchemeName}\n */\nfunction isValidSchemeName (name) {\n  return supportedSchemeNames.indexOf(/** @type {*} */ (name)) !== -1\n}\n\n/**\n * @callback SchemeFn\n * @param {import('../types/index').URIComponent} component\n * @param {import('../types/index').Options} options\n * @returns {import('../types/index').URIComponent}\n */\n\n/**\n * @typedef {Object} SchemeHandler\n * @property {SchemeName} scheme - The scheme name.\n * @property {boolean} [domainHost] - Indicates if the scheme supports domain hosts.\n * @property {SchemeFn} parse - Function to parse the URI component for this scheme.\n * @property {SchemeFn} serialize - Function to serialize the URI component for this scheme.\n * @property {boolean} [skipNormalize] - Indicates if normalization should be skipped for this scheme.\n * @property {boolean} [absolutePath] - Indicates if the scheme uses absolute paths.\n * @property {boolean} [unicodeSupport] - Indicates if the scheme supports Unicode.\n */\n\n/**\n * @param {import('../types/index').URIComponent} wsComponent\n * @returns {boolean}\n */\nfunction wsIsSecure (wsComponent) {\n  if (wsComponent.secure === true) {\n    return true\n  } else if (wsComponent.secure === false) {\n    return false\n  } else if (wsComponent.scheme) {\n    return (\n      wsComponent.scheme.length === 3 &&\n      (wsComponent.scheme[0] === 'w' || wsComponent.scheme[0] === 'W') &&\n      (wsComponent.scheme[1] === 's' || wsComponent.scheme[1] === 'S') &&\n      (wsComponent.scheme[2] === 's' || wsComponent.scheme[2] === 'S')\n    )\n  } else {\n    return false\n  }\n}\n\n/** @type {SchemeFn} */\nfunction httpParse (component) {\n  if (!component.host) {\n    component.error = component.error || 'HTTP URIs must have a host.'\n  }\n\n  return component\n}\n\n/** @type {SchemeFn} */\nfunction httpSerialize (component) {\n  const secure = String(component.scheme).toLowerCase() === 'https'\n\n  // normalize the default port\n  if (component.port === (secure ? 443 : 80) || component.port === '') {\n    component.port = undefined\n  }\n\n  // normalize the empty path\n  if (!component.path) {\n    component.path = '/'\n  }\n\n  // NOTE: We do not parse query strings for HTTP URIs\n  // as WWW Form Url Encoded query strings are part of the HTML4+ spec,\n  // and not the HTTP spec.\n\n  return component\n}\n\n/** @type {SchemeFn} */\nfunction wsParse (wsComponent) {\n// indicate if the secure flag is set\n  wsComponent.secure = wsIsSecure(wsComponent)\n\n  // construct resouce name\n  wsComponent.resourceName = (wsComponent.path || '/') + (wsComponent.query ? '?' + wsComponent.query : '')\n  wsComponent.path = undefined\n  wsComponent.query = undefined\n\n  return wsComponent\n}\n\n/** @type {SchemeFn} */\nfunction wsSerialize (wsComponent) {\n// normalize the default port\n  if (wsComponent.port === (wsIsSecure(wsComponent) ? 443 : 80) || wsComponent.port === '') {\n    wsComponent.port = undefined\n  }\n\n  // ensure scheme matches secure flag\n  if (typeof wsComponent.secure === 'boolean') {\n    wsComponent.scheme = (wsComponent.secure ? 'wss' : 'ws')\n    wsComponent.secure = undefined\n  }\n\n  // reconstruct path from resource name\n  if (wsComponent.resourceName) {\n    const [path, query] = wsComponent.resourceName.split('?')\n    wsComponent.path = (path && path !== '/' ? path : undefined)\n    wsComponent.query = query\n    wsComponent.resourceName = undefined\n  }\n\n  // forbid fragment component\n  wsComponent.fragment = undefined\n\n  return wsComponent\n}\n\n/** @type {SchemeFn} */\nfunction urnParse (urnComponent, options) {\n  if (!urnComponent.path) {\n    urnComponent.error = 'URN can not be parsed'\n    return urnComponent\n  }\n  const matches = urnComponent.path.match(URN_REG)\n  if (matches) {\n    const scheme = options.scheme || urnComponent.scheme || 'urn'\n    urnComponent.nid = matches[1].toLowerCase()\n    urnComponent.nss = matches[2]\n    const urnScheme = `${scheme}:${options.nid || urnComponent.nid}`\n    const schemeHandler = getSchemeHandler(urnScheme)\n    urnComponent.path = undefined\n\n    if (schemeHandler) {\n      urnComponent = schemeHandler.parse(urnComponent, options)\n    }\n  } else {\n    urnComponent.error = urnComponent.error || 'URN can not be parsed.'\n  }\n\n  return urnComponent\n}\n\n/** @type {SchemeFn} */\nfunction urnSerialize (urnComponent, options) {\n  if (urnComponent.nid === undefined) {\n    throw new Error('URN without nid cannot be serialized')\n  }\n  const scheme = options.scheme || urnComponent.scheme || 'urn'\n  const nid = urnComponent.nid.toLowerCase()\n  const urnScheme = `${scheme}:${options.nid || nid}`\n  const schemeHandler = getSchemeHandler(urnScheme)\n\n  if (schemeHandler) {\n    urnComponent = schemeHandler.serialize(urnComponent, options)\n  }\n\n  const uriComponent = urnComponent\n  const nss = urnComponent.nss\n  uriComponent.path = `${nid || options.nid}:${nss}`\n\n  options.skipEscape = true\n  return uriComponent\n}\n\n/** @type {SchemeFn} */\nfunction urnuuidParse (urnComponent, options) {\n  const uuidComponent = urnComponent\n  uuidComponent.uuid = uuidComponent.nss\n  uuidComponent.nss = undefined\n\n  if (!options.tolerant && (!uuidComponent.uuid || !isUUID(uuidComponent.uuid))) {\n    uuidComponent.error = uuidComponent.error || 'UUID is not valid.'\n  }\n\n  return uuidComponent\n}\n\n/** @type {SchemeFn} */\nfunction urnuuidSerialize (uuidComponent) {\n  const urnComponent = uuidComponent\n  // normalize UUID\n  urnComponent.nss = (uuidComponent.uuid || '').toLowerCase()\n  return urnComponent\n}\n\nconst http = /** @type {SchemeHandler} */ ({\n  scheme: 'http',\n  domainHost: true,\n  parse: httpParse,\n  serialize: httpSerialize\n})\n\nconst https = /** @type {SchemeHandler} */ ({\n  scheme: 'https',\n  domainHost: http.domainHost,\n  parse: httpParse,\n  serialize: httpSerialize\n})\n\nconst ws = /** @type {SchemeHandler} */ ({\n  scheme: 'ws',\n  domainHost: true,\n  parse: wsParse,\n  serialize: wsSerialize\n})\n\nconst wss = /** @type {SchemeHandler} */ ({\n  scheme: 'wss',\n  domainHost: ws.domainHost,\n  parse: ws.parse,\n  serialize: ws.serialize\n})\n\nconst urn = /** @type {SchemeHandler} */ ({\n  scheme: 'urn',\n  parse: urnParse,\n  serialize: urnSerialize,\n  skipNormalize: true\n})\n\nconst urnuuid = /** @type {SchemeHandler} */ ({\n  scheme: 'urn:uuid',\n  parse: urnuuidParse,\n  serialize: urnuuidSerialize,\n  skipNormalize: true\n})\n\nconst SCHEMES = /** @type {Record<SchemeName, SchemeHandler>} */ ({\n  http,\n  https,\n  ws,\n  wss,\n  urn,\n  'urn:uuid': urnuuid\n})\n\nObject.setPrototypeOf(SCHEMES, null)\n\n/**\n * @param {string|undefined} scheme\n * @returns {SchemeHandler|undefined}\n */\nfunction getSchemeHandler (scheme) {\n  return (\n    scheme && (\n      SCHEMES[/** @type {SchemeName} */ (scheme)] ||\n      SCHEMES[/** @type {SchemeName} */(scheme.toLowerCase())])\n  ) ||\n    undefined\n}\n\nmodule.exports = {\n  wsIsSecure,\n  SCHEMES,\n  isValidSchemeName,\n  getSchemeHandler,\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fast-uri/lib/schemes.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fast-uri/lib/utils.js":
/*!********************************************!*\
  !*** ./node_modules/fast-uri/lib/utils.js ***!
  \********************************************/
/***/ ((module) => {

eval("\n\n/** @type {(value: string) => boolean} */\nconst isUUID = RegExp.prototype.test.bind(/^[\\da-f]{8}-[\\da-f]{4}-[\\da-f]{4}-[\\da-f]{4}-[\\da-f]{12}$/iu)\n\n/** @type {(value: string) => boolean} */\nconst isIPv4 = RegExp.prototype.test.bind(/^(?:(?:25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)\\.){3}(?:25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)$/u)\n\n/**\n * @param {Array<string>} input\n * @returns {string}\n */\nfunction stringArrayToHexStripped (input) {\n  let acc = ''\n  let code = 0\n  let i = 0\n\n  for (i = 0; i < input.length; i++) {\n    code = input[i].charCodeAt(0)\n    if (code === 48) {\n      continue\n    }\n    if (!((code >= 48 && code <= 57) || (code >= 65 && code <= 70) || (code >= 97 && code <= 102))) {\n      return ''\n    }\n    acc += input[i]\n    break\n  }\n\n  for (i += 1; i < input.length; i++) {\n    code = input[i].charCodeAt(0)\n    if (!((code >= 48 && code <= 57) || (code >= 65 && code <= 70) || (code >= 97 && code <= 102))) {\n      return ''\n    }\n    acc += input[i]\n  }\n  return acc\n}\n\n/**\n * @typedef {Object} GetIPV6Result\n * @property {boolean} error - Indicates if there was an error parsing the IPv6 address.\n * @property {string} address - The parsed IPv6 address.\n * @property {string} [zone] - The zone identifier, if present.\n */\n\n/**\n * @param {string} value\n * @returns {boolean}\n */\nconst nonSimpleDomain = RegExp.prototype.test.bind(/[^!\"$&'()*+,\\-.;=_`a-z{}~]/u)\n\n/**\n * @param {Array<string>} buffer\n * @returns {boolean}\n */\nfunction consumeIsZone (buffer) {\n  buffer.length = 0\n  return true\n}\n\n/**\n * @param {Array<string>} buffer\n * @param {Array<string>} address\n * @param {GetIPV6Result} output\n * @returns {boolean}\n */\nfunction consumeHextets (buffer, address, output) {\n  if (buffer.length) {\n    const hex = stringArrayToHexStripped(buffer)\n    if (hex !== '') {\n      address.push(hex)\n    } else {\n      output.error = true\n      return false\n    }\n    buffer.length = 0\n  }\n  return true\n}\n\n/**\n * @param {string} input\n * @returns {GetIPV6Result}\n */\nfunction getIPV6 (input) {\n  let tokenCount = 0\n  const output = { error: false, address: '', zone: '' }\n  /** @type {Array<string>} */\n  const address = []\n  /** @type {Array<string>} */\n  const buffer = []\n  let endipv6Encountered = false\n  let endIpv6 = false\n\n  let consume = consumeHextets\n\n  for (let i = 0; i < input.length; i++) {\n    const cursor = input[i]\n    if (cursor === '[' || cursor === ']') { continue }\n    if (cursor === ':') {\n      if (endipv6Encountered === true) {\n        endIpv6 = true\n      }\n      if (!consume(buffer, address, output)) { break }\n      if (++tokenCount > 7) {\n        // not valid\n        output.error = true\n        break\n      }\n      if (i > 0 && input[i - 1] === ':') {\n        endipv6Encountered = true\n      }\n      address.push(':')\n      continue\n    } else if (cursor === '%') {\n      if (!consume(buffer, address, output)) { break }\n      // switch to zone detection\n      consume = consumeIsZone\n    } else {\n      buffer.push(cursor)\n      continue\n    }\n  }\n  if (buffer.length) {\n    if (consume === consumeIsZone) {\n      output.zone = buffer.join('')\n    } else if (endIpv6) {\n      address.push(buffer.join(''))\n    } else {\n      address.push(stringArrayToHexStripped(buffer))\n    }\n  }\n  output.address = address.join('')\n  return output\n}\n\n/**\n * @typedef {Object} NormalizeIPv6Result\n * @property {string} host - The normalized host.\n * @property {string} [escapedHost] - The escaped host.\n * @property {boolean} isIPV6 - Indicates if the host is an IPv6 address.\n */\n\n/**\n * @param {string} host\n * @returns {NormalizeIPv6Result}\n */\nfunction normalizeIPv6 (host) {\n  if (findToken(host, ':') < 2) { return { host, isIPV6: false } }\n  const ipv6 = getIPV6(host)\n\n  if (!ipv6.error) {\n    let newHost = ipv6.address\n    let escapedHost = ipv6.address\n    if (ipv6.zone) {\n      newHost += '%' + ipv6.zone\n      escapedHost += '%25' + ipv6.zone\n    }\n    return { host: newHost, isIPV6: true, escapedHost }\n  } else {\n    return { host, isIPV6: false }\n  }\n}\n\n/**\n * @param {string} str\n * @param {string} token\n * @returns {number}\n */\nfunction findToken (str, token) {\n  let ind = 0\n  for (let i = 0; i < str.length; i++) {\n    if (str[i] === token) ind++\n  }\n  return ind\n}\n\n/**\n * @param {string} path\n * @returns {string}\n *\n * @see https://datatracker.ietf.org/doc/html/rfc3986#section-5.2.4\n */\nfunction removeDotSegments (path) {\n  let input = path\n  const output = []\n  let nextSlash = -1\n  let len = 0\n\n  // eslint-disable-next-line no-cond-assign\n  while (len = input.length) {\n    if (len === 1) {\n      if (input === '.') {\n        break\n      } else if (input === '/') {\n        output.push('/')\n        break\n      } else {\n        output.push(input)\n        break\n      }\n    } else if (len === 2) {\n      if (input[0] === '.') {\n        if (input[1] === '.') {\n          break\n        } else if (input[1] === '/') {\n          input = input.slice(2)\n          continue\n        }\n      } else if (input[0] === '/') {\n        if (input[1] === '.' || input[1] === '/') {\n          output.push('/')\n          break\n        }\n      }\n    } else if (len === 3) {\n      if (input === '/..') {\n        if (output.length !== 0) {\n          output.pop()\n        }\n        output.push('/')\n        break\n      }\n    }\n    if (input[0] === '.') {\n      if (input[1] === '.') {\n        if (input[2] === '/') {\n          input = input.slice(3)\n          continue\n        }\n      } else if (input[1] === '/') {\n        input = input.slice(2)\n        continue\n      }\n    } else if (input[0] === '/') {\n      if (input[1] === '.') {\n        if (input[2] === '/') {\n          input = input.slice(2)\n          continue\n        } else if (input[2] === '.') {\n          if (input[3] === '/') {\n            input = input.slice(3)\n            if (output.length !== 0) {\n              output.pop()\n            }\n            continue\n          }\n        }\n      }\n    }\n\n    // Rule 2E: Move normal path segment to output\n    if ((nextSlash = input.indexOf('/', 1)) === -1) {\n      output.push(input)\n      break\n    } else {\n      output.push(input.slice(0, nextSlash))\n      input = input.slice(nextSlash)\n    }\n  }\n\n  return output.join('')\n}\n\n/**\n * @param {import('../types/index').URIComponent} component\n * @param {boolean} esc\n * @returns {import('../types/index').URIComponent}\n */\nfunction normalizeComponentEncoding (component, esc) {\n  const func = esc !== true ? escape : unescape\n  if (component.scheme !== undefined) {\n    component.scheme = func(component.scheme)\n  }\n  if (component.userinfo !== undefined) {\n    component.userinfo = func(component.userinfo)\n  }\n  if (component.host !== undefined) {\n    component.host = func(component.host)\n  }\n  if (component.path !== undefined) {\n    component.path = func(component.path)\n  }\n  if (component.query !== undefined) {\n    component.query = func(component.query)\n  }\n  if (component.fragment !== undefined) {\n    component.fragment = func(component.fragment)\n  }\n  return component\n}\n\n/**\n * @param {import('../types/index').URIComponent} component\n * @returns {string|undefined}\n */\nfunction recomposeAuthority (component) {\n  const uriTokens = []\n\n  if (component.userinfo !== undefined) {\n    uriTokens.push(component.userinfo)\n    uriTokens.push('@')\n  }\n\n  if (component.host !== undefined) {\n    let host = unescape(component.host)\n    if (!isIPv4(host)) {\n      const ipV6res = normalizeIPv6(host)\n      if (ipV6res.isIPV6 === true) {\n        host = `[${ipV6res.escapedHost}]`\n      } else {\n        host = component.host\n      }\n    }\n    uriTokens.push(host)\n  }\n\n  if (typeof component.port === 'number' || typeof component.port === 'string') {\n    uriTokens.push(':')\n    uriTokens.push(String(component.port))\n  }\n\n  return uriTokens.length ? uriTokens.join('') : undefined\n};\n\nmodule.exports = {\n  nonSimpleDomain,\n  recomposeAuthority,\n  normalizeComponentEncoding,\n  removeDotSegments,\n  isIPv4,\n  isUUID,\n  normalizeIPv6,\n  stringArrayToHexStripped\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fast-uri/lib/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fast-uri/index.js":
/*!****************************************!*\
  !*** ./node_modules/fast-uri/index.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { normalizeIPv6, removeDotSegments, recomposeAuthority, normalizeComponentEncoding, isIPv4, nonSimpleDomain } = __webpack_require__(/*! ./lib/utils */ \"(ssr)/./node_modules/fast-uri/lib/utils.js\")\nconst { SCHEMES, getSchemeHandler } = __webpack_require__(/*! ./lib/schemes */ \"(ssr)/./node_modules/fast-uri/lib/schemes.js\")\n\n/**\n * @template {import('./types/index').URIComponent|string} T\n * @param {T} uri\n * @param {import('./types/index').Options} [options]\n * @returns {T}\n */\nfunction normalize (uri, options) {\n  if (typeof uri === 'string') {\n    uri = /** @type {T} */ (serialize(parse(uri, options), options))\n  } else if (typeof uri === 'object') {\n    uri = /** @type {T} */ (parse(serialize(uri, options), options))\n  }\n  return uri\n}\n\n/**\n * @param {string} baseURI\n * @param {string} relativeURI\n * @param {import('./types/index').Options} [options]\n * @returns {string}\n */\nfunction resolve (baseURI, relativeURI, options) {\n  const schemelessOptions = options ? Object.assign({ scheme: 'null' }, options) : { scheme: 'null' }\n  const resolved = resolveComponent(parse(baseURI, schemelessOptions), parse(relativeURI, schemelessOptions), schemelessOptions, true)\n  schemelessOptions.skipEscape = true\n  return serialize(resolved, schemelessOptions)\n}\n\n/**\n * @param {import ('./types/index').URIComponent} base\n * @param {import ('./types/index').URIComponent} relative\n * @param {import('./types/index').Options} [options]\n * @param {boolean} [skipNormalization=false]\n * @returns {import ('./types/index').URIComponent}\n */\nfunction resolveComponent (base, relative, options, skipNormalization) {\n  /** @type {import('./types/index').URIComponent} */\n  const target = {}\n  if (!skipNormalization) {\n    base = parse(serialize(base, options), options) // normalize base component\n    relative = parse(serialize(relative, options), options) // normalize relative component\n  }\n  options = options || {}\n\n  if (!options.tolerant && relative.scheme) {\n    target.scheme = relative.scheme\n    // target.authority = relative.authority;\n    target.userinfo = relative.userinfo\n    target.host = relative.host\n    target.port = relative.port\n    target.path = removeDotSegments(relative.path || '')\n    target.query = relative.query\n  } else {\n    if (relative.userinfo !== undefined || relative.host !== undefined || relative.port !== undefined) {\n      // target.authority = relative.authority;\n      target.userinfo = relative.userinfo\n      target.host = relative.host\n      target.port = relative.port\n      target.path = removeDotSegments(relative.path || '')\n      target.query = relative.query\n    } else {\n      if (!relative.path) {\n        target.path = base.path\n        if (relative.query !== undefined) {\n          target.query = relative.query\n        } else {\n          target.query = base.query\n        }\n      } else {\n        if (relative.path[0] === '/') {\n          target.path = removeDotSegments(relative.path)\n        } else {\n          if ((base.userinfo !== undefined || base.host !== undefined || base.port !== undefined) && !base.path) {\n            target.path = '/' + relative.path\n          } else if (!base.path) {\n            target.path = relative.path\n          } else {\n            target.path = base.path.slice(0, base.path.lastIndexOf('/') + 1) + relative.path\n          }\n          target.path = removeDotSegments(target.path)\n        }\n        target.query = relative.query\n      }\n      // target.authority = base.authority;\n      target.userinfo = base.userinfo\n      target.host = base.host\n      target.port = base.port\n    }\n    target.scheme = base.scheme\n  }\n\n  target.fragment = relative.fragment\n\n  return target\n}\n\n/**\n * @param {import ('./types/index').URIComponent|string} uriA\n * @param {import ('./types/index').URIComponent|string} uriB\n * @param {import ('./types/index').Options} options\n * @returns {boolean}\n */\nfunction equal (uriA, uriB, options) {\n  if (typeof uriA === 'string') {\n    uriA = unescape(uriA)\n    uriA = serialize(normalizeComponentEncoding(parse(uriA, options), true), { ...options, skipEscape: true })\n  } else if (typeof uriA === 'object') {\n    uriA = serialize(normalizeComponentEncoding(uriA, true), { ...options, skipEscape: true })\n  }\n\n  if (typeof uriB === 'string') {\n    uriB = unescape(uriB)\n    uriB = serialize(normalizeComponentEncoding(parse(uriB, options), true), { ...options, skipEscape: true })\n  } else if (typeof uriB === 'object') {\n    uriB = serialize(normalizeComponentEncoding(uriB, true), { ...options, skipEscape: true })\n  }\n\n  return uriA.toLowerCase() === uriB.toLowerCase()\n}\n\n/**\n * @param {Readonly<import('./types/index').URIComponent>} cmpts\n * @param {import('./types/index').Options} [opts]\n * @returns {string}\n */\nfunction serialize (cmpts, opts) {\n  const component = {\n    host: cmpts.host,\n    scheme: cmpts.scheme,\n    userinfo: cmpts.userinfo,\n    port: cmpts.port,\n    path: cmpts.path,\n    query: cmpts.query,\n    nid: cmpts.nid,\n    nss: cmpts.nss,\n    uuid: cmpts.uuid,\n    fragment: cmpts.fragment,\n    reference: cmpts.reference,\n    resourceName: cmpts.resourceName,\n    secure: cmpts.secure,\n    error: ''\n  }\n  const options = Object.assign({}, opts)\n  const uriTokens = []\n\n  // find scheme handler\n  const schemeHandler = getSchemeHandler(options.scheme || component.scheme)\n\n  // perform scheme specific serialization\n  if (schemeHandler && schemeHandler.serialize) schemeHandler.serialize(component, options)\n\n  if (component.path !== undefined) {\n    if (!options.skipEscape) {\n      component.path = escape(component.path)\n\n      if (component.scheme !== undefined) {\n        component.path = component.path.split('%3A').join(':')\n      }\n    } else {\n      component.path = unescape(component.path)\n    }\n  }\n\n  if (options.reference !== 'suffix' && component.scheme) {\n    uriTokens.push(component.scheme, ':')\n  }\n\n  const authority = recomposeAuthority(component)\n  if (authority !== undefined) {\n    if (options.reference !== 'suffix') {\n      uriTokens.push('//')\n    }\n\n    uriTokens.push(authority)\n\n    if (component.path && component.path[0] !== '/') {\n      uriTokens.push('/')\n    }\n  }\n  if (component.path !== undefined) {\n    let s = component.path\n\n    if (!options.absolutePath && (!schemeHandler || !schemeHandler.absolutePath)) {\n      s = removeDotSegments(s)\n    }\n\n    if (\n      authority === undefined &&\n      s[0] === '/' &&\n      s[1] === '/'\n    ) {\n      // don't allow the path to start with \"//\"\n      s = '/%2F' + s.slice(2)\n    }\n\n    uriTokens.push(s)\n  }\n\n  if (component.query !== undefined) {\n    uriTokens.push('?', component.query)\n  }\n\n  if (component.fragment !== undefined) {\n    uriTokens.push('#', component.fragment)\n  }\n  return uriTokens.join('')\n}\n\nconst URI_PARSE = /^(?:([^#/:?]+):)?(?:\\/\\/((?:([^#/?@]*)@)?(\\[[^#/?\\]]+\\]|[^#/:?]*)(?::(\\d*))?))?([^#?]*)(?:\\?([^#]*))?(?:#((?:.|[\\n\\r])*))?/u\n\n/**\n * @param {string} uri\n * @param {import('./types/index').Options} [opts]\n * @returns\n */\nfunction parse (uri, opts) {\n  const options = Object.assign({}, opts)\n  /** @type {import('./types/index').URIComponent} */\n  const parsed = {\n    scheme: undefined,\n    userinfo: undefined,\n    host: '',\n    port: undefined,\n    path: '',\n    query: undefined,\n    fragment: undefined\n  }\n\n  let isIP = false\n  if (options.reference === 'suffix') {\n    if (options.scheme) {\n      uri = options.scheme + ':' + uri\n    } else {\n      uri = '//' + uri\n    }\n  }\n\n  const matches = uri.match(URI_PARSE)\n\n  if (matches) {\n    // store each component\n    parsed.scheme = matches[1]\n    parsed.userinfo = matches[3]\n    parsed.host = matches[4]\n    parsed.port = parseInt(matches[5], 10)\n    parsed.path = matches[6] || ''\n    parsed.query = matches[7]\n    parsed.fragment = matches[8]\n\n    // fix port number\n    if (isNaN(parsed.port)) {\n      parsed.port = matches[5]\n    }\n    if (parsed.host) {\n      const ipv4result = isIPv4(parsed.host)\n      if (ipv4result === false) {\n        const ipv6result = normalizeIPv6(parsed.host)\n        parsed.host = ipv6result.host.toLowerCase()\n        isIP = ipv6result.isIPV6\n      } else {\n        isIP = true\n      }\n    }\n    if (parsed.scheme === undefined && parsed.userinfo === undefined && parsed.host === undefined && parsed.port === undefined && parsed.query === undefined && !parsed.path) {\n      parsed.reference = 'same-document'\n    } else if (parsed.scheme === undefined) {\n      parsed.reference = 'relative'\n    } else if (parsed.fragment === undefined) {\n      parsed.reference = 'absolute'\n    } else {\n      parsed.reference = 'uri'\n    }\n\n    // check for reference errors\n    if (options.reference && options.reference !== 'suffix' && options.reference !== parsed.reference) {\n      parsed.error = parsed.error || 'URI is not a ' + options.reference + ' reference.'\n    }\n\n    // find scheme handler\n    const schemeHandler = getSchemeHandler(options.scheme || parsed.scheme)\n\n    // check if scheme can't handle IRIs\n    if (!options.unicodeSupport && (!schemeHandler || !schemeHandler.unicodeSupport)) {\n      // if host component is a domain name\n      if (parsed.host && (options.domainHost || (schemeHandler && schemeHandler.domainHost)) && isIP === false && nonSimpleDomain(parsed.host)) {\n        // convert Unicode IDN -> ASCII IDN\n        try {\n          parsed.host = URL.domainToASCII(parsed.host.toLowerCase())\n        } catch (e) {\n          parsed.error = parsed.error || \"Host's domain name can not be converted to ASCII: \" + e\n        }\n      }\n      // convert IRI -> URI\n    }\n\n    if (!schemeHandler || (schemeHandler && !schemeHandler.skipNormalize)) {\n      if (uri.indexOf('%') !== -1) {\n        if (parsed.scheme !== undefined) {\n          parsed.scheme = unescape(parsed.scheme)\n        }\n        if (parsed.host !== undefined) {\n          parsed.host = unescape(parsed.host)\n        }\n      }\n      if (parsed.path) {\n        parsed.path = escape(unescape(parsed.path))\n      }\n      if (parsed.fragment) {\n        parsed.fragment = encodeURI(decodeURIComponent(parsed.fragment))\n      }\n    }\n\n    // perform scheme specific parsing\n    if (schemeHandler && schemeHandler.parse) {\n      schemeHandler.parse(parsed, options)\n    }\n  } else {\n    parsed.error = parsed.error || 'URI can not be parsed.'\n  }\n  return parsed\n}\n\nconst fastUri = {\n  SCHEMES,\n  normalize,\n  resolve,\n  resolveComponent,\n  equal,\n  serialize,\n  parse\n}\n\nmodule.exports = fastUri\nmodule.exports[\"default\"] = fastUri\nmodule.exports.fastUri = fastUri\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-uri/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fast-uri/lib/schemes.js":
/*!**********************************************!*\
  !*** ./node_modules/fast-uri/lib/schemes.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { isUUID } = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/fast-uri/lib/utils.js\")\nconst URN_REG = /([\\da-z][\\d\\-a-z]{0,31}):((?:[\\w!$'()*+,\\-.:;=@]|%[\\da-f]{2})+)/iu\n\nconst supportedSchemeNames = /** @type {const} */ (['http', 'https', 'ws',\n  'wss', 'urn', 'urn:uuid'])\n\n/** @typedef {supportedSchemeNames[number]} SchemeName */\n\n/**\n * @param {string} name\n * @returns {name is SchemeName}\n */\nfunction isValidSchemeName (name) {\n  return supportedSchemeNames.indexOf(/** @type {*} */ (name)) !== -1\n}\n\n/**\n * @callback SchemeFn\n * @param {import('../types/index').URIComponent} component\n * @param {import('../types/index').Options} options\n * @returns {import('../types/index').URIComponent}\n */\n\n/**\n * @typedef {Object} SchemeHandler\n * @property {SchemeName} scheme - The scheme name.\n * @property {boolean} [domainHost] - Indicates if the scheme supports domain hosts.\n * @property {SchemeFn} parse - Function to parse the URI component for this scheme.\n * @property {SchemeFn} serialize - Function to serialize the URI component for this scheme.\n * @property {boolean} [skipNormalize] - Indicates if normalization should be skipped for this scheme.\n * @property {boolean} [absolutePath] - Indicates if the scheme uses absolute paths.\n * @property {boolean} [unicodeSupport] - Indicates if the scheme supports Unicode.\n */\n\n/**\n * @param {import('../types/index').URIComponent} wsComponent\n * @returns {boolean}\n */\nfunction wsIsSecure (wsComponent) {\n  if (wsComponent.secure === true) {\n    return true\n  } else if (wsComponent.secure === false) {\n    return false\n  } else if (wsComponent.scheme) {\n    return (\n      wsComponent.scheme.length === 3 &&\n      (wsComponent.scheme[0] === 'w' || wsComponent.scheme[0] === 'W') &&\n      (wsComponent.scheme[1] === 's' || wsComponent.scheme[1] === 'S') &&\n      (wsComponent.scheme[2] === 's' || wsComponent.scheme[2] === 'S')\n    )\n  } else {\n    return false\n  }\n}\n\n/** @type {SchemeFn} */\nfunction httpParse (component) {\n  if (!component.host) {\n    component.error = component.error || 'HTTP URIs must have a host.'\n  }\n\n  return component\n}\n\n/** @type {SchemeFn} */\nfunction httpSerialize (component) {\n  const secure = String(component.scheme).toLowerCase() === 'https'\n\n  // normalize the default port\n  if (component.port === (secure ? 443 : 80) || component.port === '') {\n    component.port = undefined\n  }\n\n  // normalize the empty path\n  if (!component.path) {\n    component.path = '/'\n  }\n\n  // NOTE: We do not parse query strings for HTTP URIs\n  // as WWW Form Url Encoded query strings are part of the HTML4+ spec,\n  // and not the HTTP spec.\n\n  return component\n}\n\n/** @type {SchemeFn} */\nfunction wsParse (wsComponent) {\n// indicate if the secure flag is set\n  wsComponent.secure = wsIsSecure(wsComponent)\n\n  // construct resouce name\n  wsComponent.resourceName = (wsComponent.path || '/') + (wsComponent.query ? '?' + wsComponent.query : '')\n  wsComponent.path = undefined\n  wsComponent.query = undefined\n\n  return wsComponent\n}\n\n/** @type {SchemeFn} */\nfunction wsSerialize (wsComponent) {\n// normalize the default port\n  if (wsComponent.port === (wsIsSecure(wsComponent) ? 443 : 80) || wsComponent.port === '') {\n    wsComponent.port = undefined\n  }\n\n  // ensure scheme matches secure flag\n  if (typeof wsComponent.secure === 'boolean') {\n    wsComponent.scheme = (wsComponent.secure ? 'wss' : 'ws')\n    wsComponent.secure = undefined\n  }\n\n  // reconstruct path from resource name\n  if (wsComponent.resourceName) {\n    const [path, query] = wsComponent.resourceName.split('?')\n    wsComponent.path = (path && path !== '/' ? path : undefined)\n    wsComponent.query = query\n    wsComponent.resourceName = undefined\n  }\n\n  // forbid fragment component\n  wsComponent.fragment = undefined\n\n  return wsComponent\n}\n\n/** @type {SchemeFn} */\nfunction urnParse (urnComponent, options) {\n  if (!urnComponent.path) {\n    urnComponent.error = 'URN can not be parsed'\n    return urnComponent\n  }\n  const matches = urnComponent.path.match(URN_REG)\n  if (matches) {\n    const scheme = options.scheme || urnComponent.scheme || 'urn'\n    urnComponent.nid = matches[1].toLowerCase()\n    urnComponent.nss = matches[2]\n    const urnScheme = `${scheme}:${options.nid || urnComponent.nid}`\n    const schemeHandler = getSchemeHandler(urnScheme)\n    urnComponent.path = undefined\n\n    if (schemeHandler) {\n      urnComponent = schemeHandler.parse(urnComponent, options)\n    }\n  } else {\n    urnComponent.error = urnComponent.error || 'URN can not be parsed.'\n  }\n\n  return urnComponent\n}\n\n/** @type {SchemeFn} */\nfunction urnSerialize (urnComponent, options) {\n  if (urnComponent.nid === undefined) {\n    throw new Error('URN without nid cannot be serialized')\n  }\n  const scheme = options.scheme || urnComponent.scheme || 'urn'\n  const nid = urnComponent.nid.toLowerCase()\n  const urnScheme = `${scheme}:${options.nid || nid}`\n  const schemeHandler = getSchemeHandler(urnScheme)\n\n  if (schemeHandler) {\n    urnComponent = schemeHandler.serialize(urnComponent, options)\n  }\n\n  const uriComponent = urnComponent\n  const nss = urnComponent.nss\n  uriComponent.path = `${nid || options.nid}:${nss}`\n\n  options.skipEscape = true\n  return uriComponent\n}\n\n/** @type {SchemeFn} */\nfunction urnuuidParse (urnComponent, options) {\n  const uuidComponent = urnComponent\n  uuidComponent.uuid = uuidComponent.nss\n  uuidComponent.nss = undefined\n\n  if (!options.tolerant && (!uuidComponent.uuid || !isUUID(uuidComponent.uuid))) {\n    uuidComponent.error = uuidComponent.error || 'UUID is not valid.'\n  }\n\n  return uuidComponent\n}\n\n/** @type {SchemeFn} */\nfunction urnuuidSerialize (uuidComponent) {\n  const urnComponent = uuidComponent\n  // normalize UUID\n  urnComponent.nss = (uuidComponent.uuid || '').toLowerCase()\n  return urnComponent\n}\n\nconst http = /** @type {SchemeHandler} */ ({\n  scheme: 'http',\n  domainHost: true,\n  parse: httpParse,\n  serialize: httpSerialize\n})\n\nconst https = /** @type {SchemeHandler} */ ({\n  scheme: 'https',\n  domainHost: http.domainHost,\n  parse: httpParse,\n  serialize: httpSerialize\n})\n\nconst ws = /** @type {SchemeHandler} */ ({\n  scheme: 'ws',\n  domainHost: true,\n  parse: wsParse,\n  serialize: wsSerialize\n})\n\nconst wss = /** @type {SchemeHandler} */ ({\n  scheme: 'wss',\n  domainHost: ws.domainHost,\n  parse: ws.parse,\n  serialize: ws.serialize\n})\n\nconst urn = /** @type {SchemeHandler} */ ({\n  scheme: 'urn',\n  parse: urnParse,\n  serialize: urnSerialize,\n  skipNormalize: true\n})\n\nconst urnuuid = /** @type {SchemeHandler} */ ({\n  scheme: 'urn:uuid',\n  parse: urnuuidParse,\n  serialize: urnuuidSerialize,\n  skipNormalize: true\n})\n\nconst SCHEMES = /** @type {Record<SchemeName, SchemeHandler>} */ ({\n  http,\n  https,\n  ws,\n  wss,\n  urn,\n  'urn:uuid': urnuuid\n})\n\nObject.setPrototypeOf(SCHEMES, null)\n\n/**\n * @param {string|undefined} scheme\n * @returns {SchemeHandler|undefined}\n */\nfunction getSchemeHandler (scheme) {\n  return (\n    scheme && (\n      SCHEMES[/** @type {SchemeName} */ (scheme)] ||\n      SCHEMES[/** @type {SchemeName} */(scheme.toLowerCase())])\n  ) ||\n    undefined\n}\n\nmodule.exports = {\n  wsIsSecure,\n  SCHEMES,\n  isValidSchemeName,\n  getSchemeHandler,\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-uri/lib/schemes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fast-uri/lib/utils.js":
/*!********************************************!*\
  !*** ./node_modules/fast-uri/lib/utils.js ***!
  \********************************************/
/***/ ((module) => {

eval("\n\n/** @type {(value: string) => boolean} */\nconst isUUID = RegExp.prototype.test.bind(/^[\\da-f]{8}-[\\da-f]{4}-[\\da-f]{4}-[\\da-f]{4}-[\\da-f]{12}$/iu)\n\n/** @type {(value: string) => boolean} */\nconst isIPv4 = RegExp.prototype.test.bind(/^(?:(?:25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)\\.){3}(?:25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)$/u)\n\n/**\n * @param {Array<string>} input\n * @returns {string}\n */\nfunction stringArrayToHexStripped (input) {\n  let acc = ''\n  let code = 0\n  let i = 0\n\n  for (i = 0; i < input.length; i++) {\n    code = input[i].charCodeAt(0)\n    if (code === 48) {\n      continue\n    }\n    if (!((code >= 48 && code <= 57) || (code >= 65 && code <= 70) || (code >= 97 && code <= 102))) {\n      return ''\n    }\n    acc += input[i]\n    break\n  }\n\n  for (i += 1; i < input.length; i++) {\n    code = input[i].charCodeAt(0)\n    if (!((code >= 48 && code <= 57) || (code >= 65 && code <= 70) || (code >= 97 && code <= 102))) {\n      return ''\n    }\n    acc += input[i]\n  }\n  return acc\n}\n\n/**\n * @typedef {Object} GetIPV6Result\n * @property {boolean} error - Indicates if there was an error parsing the IPv6 address.\n * @property {string} address - The parsed IPv6 address.\n * @property {string} [zone] - The zone identifier, if present.\n */\n\n/**\n * @param {string} value\n * @returns {boolean}\n */\nconst nonSimpleDomain = RegExp.prototype.test.bind(/[^!\"$&'()*+,\\-.;=_`a-z{}~]/u)\n\n/**\n * @param {Array<string>} buffer\n * @returns {boolean}\n */\nfunction consumeIsZone (buffer) {\n  buffer.length = 0\n  return true\n}\n\n/**\n * @param {Array<string>} buffer\n * @param {Array<string>} address\n * @param {GetIPV6Result} output\n * @returns {boolean}\n */\nfunction consumeHextets (buffer, address, output) {\n  if (buffer.length) {\n    const hex = stringArrayToHexStripped(buffer)\n    if (hex !== '') {\n      address.push(hex)\n    } else {\n      output.error = true\n      return false\n    }\n    buffer.length = 0\n  }\n  return true\n}\n\n/**\n * @param {string} input\n * @returns {GetIPV6Result}\n */\nfunction getIPV6 (input) {\n  let tokenCount = 0\n  const output = { error: false, address: '', zone: '' }\n  /** @type {Array<string>} */\n  const address = []\n  /** @type {Array<string>} */\n  const buffer = []\n  let endipv6Encountered = false\n  let endIpv6 = false\n\n  let consume = consumeHextets\n\n  for (let i = 0; i < input.length; i++) {\n    const cursor = input[i]\n    if (cursor === '[' || cursor === ']') { continue }\n    if (cursor === ':') {\n      if (endipv6Encountered === true) {\n        endIpv6 = true\n      }\n      if (!consume(buffer, address, output)) { break }\n      if (++tokenCount > 7) {\n        // not valid\n        output.error = true\n        break\n      }\n      if (i > 0 && input[i - 1] === ':') {\n        endipv6Encountered = true\n      }\n      address.push(':')\n      continue\n    } else if (cursor === '%') {\n      if (!consume(buffer, address, output)) { break }\n      // switch to zone detection\n      consume = consumeIsZone\n    } else {\n      buffer.push(cursor)\n      continue\n    }\n  }\n  if (buffer.length) {\n    if (consume === consumeIsZone) {\n      output.zone = buffer.join('')\n    } else if (endIpv6) {\n      address.push(buffer.join(''))\n    } else {\n      address.push(stringArrayToHexStripped(buffer))\n    }\n  }\n  output.address = address.join('')\n  return output\n}\n\n/**\n * @typedef {Object} NormalizeIPv6Result\n * @property {string} host - The normalized host.\n * @property {string} [escapedHost] - The escaped host.\n * @property {boolean} isIPV6 - Indicates if the host is an IPv6 address.\n */\n\n/**\n * @param {string} host\n * @returns {NormalizeIPv6Result}\n */\nfunction normalizeIPv6 (host) {\n  if (findToken(host, ':') < 2) { return { host, isIPV6: false } }\n  const ipv6 = getIPV6(host)\n\n  if (!ipv6.error) {\n    let newHost = ipv6.address\n    let escapedHost = ipv6.address\n    if (ipv6.zone) {\n      newHost += '%' + ipv6.zone\n      escapedHost += '%25' + ipv6.zone\n    }\n    return { host: newHost, isIPV6: true, escapedHost }\n  } else {\n    return { host, isIPV6: false }\n  }\n}\n\n/**\n * @param {string} str\n * @param {string} token\n * @returns {number}\n */\nfunction findToken (str, token) {\n  let ind = 0\n  for (let i = 0; i < str.length; i++) {\n    if (str[i] === token) ind++\n  }\n  return ind\n}\n\n/**\n * @param {string} path\n * @returns {string}\n *\n * @see https://datatracker.ietf.org/doc/html/rfc3986#section-5.2.4\n */\nfunction removeDotSegments (path) {\n  let input = path\n  const output = []\n  let nextSlash = -1\n  let len = 0\n\n  // eslint-disable-next-line no-cond-assign\n  while (len = input.length) {\n    if (len === 1) {\n      if (input === '.') {\n        break\n      } else if (input === '/') {\n        output.push('/')\n        break\n      } else {\n        output.push(input)\n        break\n      }\n    } else if (len === 2) {\n      if (input[0] === '.') {\n        if (input[1] === '.') {\n          break\n        } else if (input[1] === '/') {\n          input = input.slice(2)\n          continue\n        }\n      } else if (input[0] === '/') {\n        if (input[1] === '.' || input[1] === '/') {\n          output.push('/')\n          break\n        }\n      }\n    } else if (len === 3) {\n      if (input === '/..') {\n        if (output.length !== 0) {\n          output.pop()\n        }\n        output.push('/')\n        break\n      }\n    }\n    if (input[0] === '.') {\n      if (input[1] === '.') {\n        if (input[2] === '/') {\n          input = input.slice(3)\n          continue\n        }\n      } else if (input[1] === '/') {\n        input = input.slice(2)\n        continue\n      }\n    } else if (input[0] === '/') {\n      if (input[1] === '.') {\n        if (input[2] === '/') {\n          input = input.slice(2)\n          continue\n        } else if (input[2] === '.') {\n          if (input[3] === '/') {\n            input = input.slice(3)\n            if (output.length !== 0) {\n              output.pop()\n            }\n            continue\n          }\n        }\n      }\n    }\n\n    // Rule 2E: Move normal path segment to output\n    if ((nextSlash = input.indexOf('/', 1)) === -1) {\n      output.push(input)\n      break\n    } else {\n      output.push(input.slice(0, nextSlash))\n      input = input.slice(nextSlash)\n    }\n  }\n\n  return output.join('')\n}\n\n/**\n * @param {import('../types/index').URIComponent} component\n * @param {boolean} esc\n * @returns {import('../types/index').URIComponent}\n */\nfunction normalizeComponentEncoding (component, esc) {\n  const func = esc !== true ? escape : unescape\n  if (component.scheme !== undefined) {\n    component.scheme = func(component.scheme)\n  }\n  if (component.userinfo !== undefined) {\n    component.userinfo = func(component.userinfo)\n  }\n  if (component.host !== undefined) {\n    component.host = func(component.host)\n  }\n  if (component.path !== undefined) {\n    component.path = func(component.path)\n  }\n  if (component.query !== undefined) {\n    component.query = func(component.query)\n  }\n  if (component.fragment !== undefined) {\n    component.fragment = func(component.fragment)\n  }\n  return component\n}\n\n/**\n * @param {import('../types/index').URIComponent} component\n * @returns {string|undefined}\n */\nfunction recomposeAuthority (component) {\n  const uriTokens = []\n\n  if (component.userinfo !== undefined) {\n    uriTokens.push(component.userinfo)\n    uriTokens.push('@')\n  }\n\n  if (component.host !== undefined) {\n    let host = unescape(component.host)\n    if (!isIPv4(host)) {\n      const ipV6res = normalizeIPv6(host)\n      if (ipV6res.isIPV6 === true) {\n        host = `[${ipV6res.escapedHost}]`\n      } else {\n        host = component.host\n      }\n    }\n    uriTokens.push(host)\n  }\n\n  if (typeof component.port === 'number' || typeof component.port === 'string') {\n    uriTokens.push(':')\n    uriTokens.push(String(component.port))\n  }\n\n  return uriTokens.length ? uriTokens.join('') : undefined\n};\n\nmodule.exports = {\n  nonSimpleDomain,\n  recomposeAuthority,\n  normalizeComponentEncoding,\n  removeDotSegments,\n  isIPv4,\n  isUUID,\n  normalizeIPv6,\n  stringArrayToHexStripped\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-uri/lib/utils.js\n");

/***/ })

};
;