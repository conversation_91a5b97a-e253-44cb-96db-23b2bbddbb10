"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/image-size";
exports.ids = ["vendor-chunks/image-size"];
exports.modules = {

/***/ "(rsc)/./node_modules/image-size/dist/fromFile.mjs":
/*!***************************************************!*\
  !*** ./node_modules/image-size/dist/fromFile.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   imageSizeFromFile: () => (/* binding */ imageSizeFromFile),\n/* harmony export */   setConcurrency: () => (/* binding */ setConcurrency)\n/* harmony export */ });\n/* harmony import */ var node_fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:fs */ \"node:fs\");\n/* harmony import */ var node_path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:path */ \"node:path\");\n\n\n\n// lib/fromFile.ts\n\n// lib/types/utils.ts\nvar decoder = new TextDecoder();\nvar toUTF8String = (input, start = 0, end = input.length) => decoder.decode(input.slice(start, end));\nvar toHexString = (input, start = 0, end = input.length) => input.slice(start, end).reduce((memo, i) => memo + `0${i.toString(16)}`.slice(-2), \"\");\nvar getView = (input, offset) => new DataView(input.buffer, input.byteOffset + offset);\nvar readInt16LE = (input, offset = 0) => getView(input, offset).getInt16(0, true);\nvar readUInt16BE = (input, offset = 0) => getView(input, offset).getUint16(0, false);\nvar readUInt16LE = (input, offset = 0) => getView(input, offset).getUint16(0, true);\nvar readUInt24LE = (input, offset = 0) => {\n  const view = getView(input, offset);\n  return view.getUint16(0, true) + (view.getUint8(2) << 16);\n};\nvar readInt32LE = (input, offset = 0) => getView(input, offset).getInt32(0, true);\nvar readUInt32BE = (input, offset = 0) => getView(input, offset).getUint32(0, false);\nvar readUInt32LE = (input, offset = 0) => getView(input, offset).getUint32(0, true);\nvar readUInt64 = (input, offset, isBigEndian) => getView(input, offset).getBigUint64(0, !isBigEndian);\nvar methods = {\n  readUInt16BE,\n  readUInt16LE,\n  readUInt32BE,\n  readUInt32LE\n};\nfunction readUInt(input, bits, offset = 0, isBigEndian = false) {\n  const endian = isBigEndian ? \"BE\" : \"LE\";\n  const methodName = `readUInt${bits}${endian}`;\n  return methods[methodName](input, offset);\n}\nfunction readBox(input, offset) {\n  if (input.length - offset < 4) return;\n  const boxSize = readUInt32BE(input, offset);\n  if (input.length - offset < boxSize) return;\n  return {\n    name: toUTF8String(input, 4 + offset, 8 + offset),\n    offset,\n    size: boxSize\n  };\n}\nfunction findBox(input, boxName, currentOffset) {\n  while (currentOffset < input.length) {\n    const box = readBox(input, currentOffset);\n    if (!box) break;\n    if (box.name === boxName) return box;\n    currentOffset += box.size > 0 ? box.size : 8;\n  }\n}\n\n// lib/types/bmp.ts\nvar BMP = {\n  validate: (input) => toUTF8String(input, 0, 2) === \"BM\",\n  calculate: (input) => ({\n    height: Math.abs(readInt32LE(input, 22)),\n    width: readUInt32LE(input, 18)\n  })\n};\n\n// lib/types/ico.ts\nvar TYPE_ICON = 1;\nvar SIZE_HEADER = 2 + 2 + 2;\nvar SIZE_IMAGE_ENTRY = 1 + 1 + 1 + 1 + 2 + 2 + 4 + 4;\nfunction getSizeFromOffset(input, offset) {\n  const value = input[offset];\n  return value === 0 ? 256 : value;\n}\nfunction getImageSize(input, imageIndex) {\n  const offset = SIZE_HEADER + imageIndex * SIZE_IMAGE_ENTRY;\n  return {\n    height: getSizeFromOffset(input, offset + 1),\n    width: getSizeFromOffset(input, offset)\n  };\n}\nvar ICO = {\n  validate(input) {\n    const reserved = readUInt16LE(input, 0);\n    const imageCount = readUInt16LE(input, 4);\n    if (reserved !== 0 || imageCount === 0) return false;\n    const imageType = readUInt16LE(input, 2);\n    return imageType === TYPE_ICON;\n  },\n  calculate(input) {\n    const nbImages = readUInt16LE(input, 4);\n    const imageSize2 = getImageSize(input, 0);\n    if (nbImages === 1) return imageSize2;\n    const images = [];\n    for (let imageIndex = 0; imageIndex < nbImages; imageIndex += 1) {\n      images.push(getImageSize(input, imageIndex));\n    }\n    return {\n      width: imageSize2.width,\n      height: imageSize2.height,\n      images\n    };\n  }\n};\n\n// lib/types/cur.ts\nvar TYPE_CURSOR = 2;\nvar CUR = {\n  validate(input) {\n    const reserved = readUInt16LE(input, 0);\n    const imageCount = readUInt16LE(input, 4);\n    if (reserved !== 0 || imageCount === 0) return false;\n    const imageType = readUInt16LE(input, 2);\n    return imageType === TYPE_CURSOR;\n  },\n  calculate: (input) => ICO.calculate(input)\n};\n\n// lib/types/dds.ts\nvar DDS = {\n  validate: (input) => readUInt32LE(input, 0) === 542327876,\n  calculate: (input) => ({\n    height: readUInt32LE(input, 12),\n    width: readUInt32LE(input, 16)\n  })\n};\n\n// lib/types/gif.ts\nvar gifRegexp = /^GIF8[79]a/;\nvar GIF = {\n  validate: (input) => gifRegexp.test(toUTF8String(input, 0, 6)),\n  calculate: (input) => ({\n    height: readUInt16LE(input, 8),\n    width: readUInt16LE(input, 6)\n  })\n};\n\n// lib/types/heif.ts\nvar brandMap = {\n  avif: \"avif\",\n  mif1: \"heif\",\n  msf1: \"heif\",\n  // heif-sequence\n  heic: \"heic\",\n  heix: \"heic\",\n  hevc: \"heic\",\n  // heic-sequence\n  hevx: \"heic\"\n  // heic-sequence\n};\nvar HEIF = {\n  validate(input) {\n    const boxType = toUTF8String(input, 4, 8);\n    if (boxType !== \"ftyp\") return false;\n    const ftypBox = findBox(input, \"ftyp\", 0);\n    if (!ftypBox) return false;\n    const brand = toUTF8String(input, ftypBox.offset + 8, ftypBox.offset + 12);\n    return brand in brandMap;\n  },\n  calculate(input) {\n    const metaBox = findBox(input, \"meta\", 0);\n    const iprpBox = metaBox && findBox(input, \"iprp\", metaBox.offset + 12);\n    const ipcoBox = iprpBox && findBox(input, \"ipco\", iprpBox.offset + 8);\n    if (!ipcoBox) {\n      throw new TypeError(\"Invalid HEIF, no ipco box found\");\n    }\n    const type = toUTF8String(input, 8, 12);\n    const images = [];\n    let currentOffset = ipcoBox.offset + 8;\n    while (currentOffset < ipcoBox.offset + ipcoBox.size) {\n      const ispeBox = findBox(input, \"ispe\", currentOffset);\n      if (!ispeBox) break;\n      const rawWidth = readUInt32BE(input, ispeBox.offset + 12);\n      const rawHeight = readUInt32BE(input, ispeBox.offset + 16);\n      const clapBox = findBox(input, \"clap\", currentOffset);\n      let width = rawWidth;\n      let height = rawHeight;\n      if (clapBox && clapBox.offset < ipcoBox.offset + ipcoBox.size) {\n        const cropRight = readUInt32BE(input, clapBox.offset + 12);\n        width = rawWidth - cropRight;\n      }\n      images.push({ height, width });\n      currentOffset = ispeBox.offset + ispeBox.size;\n    }\n    if (images.length === 0) {\n      throw new TypeError(\"Invalid HEIF, no sizes found\");\n    }\n    return {\n      width: images[0].width,\n      height: images[0].height,\n      type,\n      ...images.length > 1 ? { images } : {}\n    };\n  }\n};\n\n// lib/types/icns.ts\nvar SIZE_HEADER2 = 4 + 4;\nvar FILE_LENGTH_OFFSET = 4;\nvar ENTRY_LENGTH_OFFSET = 4;\nvar ICON_TYPE_SIZE = {\n  ICON: 32,\n  \"ICN#\": 32,\n  // m => 16 x 16\n  \"icm#\": 16,\n  icm4: 16,\n  icm8: 16,\n  // s => 16 x 16\n  \"ics#\": 16,\n  ics4: 16,\n  ics8: 16,\n  is32: 16,\n  s8mk: 16,\n  icp4: 16,\n  // l => 32 x 32\n  icl4: 32,\n  icl8: 32,\n  il32: 32,\n  l8mk: 32,\n  icp5: 32,\n  ic11: 32,\n  // h => 48 x 48\n  ich4: 48,\n  ich8: 48,\n  ih32: 48,\n  h8mk: 48,\n  // . => 64 x 64\n  icp6: 64,\n  ic12: 32,\n  // t => 128 x 128\n  it32: 128,\n  t8mk: 128,\n  ic07: 128,\n  // . => 256 x 256\n  ic08: 256,\n  ic13: 256,\n  // . => 512 x 512\n  ic09: 512,\n  ic14: 512,\n  // . => 1024 x 1024\n  ic10: 1024\n};\nfunction readImageHeader(input, imageOffset) {\n  const imageLengthOffset = imageOffset + ENTRY_LENGTH_OFFSET;\n  return [\n    toUTF8String(input, imageOffset, imageLengthOffset),\n    readUInt32BE(input, imageLengthOffset)\n  ];\n}\nfunction getImageSize2(type) {\n  const size = ICON_TYPE_SIZE[type];\n  return { width: size, height: size, type };\n}\nvar ICNS = {\n  validate: (input) => toUTF8String(input, 0, 4) === \"icns\",\n  calculate(input) {\n    const inputLength = input.length;\n    const fileLength = readUInt32BE(input, FILE_LENGTH_OFFSET);\n    let imageOffset = SIZE_HEADER2;\n    const images = [];\n    while (imageOffset < fileLength && imageOffset < inputLength) {\n      const imageHeader = readImageHeader(input, imageOffset);\n      const imageSize2 = getImageSize2(imageHeader[0]);\n      images.push(imageSize2);\n      imageOffset += imageHeader[1];\n    }\n    if (images.length === 0) {\n      throw new TypeError(\"Invalid ICNS, no sizes found\");\n    }\n    return {\n      width: images[0].width,\n      height: images[0].height,\n      ...images.length > 1 ? { images } : {}\n    };\n  }\n};\n\n// lib/types/j2c.ts\nvar J2C = {\n  // TODO: this doesn't seem right. SIZ marker doesn't have to be right after the SOC\n  validate: (input) => readUInt32BE(input, 0) === 4283432785,\n  calculate: (input) => ({\n    height: readUInt32BE(input, 12),\n    width: readUInt32BE(input, 8)\n  })\n};\n\n// lib/types/jp2.ts\nvar JP2 = {\n  validate(input) {\n    const boxType = toUTF8String(input, 4, 8);\n    if (boxType !== \"jP  \") return false;\n    const ftypBox = findBox(input, \"ftyp\", 0);\n    if (!ftypBox) return false;\n    const brand = toUTF8String(input, ftypBox.offset + 8, ftypBox.offset + 12);\n    return brand === \"jp2 \";\n  },\n  calculate(input) {\n    const jp2hBox = findBox(input, \"jp2h\", 0);\n    const ihdrBox = jp2hBox && findBox(input, \"ihdr\", jp2hBox.offset + 8);\n    if (ihdrBox) {\n      return {\n        height: readUInt32BE(input, ihdrBox.offset + 8),\n        width: readUInt32BE(input, ihdrBox.offset + 12)\n      };\n    }\n    throw new TypeError(\"Unsupported JPEG 2000 format\");\n  }\n};\n\n// lib/types/jpg.ts\nvar EXIF_MARKER = \"45786966\";\nvar APP1_DATA_SIZE_BYTES = 2;\nvar EXIF_HEADER_BYTES = 6;\nvar TIFF_BYTE_ALIGN_BYTES = 2;\nvar BIG_ENDIAN_BYTE_ALIGN = \"4d4d\";\nvar LITTLE_ENDIAN_BYTE_ALIGN = \"4949\";\nvar IDF_ENTRY_BYTES = 12;\nvar NUM_DIRECTORY_ENTRIES_BYTES = 2;\nfunction isEXIF(input) {\n  return toHexString(input, 2, 6) === EXIF_MARKER;\n}\nfunction extractSize(input, index) {\n  return {\n    height: readUInt16BE(input, index),\n    width: readUInt16BE(input, index + 2)\n  };\n}\nfunction extractOrientation(exifBlock, isBigEndian) {\n  const idfOffset = 8;\n  const offset = EXIF_HEADER_BYTES + idfOffset;\n  const idfDirectoryEntries = readUInt(exifBlock, 16, offset, isBigEndian);\n  for (let directoryEntryNumber = 0; directoryEntryNumber < idfDirectoryEntries; directoryEntryNumber++) {\n    const start = offset + NUM_DIRECTORY_ENTRIES_BYTES + directoryEntryNumber * IDF_ENTRY_BYTES;\n    const end = start + IDF_ENTRY_BYTES;\n    if (start > exifBlock.length) {\n      return;\n    }\n    const block = exifBlock.slice(start, end);\n    const tagNumber = readUInt(block, 16, 0, isBigEndian);\n    if (tagNumber === 274) {\n      const dataFormat = readUInt(block, 16, 2, isBigEndian);\n      if (dataFormat !== 3) {\n        return;\n      }\n      const numberOfComponents = readUInt(block, 32, 4, isBigEndian);\n      if (numberOfComponents !== 1) {\n        return;\n      }\n      return readUInt(block, 16, 8, isBigEndian);\n    }\n  }\n}\nfunction validateExifBlock(input, index) {\n  const exifBlock = input.slice(APP1_DATA_SIZE_BYTES, index);\n  const byteAlign = toHexString(\n    exifBlock,\n    EXIF_HEADER_BYTES,\n    EXIF_HEADER_BYTES + TIFF_BYTE_ALIGN_BYTES\n  );\n  const isBigEndian = byteAlign === BIG_ENDIAN_BYTE_ALIGN;\n  const isLittleEndian = byteAlign === LITTLE_ENDIAN_BYTE_ALIGN;\n  if (isBigEndian || isLittleEndian) {\n    return extractOrientation(exifBlock, isBigEndian);\n  }\n}\nfunction validateInput(input, index) {\n  if (index > input.length) {\n    throw new TypeError(\"Corrupt JPG, exceeded buffer limits\");\n  }\n}\nvar JPG = {\n  validate: (input) => toHexString(input, 0, 2) === \"ffd8\",\n  calculate(_input) {\n    let input = _input.slice(4);\n    let orientation;\n    let next;\n    while (input.length) {\n      const i = readUInt16BE(input, 0);\n      validateInput(input, i);\n      if (input[i] !== 255) {\n        input = input.slice(1);\n        continue;\n      }\n      if (isEXIF(input)) {\n        orientation = validateExifBlock(input, i);\n      }\n      next = input[i + 1];\n      if (next === 192 || next === 193 || next === 194) {\n        const size = extractSize(input, i + 5);\n        if (!orientation) {\n          return size;\n        }\n        return {\n          height: size.height,\n          orientation,\n          width: size.width\n        };\n      }\n      input = input.slice(i + 2);\n    }\n    throw new TypeError(\"Invalid JPG, no size found\");\n  }\n};\n\n// lib/utils/bit-reader.ts\nvar BitReader = class {\n  constructor(input, endianness) {\n    this.input = input;\n    this.endianness = endianness;\n    // Skip the first 16 bits (2 bytes) of signature\n    this.byteOffset = 2;\n    this.bitOffset = 0;\n  }\n  /** Reads a specified number of bits, and move the offset */\n  getBits(length = 1) {\n    let result = 0;\n    let bitsRead = 0;\n    while (bitsRead < length) {\n      if (this.byteOffset >= this.input.length) {\n        throw new Error(\"Reached end of input\");\n      }\n      const currentByte = this.input[this.byteOffset];\n      const bitsLeft = 8 - this.bitOffset;\n      const bitsToRead = Math.min(length - bitsRead, bitsLeft);\n      if (this.endianness === \"little-endian\") {\n        const mask = (1 << bitsToRead) - 1;\n        const bits = currentByte >> this.bitOffset & mask;\n        result |= bits << bitsRead;\n      } else {\n        const mask = (1 << bitsToRead) - 1 << 8 - this.bitOffset - bitsToRead;\n        const bits = (currentByte & mask) >> 8 - this.bitOffset - bitsToRead;\n        result = result << bitsToRead | bits;\n      }\n      bitsRead += bitsToRead;\n      this.bitOffset += bitsToRead;\n      if (this.bitOffset === 8) {\n        this.byteOffset++;\n        this.bitOffset = 0;\n      }\n    }\n    return result;\n  }\n};\n\n// lib/types/jxl-stream.ts\nfunction calculateImageDimension(reader, isSmallImage) {\n  if (isSmallImage) {\n    return 8 * (1 + reader.getBits(5));\n  }\n  const sizeClass = reader.getBits(2);\n  const extraBits = [9, 13, 18, 30][sizeClass];\n  return 1 + reader.getBits(extraBits);\n}\nfunction calculateImageWidth(reader, isSmallImage, widthMode, height) {\n  if (isSmallImage && widthMode === 0) {\n    return 8 * (1 + reader.getBits(5));\n  }\n  if (widthMode === 0) {\n    return calculateImageDimension(reader, false);\n  }\n  const aspectRatios = [1, 1.2, 4 / 3, 1.5, 16 / 9, 5 / 4, 2];\n  return Math.floor(height * aspectRatios[widthMode - 1]);\n}\nvar JXLStream = {\n  validate: (input) => {\n    return toHexString(input, 0, 2) === \"ff0a\";\n  },\n  calculate(input) {\n    const reader = new BitReader(input, \"little-endian\");\n    const isSmallImage = reader.getBits(1) === 1;\n    const height = calculateImageDimension(reader, isSmallImage);\n    const widthMode = reader.getBits(3);\n    const width = calculateImageWidth(reader, isSmallImage, widthMode, height);\n    return { width, height };\n  }\n};\n\n// lib/types/jxl.ts\nfunction extractCodestream(input) {\n  const jxlcBox = findBox(input, \"jxlc\", 0);\n  if (jxlcBox) {\n    return input.slice(jxlcBox.offset + 8, jxlcBox.offset + jxlcBox.size);\n  }\n  const partialStreams = extractPartialStreams(input);\n  if (partialStreams.length > 0) {\n    return concatenateCodestreams(partialStreams);\n  }\n  return void 0;\n}\nfunction extractPartialStreams(input) {\n  const partialStreams = [];\n  let offset = 0;\n  while (offset < input.length) {\n    const jxlpBox = findBox(input, \"jxlp\", offset);\n    if (!jxlpBox) break;\n    partialStreams.push(\n      input.slice(jxlpBox.offset + 12, jxlpBox.offset + jxlpBox.size)\n    );\n    offset = jxlpBox.offset + jxlpBox.size;\n  }\n  return partialStreams;\n}\nfunction concatenateCodestreams(partialCodestreams) {\n  const totalLength = partialCodestreams.reduce(\n    (acc, curr) => acc + curr.length,\n    0\n  );\n  const codestream = new Uint8Array(totalLength);\n  let position = 0;\n  for (const partial of partialCodestreams) {\n    codestream.set(partial, position);\n    position += partial.length;\n  }\n  return codestream;\n}\nvar JXL = {\n  validate: (input) => {\n    const boxType = toUTF8String(input, 4, 8);\n    if (boxType !== \"JXL \") return false;\n    const ftypBox = findBox(input, \"ftyp\", 0);\n    if (!ftypBox) return false;\n    const brand = toUTF8String(input, ftypBox.offset + 8, ftypBox.offset + 12);\n    return brand === \"jxl \";\n  },\n  calculate(input) {\n    const codestream = extractCodestream(input);\n    if (codestream) return JXLStream.calculate(codestream);\n    throw new Error(\"No codestream found in JXL container\");\n  }\n};\n\n// lib/types/ktx.ts\nvar KTX = {\n  validate: (input) => {\n    const signature = toUTF8String(input, 1, 7);\n    return [\"KTX 11\", \"KTX 20\"].includes(signature);\n  },\n  calculate: (input) => {\n    const type = input[5] === 49 ? \"ktx\" : \"ktx2\";\n    const offset = type === \"ktx\" ? 36 : 20;\n    return {\n      height: readUInt32LE(input, offset + 4),\n      width: readUInt32LE(input, offset),\n      type\n    };\n  }\n};\n\n// lib/types/png.ts\nvar pngSignature = \"PNG\\r\\n\u001a\\n\";\nvar pngImageHeaderChunkName = \"IHDR\";\nvar pngFriedChunkName = \"CgBI\";\nvar PNG = {\n  validate(input) {\n    if (pngSignature === toUTF8String(input, 1, 8)) {\n      let chunkName = toUTF8String(input, 12, 16);\n      if (chunkName === pngFriedChunkName) {\n        chunkName = toUTF8String(input, 28, 32);\n      }\n      if (chunkName !== pngImageHeaderChunkName) {\n        throw new TypeError(\"Invalid PNG\");\n      }\n      return true;\n    }\n    return false;\n  },\n  calculate(input) {\n    if (toUTF8String(input, 12, 16) === pngFriedChunkName) {\n      return {\n        height: readUInt32BE(input, 36),\n        width: readUInt32BE(input, 32)\n      };\n    }\n    return {\n      height: readUInt32BE(input, 20),\n      width: readUInt32BE(input, 16)\n    };\n  }\n};\n\n// lib/types/pnm.ts\nvar PNMTypes = {\n  P1: \"pbm/ascii\",\n  P2: \"pgm/ascii\",\n  P3: \"ppm/ascii\",\n  P4: \"pbm\",\n  P5: \"pgm\",\n  P6: \"ppm\",\n  P7: \"pam\",\n  PF: \"pfm\"\n};\nvar handlers = {\n  default: (lines) => {\n    let dimensions = [];\n    while (lines.length > 0) {\n      const line = lines.shift();\n      if (line[0] === \"#\") {\n        continue;\n      }\n      dimensions = line.split(\" \");\n      break;\n    }\n    if (dimensions.length === 2) {\n      return {\n        height: Number.parseInt(dimensions[1], 10),\n        width: Number.parseInt(dimensions[0], 10)\n      };\n    }\n    throw new TypeError(\"Invalid PNM\");\n  },\n  pam: (lines) => {\n    const size = {};\n    while (lines.length > 0) {\n      const line = lines.shift();\n      if (line.length > 16 || line.charCodeAt(0) > 128) {\n        continue;\n      }\n      const [key, value] = line.split(\" \");\n      if (key && value) {\n        size[key.toLowerCase()] = Number.parseInt(value, 10);\n      }\n      if (size.height && size.width) {\n        break;\n      }\n    }\n    if (size.height && size.width) {\n      return {\n        height: size.height,\n        width: size.width\n      };\n    }\n    throw new TypeError(\"Invalid PAM\");\n  }\n};\nvar PNM = {\n  validate: (input) => toUTF8String(input, 0, 2) in PNMTypes,\n  calculate(input) {\n    const signature = toUTF8String(input, 0, 2);\n    const type = PNMTypes[signature];\n    const lines = toUTF8String(input, 3).split(/[\\r\\n]+/);\n    const handler = handlers[type] || handlers.default;\n    return handler(lines);\n  }\n};\n\n// lib/types/psd.ts\nvar PSD = {\n  validate: (input) => toUTF8String(input, 0, 4) === \"8BPS\",\n  calculate: (input) => ({\n    height: readUInt32BE(input, 14),\n    width: readUInt32BE(input, 18)\n  })\n};\n\n// lib/types/svg.ts\nvar svgReg = /<svg\\s([^>\"']|\"[^\"]*\"|'[^']*')*>/;\nvar extractorRegExps = {\n  height: /\\sheight=(['\"])([^%]+?)\\1/,\n  root: svgReg,\n  viewbox: /\\sviewBox=(['\"])(.+?)\\1/i,\n  width: /\\swidth=(['\"])([^%]+?)\\1/\n};\nvar INCH_CM = 2.54;\nvar units = {\n  in: 96,\n  cm: 96 / INCH_CM,\n  em: 16,\n  ex: 8,\n  m: 96 / INCH_CM * 100,\n  mm: 96 / INCH_CM / 10,\n  pc: 96 / 72 / 12,\n  pt: 96 / 72,\n  px: 1\n};\nvar unitsReg = new RegExp(\n  `^([0-9.]+(?:e\\\\d+)?)(${Object.keys(units).join(\"|\")})?$`\n);\nfunction parseLength(len) {\n  const m = unitsReg.exec(len);\n  if (!m) {\n    return void 0;\n  }\n  return Math.round(Number(m[1]) * (units[m[2]] || 1));\n}\nfunction parseViewbox(viewbox) {\n  const bounds = viewbox.split(\" \");\n  return {\n    height: parseLength(bounds[3]),\n    width: parseLength(bounds[2])\n  };\n}\nfunction parseAttributes(root) {\n  const width = root.match(extractorRegExps.width);\n  const height = root.match(extractorRegExps.height);\n  const viewbox = root.match(extractorRegExps.viewbox);\n  return {\n    height: height && parseLength(height[2]),\n    viewbox: viewbox && parseViewbox(viewbox[2]),\n    width: width && parseLength(width[2])\n  };\n}\nfunction calculateByDimensions(attrs) {\n  return {\n    height: attrs.height,\n    width: attrs.width\n  };\n}\nfunction calculateByViewbox(attrs, viewbox) {\n  const ratio = viewbox.width / viewbox.height;\n  if (attrs.width) {\n    return {\n      height: Math.floor(attrs.width / ratio),\n      width: attrs.width\n    };\n  }\n  if (attrs.height) {\n    return {\n      height: attrs.height,\n      width: Math.floor(attrs.height * ratio)\n    };\n  }\n  return {\n    height: viewbox.height,\n    width: viewbox.width\n  };\n}\nvar SVG = {\n  // Scan only the first kilo-byte to speed up the check on larger files\n  validate: (input) => svgReg.test(toUTF8String(input, 0, 1e3)),\n  calculate(input) {\n    const root = toUTF8String(input).match(extractorRegExps.root);\n    if (root) {\n      const attrs = parseAttributes(root[0]);\n      if (attrs.width && attrs.height) {\n        return calculateByDimensions(attrs);\n      }\n      if (attrs.viewbox) {\n        return calculateByViewbox(attrs, attrs.viewbox);\n      }\n    }\n    throw new TypeError(\"Invalid SVG\");\n  }\n};\n\n// lib/types/tga.ts\nvar TGA = {\n  validate(input) {\n    return readUInt16LE(input, 0) === 0 && readUInt16LE(input, 4) === 0;\n  },\n  calculate(input) {\n    return {\n      height: readUInt16LE(input, 14),\n      width: readUInt16LE(input, 12)\n    };\n  }\n};\n\n// lib/types/tiff.ts\nvar CONSTANTS = {\n  TAG: {\n    WIDTH: 256,\n    HEIGHT: 257,\n    COMPRESSION: 259\n  },\n  TYPE: {\n    SHORT: 3,\n    LONG: 4,\n    LONG8: 16\n  },\n  ENTRY_SIZE: {\n    STANDARD: 12,\n    BIG: 20\n  },\n  COUNT_SIZE: {\n    STANDARD: 2,\n    BIG: 8\n  }\n};\nfunction readIFD(input, { isBigEndian, isBigTiff }) {\n  const ifdOffset = isBigTiff ? Number(readUInt64(input, 8, isBigEndian)) : readUInt(input, 32, 4, isBigEndian);\n  const entryCountSize = isBigTiff ? CONSTANTS.COUNT_SIZE.BIG : CONSTANTS.COUNT_SIZE.STANDARD;\n  return input.slice(ifdOffset + entryCountSize);\n}\nfunction readTagValue(input, type, offset, isBigEndian) {\n  switch (type) {\n    case CONSTANTS.TYPE.SHORT:\n      return readUInt(input, 16, offset, isBigEndian);\n    case CONSTANTS.TYPE.LONG:\n      return readUInt(input, 32, offset, isBigEndian);\n    case CONSTANTS.TYPE.LONG8: {\n      const value = Number(readUInt64(input, offset, isBigEndian));\n      if (value > Number.MAX_SAFE_INTEGER) {\n        throw new TypeError(\"Value too large\");\n      }\n      return value;\n    }\n    default:\n      return 0;\n  }\n}\nfunction nextTag(input, isBigTiff) {\n  const entrySize = isBigTiff ? CONSTANTS.ENTRY_SIZE.BIG : CONSTANTS.ENTRY_SIZE.STANDARD;\n  if (input.length > entrySize) {\n    return input.slice(entrySize);\n  }\n}\nfunction extractTags(input, { isBigEndian, isBigTiff }) {\n  const tags = {};\n  let temp = input;\n  while (temp?.length) {\n    const code = readUInt(temp, 16, 0, isBigEndian);\n    const type = readUInt(temp, 16, 2, isBigEndian);\n    const length = isBigTiff ? Number(readUInt64(temp, 4, isBigEndian)) : readUInt(temp, 32, 4, isBigEndian);\n    if (code === 0) break;\n    if (length === 1 && (type === CONSTANTS.TYPE.SHORT || type === CONSTANTS.TYPE.LONG || isBigTiff && type === CONSTANTS.TYPE.LONG8)) {\n      const valueOffset = isBigTiff ? 12 : 8;\n      tags[code] = readTagValue(temp, type, valueOffset, isBigEndian);\n    }\n    temp = nextTag(temp, isBigTiff);\n  }\n  return tags;\n}\nfunction determineFormat(input) {\n  const signature = toUTF8String(input, 0, 2);\n  const version = readUInt(input, 16, 2, signature === \"MM\");\n  return {\n    isBigEndian: signature === \"MM\",\n    isBigTiff: version === 43\n  };\n}\nfunction validateBigTIFFHeader(input, isBigEndian) {\n  const byteSize = readUInt(input, 16, 4, isBigEndian);\n  const reserved = readUInt(input, 16, 6, isBigEndian);\n  if (byteSize !== 8 || reserved !== 0) {\n    throw new TypeError(\"Invalid BigTIFF header\");\n  }\n}\nvar signatures = /* @__PURE__ */ new Set([\n  \"49492a00\",\n  // Little Endian\n  \"4d4d002a\",\n  // Big Endian\n  \"49492b00\",\n  // BigTIFF Little Endian\n  \"4d4d002b\"\n  // BigTIFF Big Endian\n]);\nvar TIFF = {\n  validate: (input) => {\n    const signature = toHexString(input, 0, 4);\n    return signatures.has(signature);\n  },\n  calculate(input) {\n    const format = determineFormat(input);\n    if (format.isBigTiff) {\n      validateBigTIFFHeader(input, format.isBigEndian);\n    }\n    const ifdBuffer = readIFD(input, format);\n    const tags = extractTags(ifdBuffer, format);\n    const info = {\n      height: tags[CONSTANTS.TAG.HEIGHT],\n      width: tags[CONSTANTS.TAG.WIDTH],\n      type: format.isBigTiff ? \"bigtiff\" : \"tiff\"\n    };\n    if (tags[CONSTANTS.TAG.COMPRESSION]) {\n      info.compression = tags[CONSTANTS.TAG.COMPRESSION];\n    }\n    if (!info.width || !info.height) {\n      throw new TypeError(\"Invalid Tiff. Missing tags\");\n    }\n    return info;\n  }\n};\n\n// lib/types/webp.ts\nfunction calculateExtended(input) {\n  return {\n    height: 1 + readUInt24LE(input, 7),\n    width: 1 + readUInt24LE(input, 4)\n  };\n}\nfunction calculateLossless(input) {\n  return {\n    height: 1 + ((input[4] & 15) << 10 | input[3] << 2 | (input[2] & 192) >> 6),\n    width: 1 + ((input[2] & 63) << 8 | input[1])\n  };\n}\nfunction calculateLossy(input) {\n  return {\n    height: readInt16LE(input, 8) & 16383,\n    width: readInt16LE(input, 6) & 16383\n  };\n}\nvar WEBP = {\n  validate(input) {\n    const riffHeader = \"RIFF\" === toUTF8String(input, 0, 4);\n    const webpHeader = \"WEBP\" === toUTF8String(input, 8, 12);\n    const vp8Header = \"VP8\" === toUTF8String(input, 12, 15);\n    return riffHeader && webpHeader && vp8Header;\n  },\n  calculate(_input) {\n    const chunkHeader = toUTF8String(_input, 12, 16);\n    const input = _input.slice(20, 30);\n    if (chunkHeader === \"VP8X\") {\n      const extendedHeader = input[0];\n      const validStart = (extendedHeader & 192) === 0;\n      const validEnd = (extendedHeader & 1) === 0;\n      if (validStart && validEnd) {\n        return calculateExtended(input);\n      }\n      throw new TypeError(\"Invalid WebP\");\n    }\n    if (chunkHeader === \"VP8 \" && input[0] !== 47) {\n      return calculateLossy(input);\n    }\n    const signature = toHexString(input, 3, 6);\n    if (chunkHeader === \"VP8L\" && signature !== \"9d012a\") {\n      return calculateLossless(input);\n    }\n    throw new TypeError(\"Invalid WebP\");\n  }\n};\n\n// lib/types/index.ts\nvar typeHandlers = /* @__PURE__ */ new Map([\n  [\"bmp\", BMP],\n  [\"cur\", CUR],\n  [\"dds\", DDS],\n  [\"gif\", GIF],\n  [\"heif\", HEIF],\n  [\"icns\", ICNS],\n  [\"ico\", ICO],\n  [\"j2c\", J2C],\n  [\"jp2\", JP2],\n  [\"jpg\", JPG],\n  [\"jxl\", JXL],\n  [\"jxl-stream\", JXLStream],\n  [\"ktx\", KTX],\n  [\"png\", PNG],\n  [\"pnm\", PNM],\n  [\"psd\", PSD],\n  [\"svg\", SVG],\n  [\"tga\", TGA],\n  [\"tiff\", TIFF],\n  [\"webp\", WEBP]\n]);\nvar types = Array.from(typeHandlers.keys());\n\n// lib/detector.ts\nvar firstBytes = /* @__PURE__ */ new Map([\n  [0, \"heif\"],\n  [56, \"psd\"],\n  [66, \"bmp\"],\n  [68, \"dds\"],\n  [71, \"gif\"],\n  [73, \"tiff\"],\n  [77, \"tiff\"],\n  [82, \"webp\"],\n  [105, \"icns\"],\n  [137, \"png\"],\n  [255, \"jpg\"]\n]);\nfunction detector(input) {\n  const byte = input[0];\n  const type = firstBytes.get(byte);\n  if (type && typeHandlers.get(type).validate(input)) {\n    return type;\n  }\n  return types.find((type2) => typeHandlers.get(type2).validate(input));\n}\n\n// lib/lookup.ts\nvar globalOptions = {\n  disabledTypes: []\n};\nfunction imageSize(input) {\n  const type = detector(input);\n  if (typeof type !== \"undefined\") {\n    if (globalOptions.disabledTypes.indexOf(type) > -1) {\n      throw new TypeError(`disabled file type: ${type}`);\n    }\n    const size = typeHandlers.get(type).calculate(input);\n    if (size !== void 0) {\n      size.type = size.type ?? type;\n      if (size.images && size.images.length > 1) {\n        const largestImage = size.images.reduce((largest, current) => {\n          return current.width * current.height > largest.width * largest.height ? current : largest;\n        }, size.images[0]);\n        size.width = largestImage.width;\n        size.height = largestImage.height;\n      }\n      return size;\n    }\n  }\n  throw new TypeError(`unsupported file type: ${type}`);\n}\n\n// lib/fromFile.ts\nvar MaxInputSize = 512 * 1024;\nvar queue = [];\nvar concurrency = 100;\nvar setConcurrency = (c) => {\n  concurrency = c;\n};\nvar processQueue = async () => {\n  const jobs = queue.splice(0, concurrency);\n  const promises2 = jobs.map(async ({ filePath, resolve: resolve2, reject }) => {\n    let handle;\n    try {\n      handle = await node_fs__WEBPACK_IMPORTED_MODULE_0__.promises.open(node_path__WEBPACK_IMPORTED_MODULE_1__.resolve(filePath), \"r\");\n    } catch (err) {\n      return reject(err);\n    }\n    try {\n      const { size } = await handle.stat();\n      if (size <= 0) {\n        throw new Error(\"Empty file\");\n      }\n      const inputSize = Math.min(size, MaxInputSize);\n      const input = new Uint8Array(inputSize);\n      await handle.read(input, 0, inputSize, 0);\n      resolve2(imageSize(input));\n    } catch (err) {\n      reject(err);\n    } finally {\n      await handle.close();\n    }\n  });\n  await Promise.allSettled(promises2);\n  if (queue.length) setTimeout(processQueue, 100);\n};\nvar imageSizeFromFile = async (filePath) => new Promise((resolve2, reject) => {\n  queue.push({ filePath, resolve: resolve2, reject });\n  processQueue();\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/image-size/dist/fromFile.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/image-size/dist/index.mjs":
/*!************************************************!*\
  !*** ./node_modules/image-size/dist/index.mjs ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ imageSize),\n/* harmony export */   disableTypes: () => (/* binding */ disableTypes),\n/* harmony export */   imageSize: () => (/* binding */ imageSize),\n/* harmony export */   types: () => (/* binding */ types)\n/* harmony export */ });\n// lib/types/utils.ts\nvar decoder = new TextDecoder();\nvar toUTF8String = (input, start = 0, end = input.length) => decoder.decode(input.slice(start, end));\nvar toHexString = (input, start = 0, end = input.length) => input.slice(start, end).reduce((memo, i) => memo + `0${i.toString(16)}`.slice(-2), \"\");\nvar getView = (input, offset) => new DataView(input.buffer, input.byteOffset + offset);\nvar readInt16LE = (input, offset = 0) => getView(input, offset).getInt16(0, true);\nvar readUInt16BE = (input, offset = 0) => getView(input, offset).getUint16(0, false);\nvar readUInt16LE = (input, offset = 0) => getView(input, offset).getUint16(0, true);\nvar readUInt24LE = (input, offset = 0) => {\n  const view = getView(input, offset);\n  return view.getUint16(0, true) + (view.getUint8(2) << 16);\n};\nvar readInt32LE = (input, offset = 0) => getView(input, offset).getInt32(0, true);\nvar readUInt32BE = (input, offset = 0) => getView(input, offset).getUint32(0, false);\nvar readUInt32LE = (input, offset = 0) => getView(input, offset).getUint32(0, true);\nvar readUInt64 = (input, offset, isBigEndian) => getView(input, offset).getBigUint64(0, !isBigEndian);\nvar methods = {\n  readUInt16BE,\n  readUInt16LE,\n  readUInt32BE,\n  readUInt32LE\n};\nfunction readUInt(input, bits, offset = 0, isBigEndian = false) {\n  const endian = isBigEndian ? \"BE\" : \"LE\";\n  const methodName = `readUInt${bits}${endian}`;\n  return methods[methodName](input, offset);\n}\nfunction readBox(input, offset) {\n  if (input.length - offset < 4) return;\n  const boxSize = readUInt32BE(input, offset);\n  if (input.length - offset < boxSize) return;\n  return {\n    name: toUTF8String(input, 4 + offset, 8 + offset),\n    offset,\n    size: boxSize\n  };\n}\nfunction findBox(input, boxName, currentOffset) {\n  while (currentOffset < input.length) {\n    const box = readBox(input, currentOffset);\n    if (!box) break;\n    if (box.name === boxName) return box;\n    currentOffset += box.size > 0 ? box.size : 8;\n  }\n}\n\n// lib/types/bmp.ts\nvar BMP = {\n  validate: (input) => toUTF8String(input, 0, 2) === \"BM\",\n  calculate: (input) => ({\n    height: Math.abs(readInt32LE(input, 22)),\n    width: readUInt32LE(input, 18)\n  })\n};\n\n// lib/types/ico.ts\nvar TYPE_ICON = 1;\nvar SIZE_HEADER = 2 + 2 + 2;\nvar SIZE_IMAGE_ENTRY = 1 + 1 + 1 + 1 + 2 + 2 + 4 + 4;\nfunction getSizeFromOffset(input, offset) {\n  const value = input[offset];\n  return value === 0 ? 256 : value;\n}\nfunction getImageSize(input, imageIndex) {\n  const offset = SIZE_HEADER + imageIndex * SIZE_IMAGE_ENTRY;\n  return {\n    height: getSizeFromOffset(input, offset + 1),\n    width: getSizeFromOffset(input, offset)\n  };\n}\nvar ICO = {\n  validate(input) {\n    const reserved = readUInt16LE(input, 0);\n    const imageCount = readUInt16LE(input, 4);\n    if (reserved !== 0 || imageCount === 0) return false;\n    const imageType = readUInt16LE(input, 2);\n    return imageType === TYPE_ICON;\n  },\n  calculate(input) {\n    const nbImages = readUInt16LE(input, 4);\n    const imageSize2 = getImageSize(input, 0);\n    if (nbImages === 1) return imageSize2;\n    const images = [];\n    for (let imageIndex = 0; imageIndex < nbImages; imageIndex += 1) {\n      images.push(getImageSize(input, imageIndex));\n    }\n    return {\n      width: imageSize2.width,\n      height: imageSize2.height,\n      images\n    };\n  }\n};\n\n// lib/types/cur.ts\nvar TYPE_CURSOR = 2;\nvar CUR = {\n  validate(input) {\n    const reserved = readUInt16LE(input, 0);\n    const imageCount = readUInt16LE(input, 4);\n    if (reserved !== 0 || imageCount === 0) return false;\n    const imageType = readUInt16LE(input, 2);\n    return imageType === TYPE_CURSOR;\n  },\n  calculate: (input) => ICO.calculate(input)\n};\n\n// lib/types/dds.ts\nvar DDS = {\n  validate: (input) => readUInt32LE(input, 0) === 542327876,\n  calculate: (input) => ({\n    height: readUInt32LE(input, 12),\n    width: readUInt32LE(input, 16)\n  })\n};\n\n// lib/types/gif.ts\nvar gifRegexp = /^GIF8[79]a/;\nvar GIF = {\n  validate: (input) => gifRegexp.test(toUTF8String(input, 0, 6)),\n  calculate: (input) => ({\n    height: readUInt16LE(input, 8),\n    width: readUInt16LE(input, 6)\n  })\n};\n\n// lib/types/heif.ts\nvar brandMap = {\n  avif: \"avif\",\n  mif1: \"heif\",\n  msf1: \"heif\",\n  // heif-sequence\n  heic: \"heic\",\n  heix: \"heic\",\n  hevc: \"heic\",\n  // heic-sequence\n  hevx: \"heic\"\n  // heic-sequence\n};\nvar HEIF = {\n  validate(input) {\n    const boxType = toUTF8String(input, 4, 8);\n    if (boxType !== \"ftyp\") return false;\n    const ftypBox = findBox(input, \"ftyp\", 0);\n    if (!ftypBox) return false;\n    const brand = toUTF8String(input, ftypBox.offset + 8, ftypBox.offset + 12);\n    return brand in brandMap;\n  },\n  calculate(input) {\n    const metaBox = findBox(input, \"meta\", 0);\n    const iprpBox = metaBox && findBox(input, \"iprp\", metaBox.offset + 12);\n    const ipcoBox = iprpBox && findBox(input, \"ipco\", iprpBox.offset + 8);\n    if (!ipcoBox) {\n      throw new TypeError(\"Invalid HEIF, no ipco box found\");\n    }\n    const type = toUTF8String(input, 8, 12);\n    const images = [];\n    let currentOffset = ipcoBox.offset + 8;\n    while (currentOffset < ipcoBox.offset + ipcoBox.size) {\n      const ispeBox = findBox(input, \"ispe\", currentOffset);\n      if (!ispeBox) break;\n      const rawWidth = readUInt32BE(input, ispeBox.offset + 12);\n      const rawHeight = readUInt32BE(input, ispeBox.offset + 16);\n      const clapBox = findBox(input, \"clap\", currentOffset);\n      let width = rawWidth;\n      let height = rawHeight;\n      if (clapBox && clapBox.offset < ipcoBox.offset + ipcoBox.size) {\n        const cropRight = readUInt32BE(input, clapBox.offset + 12);\n        width = rawWidth - cropRight;\n      }\n      images.push({ height, width });\n      currentOffset = ispeBox.offset + ispeBox.size;\n    }\n    if (images.length === 0) {\n      throw new TypeError(\"Invalid HEIF, no sizes found\");\n    }\n    return {\n      width: images[0].width,\n      height: images[0].height,\n      type,\n      ...images.length > 1 ? { images } : {}\n    };\n  }\n};\n\n// lib/types/icns.ts\nvar SIZE_HEADER2 = 4 + 4;\nvar FILE_LENGTH_OFFSET = 4;\nvar ENTRY_LENGTH_OFFSET = 4;\nvar ICON_TYPE_SIZE = {\n  ICON: 32,\n  \"ICN#\": 32,\n  // m => 16 x 16\n  \"icm#\": 16,\n  icm4: 16,\n  icm8: 16,\n  // s => 16 x 16\n  \"ics#\": 16,\n  ics4: 16,\n  ics8: 16,\n  is32: 16,\n  s8mk: 16,\n  icp4: 16,\n  // l => 32 x 32\n  icl4: 32,\n  icl8: 32,\n  il32: 32,\n  l8mk: 32,\n  icp5: 32,\n  ic11: 32,\n  // h => 48 x 48\n  ich4: 48,\n  ich8: 48,\n  ih32: 48,\n  h8mk: 48,\n  // . => 64 x 64\n  icp6: 64,\n  ic12: 32,\n  // t => 128 x 128\n  it32: 128,\n  t8mk: 128,\n  ic07: 128,\n  // . => 256 x 256\n  ic08: 256,\n  ic13: 256,\n  // . => 512 x 512\n  ic09: 512,\n  ic14: 512,\n  // . => 1024 x 1024\n  ic10: 1024\n};\nfunction readImageHeader(input, imageOffset) {\n  const imageLengthOffset = imageOffset + ENTRY_LENGTH_OFFSET;\n  return [\n    toUTF8String(input, imageOffset, imageLengthOffset),\n    readUInt32BE(input, imageLengthOffset)\n  ];\n}\nfunction getImageSize2(type) {\n  const size = ICON_TYPE_SIZE[type];\n  return { width: size, height: size, type };\n}\nvar ICNS = {\n  validate: (input) => toUTF8String(input, 0, 4) === \"icns\",\n  calculate(input) {\n    const inputLength = input.length;\n    const fileLength = readUInt32BE(input, FILE_LENGTH_OFFSET);\n    let imageOffset = SIZE_HEADER2;\n    const images = [];\n    while (imageOffset < fileLength && imageOffset < inputLength) {\n      const imageHeader = readImageHeader(input, imageOffset);\n      const imageSize2 = getImageSize2(imageHeader[0]);\n      images.push(imageSize2);\n      imageOffset += imageHeader[1];\n    }\n    if (images.length === 0) {\n      throw new TypeError(\"Invalid ICNS, no sizes found\");\n    }\n    return {\n      width: images[0].width,\n      height: images[0].height,\n      ...images.length > 1 ? { images } : {}\n    };\n  }\n};\n\n// lib/types/j2c.ts\nvar J2C = {\n  // TODO: this doesn't seem right. SIZ marker doesn't have to be right after the SOC\n  validate: (input) => readUInt32BE(input, 0) === 4283432785,\n  calculate: (input) => ({\n    height: readUInt32BE(input, 12),\n    width: readUInt32BE(input, 8)\n  })\n};\n\n// lib/types/jp2.ts\nvar JP2 = {\n  validate(input) {\n    const boxType = toUTF8String(input, 4, 8);\n    if (boxType !== \"jP  \") return false;\n    const ftypBox = findBox(input, \"ftyp\", 0);\n    if (!ftypBox) return false;\n    const brand = toUTF8String(input, ftypBox.offset + 8, ftypBox.offset + 12);\n    return brand === \"jp2 \";\n  },\n  calculate(input) {\n    const jp2hBox = findBox(input, \"jp2h\", 0);\n    const ihdrBox = jp2hBox && findBox(input, \"ihdr\", jp2hBox.offset + 8);\n    if (ihdrBox) {\n      return {\n        height: readUInt32BE(input, ihdrBox.offset + 8),\n        width: readUInt32BE(input, ihdrBox.offset + 12)\n      };\n    }\n    throw new TypeError(\"Unsupported JPEG 2000 format\");\n  }\n};\n\n// lib/types/jpg.ts\nvar EXIF_MARKER = \"45786966\";\nvar APP1_DATA_SIZE_BYTES = 2;\nvar EXIF_HEADER_BYTES = 6;\nvar TIFF_BYTE_ALIGN_BYTES = 2;\nvar BIG_ENDIAN_BYTE_ALIGN = \"4d4d\";\nvar LITTLE_ENDIAN_BYTE_ALIGN = \"4949\";\nvar IDF_ENTRY_BYTES = 12;\nvar NUM_DIRECTORY_ENTRIES_BYTES = 2;\nfunction isEXIF(input) {\n  return toHexString(input, 2, 6) === EXIF_MARKER;\n}\nfunction extractSize(input, index) {\n  return {\n    height: readUInt16BE(input, index),\n    width: readUInt16BE(input, index + 2)\n  };\n}\nfunction extractOrientation(exifBlock, isBigEndian) {\n  const idfOffset = 8;\n  const offset = EXIF_HEADER_BYTES + idfOffset;\n  const idfDirectoryEntries = readUInt(exifBlock, 16, offset, isBigEndian);\n  for (let directoryEntryNumber = 0; directoryEntryNumber < idfDirectoryEntries; directoryEntryNumber++) {\n    const start = offset + NUM_DIRECTORY_ENTRIES_BYTES + directoryEntryNumber * IDF_ENTRY_BYTES;\n    const end = start + IDF_ENTRY_BYTES;\n    if (start > exifBlock.length) {\n      return;\n    }\n    const block = exifBlock.slice(start, end);\n    const tagNumber = readUInt(block, 16, 0, isBigEndian);\n    if (tagNumber === 274) {\n      const dataFormat = readUInt(block, 16, 2, isBigEndian);\n      if (dataFormat !== 3) {\n        return;\n      }\n      const numberOfComponents = readUInt(block, 32, 4, isBigEndian);\n      if (numberOfComponents !== 1) {\n        return;\n      }\n      return readUInt(block, 16, 8, isBigEndian);\n    }\n  }\n}\nfunction validateExifBlock(input, index) {\n  const exifBlock = input.slice(APP1_DATA_SIZE_BYTES, index);\n  const byteAlign = toHexString(\n    exifBlock,\n    EXIF_HEADER_BYTES,\n    EXIF_HEADER_BYTES + TIFF_BYTE_ALIGN_BYTES\n  );\n  const isBigEndian = byteAlign === BIG_ENDIAN_BYTE_ALIGN;\n  const isLittleEndian = byteAlign === LITTLE_ENDIAN_BYTE_ALIGN;\n  if (isBigEndian || isLittleEndian) {\n    return extractOrientation(exifBlock, isBigEndian);\n  }\n}\nfunction validateInput(input, index) {\n  if (index > input.length) {\n    throw new TypeError(\"Corrupt JPG, exceeded buffer limits\");\n  }\n}\nvar JPG = {\n  validate: (input) => toHexString(input, 0, 2) === \"ffd8\",\n  calculate(_input) {\n    let input = _input.slice(4);\n    let orientation;\n    let next;\n    while (input.length) {\n      const i = readUInt16BE(input, 0);\n      validateInput(input, i);\n      if (input[i] !== 255) {\n        input = input.slice(1);\n        continue;\n      }\n      if (isEXIF(input)) {\n        orientation = validateExifBlock(input, i);\n      }\n      next = input[i + 1];\n      if (next === 192 || next === 193 || next === 194) {\n        const size = extractSize(input, i + 5);\n        if (!orientation) {\n          return size;\n        }\n        return {\n          height: size.height,\n          orientation,\n          width: size.width\n        };\n      }\n      input = input.slice(i + 2);\n    }\n    throw new TypeError(\"Invalid JPG, no size found\");\n  }\n};\n\n// lib/utils/bit-reader.ts\nvar BitReader = class {\n  constructor(input, endianness) {\n    this.input = input;\n    this.endianness = endianness;\n    // Skip the first 16 bits (2 bytes) of signature\n    this.byteOffset = 2;\n    this.bitOffset = 0;\n  }\n  /** Reads a specified number of bits, and move the offset */\n  getBits(length = 1) {\n    let result = 0;\n    let bitsRead = 0;\n    while (bitsRead < length) {\n      if (this.byteOffset >= this.input.length) {\n        throw new Error(\"Reached end of input\");\n      }\n      const currentByte = this.input[this.byteOffset];\n      const bitsLeft = 8 - this.bitOffset;\n      const bitsToRead = Math.min(length - bitsRead, bitsLeft);\n      if (this.endianness === \"little-endian\") {\n        const mask = (1 << bitsToRead) - 1;\n        const bits = currentByte >> this.bitOffset & mask;\n        result |= bits << bitsRead;\n      } else {\n        const mask = (1 << bitsToRead) - 1 << 8 - this.bitOffset - bitsToRead;\n        const bits = (currentByte & mask) >> 8 - this.bitOffset - bitsToRead;\n        result = result << bitsToRead | bits;\n      }\n      bitsRead += bitsToRead;\n      this.bitOffset += bitsToRead;\n      if (this.bitOffset === 8) {\n        this.byteOffset++;\n        this.bitOffset = 0;\n      }\n    }\n    return result;\n  }\n};\n\n// lib/types/jxl-stream.ts\nfunction calculateImageDimension(reader, isSmallImage) {\n  if (isSmallImage) {\n    return 8 * (1 + reader.getBits(5));\n  }\n  const sizeClass = reader.getBits(2);\n  const extraBits = [9, 13, 18, 30][sizeClass];\n  return 1 + reader.getBits(extraBits);\n}\nfunction calculateImageWidth(reader, isSmallImage, widthMode, height) {\n  if (isSmallImage && widthMode === 0) {\n    return 8 * (1 + reader.getBits(5));\n  }\n  if (widthMode === 0) {\n    return calculateImageDimension(reader, false);\n  }\n  const aspectRatios = [1, 1.2, 4 / 3, 1.5, 16 / 9, 5 / 4, 2];\n  return Math.floor(height * aspectRatios[widthMode - 1]);\n}\nvar JXLStream = {\n  validate: (input) => {\n    return toHexString(input, 0, 2) === \"ff0a\";\n  },\n  calculate(input) {\n    const reader = new BitReader(input, \"little-endian\");\n    const isSmallImage = reader.getBits(1) === 1;\n    const height = calculateImageDimension(reader, isSmallImage);\n    const widthMode = reader.getBits(3);\n    const width = calculateImageWidth(reader, isSmallImage, widthMode, height);\n    return { width, height };\n  }\n};\n\n// lib/types/jxl.ts\nfunction extractCodestream(input) {\n  const jxlcBox = findBox(input, \"jxlc\", 0);\n  if (jxlcBox) {\n    return input.slice(jxlcBox.offset + 8, jxlcBox.offset + jxlcBox.size);\n  }\n  const partialStreams = extractPartialStreams(input);\n  if (partialStreams.length > 0) {\n    return concatenateCodestreams(partialStreams);\n  }\n  return void 0;\n}\nfunction extractPartialStreams(input) {\n  const partialStreams = [];\n  let offset = 0;\n  while (offset < input.length) {\n    const jxlpBox = findBox(input, \"jxlp\", offset);\n    if (!jxlpBox) break;\n    partialStreams.push(\n      input.slice(jxlpBox.offset + 12, jxlpBox.offset + jxlpBox.size)\n    );\n    offset = jxlpBox.offset + jxlpBox.size;\n  }\n  return partialStreams;\n}\nfunction concatenateCodestreams(partialCodestreams) {\n  const totalLength = partialCodestreams.reduce(\n    (acc, curr) => acc + curr.length,\n    0\n  );\n  const codestream = new Uint8Array(totalLength);\n  let position = 0;\n  for (const partial of partialCodestreams) {\n    codestream.set(partial, position);\n    position += partial.length;\n  }\n  return codestream;\n}\nvar JXL = {\n  validate: (input) => {\n    const boxType = toUTF8String(input, 4, 8);\n    if (boxType !== \"JXL \") return false;\n    const ftypBox = findBox(input, \"ftyp\", 0);\n    if (!ftypBox) return false;\n    const brand = toUTF8String(input, ftypBox.offset + 8, ftypBox.offset + 12);\n    return brand === \"jxl \";\n  },\n  calculate(input) {\n    const codestream = extractCodestream(input);\n    if (codestream) return JXLStream.calculate(codestream);\n    throw new Error(\"No codestream found in JXL container\");\n  }\n};\n\n// lib/types/ktx.ts\nvar KTX = {\n  validate: (input) => {\n    const signature = toUTF8String(input, 1, 7);\n    return [\"KTX 11\", \"KTX 20\"].includes(signature);\n  },\n  calculate: (input) => {\n    const type = input[5] === 49 ? \"ktx\" : \"ktx2\";\n    const offset = type === \"ktx\" ? 36 : 20;\n    return {\n      height: readUInt32LE(input, offset + 4),\n      width: readUInt32LE(input, offset),\n      type\n    };\n  }\n};\n\n// lib/types/png.ts\nvar pngSignature = \"PNG\\r\\n\u001a\\n\";\nvar pngImageHeaderChunkName = \"IHDR\";\nvar pngFriedChunkName = \"CgBI\";\nvar PNG = {\n  validate(input) {\n    if (pngSignature === toUTF8String(input, 1, 8)) {\n      let chunkName = toUTF8String(input, 12, 16);\n      if (chunkName === pngFriedChunkName) {\n        chunkName = toUTF8String(input, 28, 32);\n      }\n      if (chunkName !== pngImageHeaderChunkName) {\n        throw new TypeError(\"Invalid PNG\");\n      }\n      return true;\n    }\n    return false;\n  },\n  calculate(input) {\n    if (toUTF8String(input, 12, 16) === pngFriedChunkName) {\n      return {\n        height: readUInt32BE(input, 36),\n        width: readUInt32BE(input, 32)\n      };\n    }\n    return {\n      height: readUInt32BE(input, 20),\n      width: readUInt32BE(input, 16)\n    };\n  }\n};\n\n// lib/types/pnm.ts\nvar PNMTypes = {\n  P1: \"pbm/ascii\",\n  P2: \"pgm/ascii\",\n  P3: \"ppm/ascii\",\n  P4: \"pbm\",\n  P5: \"pgm\",\n  P6: \"ppm\",\n  P7: \"pam\",\n  PF: \"pfm\"\n};\nvar handlers = {\n  default: (lines) => {\n    let dimensions = [];\n    while (lines.length > 0) {\n      const line = lines.shift();\n      if (line[0] === \"#\") {\n        continue;\n      }\n      dimensions = line.split(\" \");\n      break;\n    }\n    if (dimensions.length === 2) {\n      return {\n        height: Number.parseInt(dimensions[1], 10),\n        width: Number.parseInt(dimensions[0], 10)\n      };\n    }\n    throw new TypeError(\"Invalid PNM\");\n  },\n  pam: (lines) => {\n    const size = {};\n    while (lines.length > 0) {\n      const line = lines.shift();\n      if (line.length > 16 || line.charCodeAt(0) > 128) {\n        continue;\n      }\n      const [key, value] = line.split(\" \");\n      if (key && value) {\n        size[key.toLowerCase()] = Number.parseInt(value, 10);\n      }\n      if (size.height && size.width) {\n        break;\n      }\n    }\n    if (size.height && size.width) {\n      return {\n        height: size.height,\n        width: size.width\n      };\n    }\n    throw new TypeError(\"Invalid PAM\");\n  }\n};\nvar PNM = {\n  validate: (input) => toUTF8String(input, 0, 2) in PNMTypes,\n  calculate(input) {\n    const signature = toUTF8String(input, 0, 2);\n    const type = PNMTypes[signature];\n    const lines = toUTF8String(input, 3).split(/[\\r\\n]+/);\n    const handler = handlers[type] || handlers.default;\n    return handler(lines);\n  }\n};\n\n// lib/types/psd.ts\nvar PSD = {\n  validate: (input) => toUTF8String(input, 0, 4) === \"8BPS\",\n  calculate: (input) => ({\n    height: readUInt32BE(input, 14),\n    width: readUInt32BE(input, 18)\n  })\n};\n\n// lib/types/svg.ts\nvar svgReg = /<svg\\s([^>\"']|\"[^\"]*\"|'[^']*')*>/;\nvar extractorRegExps = {\n  height: /\\sheight=(['\"])([^%]+?)\\1/,\n  root: svgReg,\n  viewbox: /\\sviewBox=(['\"])(.+?)\\1/i,\n  width: /\\swidth=(['\"])([^%]+?)\\1/\n};\nvar INCH_CM = 2.54;\nvar units = {\n  in: 96,\n  cm: 96 / INCH_CM,\n  em: 16,\n  ex: 8,\n  m: 96 / INCH_CM * 100,\n  mm: 96 / INCH_CM / 10,\n  pc: 96 / 72 / 12,\n  pt: 96 / 72,\n  px: 1\n};\nvar unitsReg = new RegExp(\n  `^([0-9.]+(?:e\\\\d+)?)(${Object.keys(units).join(\"|\")})?$`\n);\nfunction parseLength(len) {\n  const m = unitsReg.exec(len);\n  if (!m) {\n    return void 0;\n  }\n  return Math.round(Number(m[1]) * (units[m[2]] || 1));\n}\nfunction parseViewbox(viewbox) {\n  const bounds = viewbox.split(\" \");\n  return {\n    height: parseLength(bounds[3]),\n    width: parseLength(bounds[2])\n  };\n}\nfunction parseAttributes(root) {\n  const width = root.match(extractorRegExps.width);\n  const height = root.match(extractorRegExps.height);\n  const viewbox = root.match(extractorRegExps.viewbox);\n  return {\n    height: height && parseLength(height[2]),\n    viewbox: viewbox && parseViewbox(viewbox[2]),\n    width: width && parseLength(width[2])\n  };\n}\nfunction calculateByDimensions(attrs) {\n  return {\n    height: attrs.height,\n    width: attrs.width\n  };\n}\nfunction calculateByViewbox(attrs, viewbox) {\n  const ratio = viewbox.width / viewbox.height;\n  if (attrs.width) {\n    return {\n      height: Math.floor(attrs.width / ratio),\n      width: attrs.width\n    };\n  }\n  if (attrs.height) {\n    return {\n      height: attrs.height,\n      width: Math.floor(attrs.height * ratio)\n    };\n  }\n  return {\n    height: viewbox.height,\n    width: viewbox.width\n  };\n}\nvar SVG = {\n  // Scan only the first kilo-byte to speed up the check on larger files\n  validate: (input) => svgReg.test(toUTF8String(input, 0, 1e3)),\n  calculate(input) {\n    const root = toUTF8String(input).match(extractorRegExps.root);\n    if (root) {\n      const attrs = parseAttributes(root[0]);\n      if (attrs.width && attrs.height) {\n        return calculateByDimensions(attrs);\n      }\n      if (attrs.viewbox) {\n        return calculateByViewbox(attrs, attrs.viewbox);\n      }\n    }\n    throw new TypeError(\"Invalid SVG\");\n  }\n};\n\n// lib/types/tga.ts\nvar TGA = {\n  validate(input) {\n    return readUInt16LE(input, 0) === 0 && readUInt16LE(input, 4) === 0;\n  },\n  calculate(input) {\n    return {\n      height: readUInt16LE(input, 14),\n      width: readUInt16LE(input, 12)\n    };\n  }\n};\n\n// lib/types/tiff.ts\nvar CONSTANTS = {\n  TAG: {\n    WIDTH: 256,\n    HEIGHT: 257,\n    COMPRESSION: 259\n  },\n  TYPE: {\n    SHORT: 3,\n    LONG: 4,\n    LONG8: 16\n  },\n  ENTRY_SIZE: {\n    STANDARD: 12,\n    BIG: 20\n  },\n  COUNT_SIZE: {\n    STANDARD: 2,\n    BIG: 8\n  }\n};\nfunction readIFD(input, { isBigEndian, isBigTiff }) {\n  const ifdOffset = isBigTiff ? Number(readUInt64(input, 8, isBigEndian)) : readUInt(input, 32, 4, isBigEndian);\n  const entryCountSize = isBigTiff ? CONSTANTS.COUNT_SIZE.BIG : CONSTANTS.COUNT_SIZE.STANDARD;\n  return input.slice(ifdOffset + entryCountSize);\n}\nfunction readTagValue(input, type, offset, isBigEndian) {\n  switch (type) {\n    case CONSTANTS.TYPE.SHORT:\n      return readUInt(input, 16, offset, isBigEndian);\n    case CONSTANTS.TYPE.LONG:\n      return readUInt(input, 32, offset, isBigEndian);\n    case CONSTANTS.TYPE.LONG8: {\n      const value = Number(readUInt64(input, offset, isBigEndian));\n      if (value > Number.MAX_SAFE_INTEGER) {\n        throw new TypeError(\"Value too large\");\n      }\n      return value;\n    }\n    default:\n      return 0;\n  }\n}\nfunction nextTag(input, isBigTiff) {\n  const entrySize = isBigTiff ? CONSTANTS.ENTRY_SIZE.BIG : CONSTANTS.ENTRY_SIZE.STANDARD;\n  if (input.length > entrySize) {\n    return input.slice(entrySize);\n  }\n}\nfunction extractTags(input, { isBigEndian, isBigTiff }) {\n  const tags = {};\n  let temp = input;\n  while (temp?.length) {\n    const code = readUInt(temp, 16, 0, isBigEndian);\n    const type = readUInt(temp, 16, 2, isBigEndian);\n    const length = isBigTiff ? Number(readUInt64(temp, 4, isBigEndian)) : readUInt(temp, 32, 4, isBigEndian);\n    if (code === 0) break;\n    if (length === 1 && (type === CONSTANTS.TYPE.SHORT || type === CONSTANTS.TYPE.LONG || isBigTiff && type === CONSTANTS.TYPE.LONG8)) {\n      const valueOffset = isBigTiff ? 12 : 8;\n      tags[code] = readTagValue(temp, type, valueOffset, isBigEndian);\n    }\n    temp = nextTag(temp, isBigTiff);\n  }\n  return tags;\n}\nfunction determineFormat(input) {\n  const signature = toUTF8String(input, 0, 2);\n  const version = readUInt(input, 16, 2, signature === \"MM\");\n  return {\n    isBigEndian: signature === \"MM\",\n    isBigTiff: version === 43\n  };\n}\nfunction validateBigTIFFHeader(input, isBigEndian) {\n  const byteSize = readUInt(input, 16, 4, isBigEndian);\n  const reserved = readUInt(input, 16, 6, isBigEndian);\n  if (byteSize !== 8 || reserved !== 0) {\n    throw new TypeError(\"Invalid BigTIFF header\");\n  }\n}\nvar signatures = /* @__PURE__ */ new Set([\n  \"49492a00\",\n  // Little Endian\n  \"4d4d002a\",\n  // Big Endian\n  \"49492b00\",\n  // BigTIFF Little Endian\n  \"4d4d002b\"\n  // BigTIFF Big Endian\n]);\nvar TIFF = {\n  validate: (input) => {\n    const signature = toHexString(input, 0, 4);\n    return signatures.has(signature);\n  },\n  calculate(input) {\n    const format = determineFormat(input);\n    if (format.isBigTiff) {\n      validateBigTIFFHeader(input, format.isBigEndian);\n    }\n    const ifdBuffer = readIFD(input, format);\n    const tags = extractTags(ifdBuffer, format);\n    const info = {\n      height: tags[CONSTANTS.TAG.HEIGHT],\n      width: tags[CONSTANTS.TAG.WIDTH],\n      type: format.isBigTiff ? \"bigtiff\" : \"tiff\"\n    };\n    if (tags[CONSTANTS.TAG.COMPRESSION]) {\n      info.compression = tags[CONSTANTS.TAG.COMPRESSION];\n    }\n    if (!info.width || !info.height) {\n      throw new TypeError(\"Invalid Tiff. Missing tags\");\n    }\n    return info;\n  }\n};\n\n// lib/types/webp.ts\nfunction calculateExtended(input) {\n  return {\n    height: 1 + readUInt24LE(input, 7),\n    width: 1 + readUInt24LE(input, 4)\n  };\n}\nfunction calculateLossless(input) {\n  return {\n    height: 1 + ((input[4] & 15) << 10 | input[3] << 2 | (input[2] & 192) >> 6),\n    width: 1 + ((input[2] & 63) << 8 | input[1])\n  };\n}\nfunction calculateLossy(input) {\n  return {\n    height: readInt16LE(input, 8) & 16383,\n    width: readInt16LE(input, 6) & 16383\n  };\n}\nvar WEBP = {\n  validate(input) {\n    const riffHeader = \"RIFF\" === toUTF8String(input, 0, 4);\n    const webpHeader = \"WEBP\" === toUTF8String(input, 8, 12);\n    const vp8Header = \"VP8\" === toUTF8String(input, 12, 15);\n    return riffHeader && webpHeader && vp8Header;\n  },\n  calculate(_input) {\n    const chunkHeader = toUTF8String(_input, 12, 16);\n    const input = _input.slice(20, 30);\n    if (chunkHeader === \"VP8X\") {\n      const extendedHeader = input[0];\n      const validStart = (extendedHeader & 192) === 0;\n      const validEnd = (extendedHeader & 1) === 0;\n      if (validStart && validEnd) {\n        return calculateExtended(input);\n      }\n      throw new TypeError(\"Invalid WebP\");\n    }\n    if (chunkHeader === \"VP8 \" && input[0] !== 47) {\n      return calculateLossy(input);\n    }\n    const signature = toHexString(input, 3, 6);\n    if (chunkHeader === \"VP8L\" && signature !== \"9d012a\") {\n      return calculateLossless(input);\n    }\n    throw new TypeError(\"Invalid WebP\");\n  }\n};\n\n// lib/types/index.ts\nvar typeHandlers = /* @__PURE__ */ new Map([\n  [\"bmp\", BMP],\n  [\"cur\", CUR],\n  [\"dds\", DDS],\n  [\"gif\", GIF],\n  [\"heif\", HEIF],\n  [\"icns\", ICNS],\n  [\"ico\", ICO],\n  [\"j2c\", J2C],\n  [\"jp2\", JP2],\n  [\"jpg\", JPG],\n  [\"jxl\", JXL],\n  [\"jxl-stream\", JXLStream],\n  [\"ktx\", KTX],\n  [\"png\", PNG],\n  [\"pnm\", PNM],\n  [\"psd\", PSD],\n  [\"svg\", SVG],\n  [\"tga\", TGA],\n  [\"tiff\", TIFF],\n  [\"webp\", WEBP]\n]);\nvar types = Array.from(typeHandlers.keys());\n\n// lib/detector.ts\nvar firstBytes = /* @__PURE__ */ new Map([\n  [0, \"heif\"],\n  [56, \"psd\"],\n  [66, \"bmp\"],\n  [68, \"dds\"],\n  [71, \"gif\"],\n  [73, \"tiff\"],\n  [77, \"tiff\"],\n  [82, \"webp\"],\n  [105, \"icns\"],\n  [137, \"png\"],\n  [255, \"jpg\"]\n]);\nfunction detector(input) {\n  const byte = input[0];\n  const type = firstBytes.get(byte);\n  if (type && typeHandlers.get(type).validate(input)) {\n    return type;\n  }\n  return types.find((type2) => typeHandlers.get(type2).validate(input));\n}\n\n// lib/lookup.ts\nvar globalOptions = {\n  disabledTypes: []\n};\nfunction imageSize(input) {\n  const type = detector(input);\n  if (typeof type !== \"undefined\") {\n    if (globalOptions.disabledTypes.indexOf(type) > -1) {\n      throw new TypeError(`disabled file type: ${type}`);\n    }\n    const size = typeHandlers.get(type).calculate(input);\n    if (size !== void 0) {\n      size.type = size.type ?? type;\n      if (size.images && size.images.length > 1) {\n        const largestImage = size.images.reduce((largest, current) => {\n          return current.width * current.height > largest.width * largest.height ? current : largest;\n        }, size.images[0]);\n        size.width = largestImage.width;\n        size.height = largestImage.height;\n      }\n      return size;\n    }\n  }\n  throw new TypeError(`unsupported file type: ${type}`);\n}\nvar disableTypes = (types2) => {\n  globalOptions.disabledTypes = types2;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/image-size/dist/index.mjs\n");

/***/ })

};
;