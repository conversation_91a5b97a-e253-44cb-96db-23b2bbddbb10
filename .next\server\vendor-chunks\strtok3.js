"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/strtok3";
exports.ids = ["vendor-chunks/strtok3"];
exports.modules = {

/***/ "(rsc)/./node_modules/strtok3/lib/AbstractTokenizer.js":
/*!*******************************************************!*\
  !*** ./node_modules/strtok3/lib/AbstractTokenizer.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AbstractTokenizer: () => (/* binding */ AbstractTokenizer)\n/* harmony export */ });\n/* harmony import */ var peek_readable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! peek-readable */ \"(rsc)/./node_modules/peek-readable/lib/index.js\");\n\n/**\n * Core tokenizer\n */\nclass AbstractTokenizer {\n    /**\n     * Constructor\n     * @param options Tokenizer options\n     * @protected\n     */\n    constructor(options) {\n        /**\n         * Tokenizer-stream position\n         */\n        this.position = 0;\n        this.numBuffer = new Uint8Array(8);\n        this.fileInfo = options?.fileInfo ?? {};\n        this.onClose = options?.onClose;\n    }\n    /**\n     * Read a token from the tokenizer-stream\n     * @param token - The token to read\n     * @param position - If provided, the desired position in the tokenizer-stream\n     * @returns Promise with token data\n     */\n    async readToken(token, position = this.position) {\n        const uint8Array = new Uint8Array(token.len);\n        const len = await this.readBuffer(uint8Array, { position });\n        if (len < token.len)\n            throw new peek_readable__WEBPACK_IMPORTED_MODULE_0__.EndOfStreamError();\n        return token.get(uint8Array, 0);\n    }\n    /**\n     * Peek a token from the tokenizer-stream.\n     * @param token - Token to peek from the tokenizer-stream.\n     * @param position - Offset where to begin reading within the file. If position is null, data will be read from the current file position.\n     * @returns Promise with token data\n     */\n    async peekToken(token, position = this.position) {\n        const uint8Array = new Uint8Array(token.len);\n        const len = await this.peekBuffer(uint8Array, { position });\n        if (len < token.len)\n            throw new peek_readable__WEBPACK_IMPORTED_MODULE_0__.EndOfStreamError();\n        return token.get(uint8Array, 0);\n    }\n    /**\n     * Read a numeric token from the stream\n     * @param token - Numeric token\n     * @returns Promise with number\n     */\n    async readNumber(token) {\n        const len = await this.readBuffer(this.numBuffer, { length: token.len });\n        if (len < token.len)\n            throw new peek_readable__WEBPACK_IMPORTED_MODULE_0__.EndOfStreamError();\n        return token.get(this.numBuffer, 0);\n    }\n    /**\n     * Read a numeric token from the stream\n     * @param token - Numeric token\n     * @returns Promise with number\n     */\n    async peekNumber(token) {\n        const len = await this.peekBuffer(this.numBuffer, { length: token.len });\n        if (len < token.len)\n            throw new peek_readable__WEBPACK_IMPORTED_MODULE_0__.EndOfStreamError();\n        return token.get(this.numBuffer, 0);\n    }\n    /**\n     * Ignore number of bytes, advances the pointer in under tokenizer-stream.\n     * @param length - Number of bytes to ignore\n     * @return resolves the number of bytes ignored, equals length if this available, otherwise the number of bytes available\n     */\n    async ignore(length) {\n        if (this.fileInfo.size !== undefined) {\n            const bytesLeft = this.fileInfo.size - this.position;\n            if (length > bytesLeft) {\n                this.position += bytesLeft;\n                return bytesLeft;\n            }\n        }\n        this.position += length;\n        return length;\n    }\n    async close() {\n        await this.onClose?.();\n    }\n    normalizeOptions(uint8Array, options) {\n        if (options && options.position !== undefined && options.position < this.position) {\n            throw new Error('`options.position` must be equal or greater than `tokenizer.position`');\n        }\n        if (options) {\n            return {\n                mayBeLess: options.mayBeLess === true,\n                offset: options.offset ? options.offset : 0,\n                length: options.length ? options.length : (uint8Array.length - (options.offset ? options.offset : 0)),\n                position: options.position ? options.position : this.position\n            };\n        }\n        return {\n            mayBeLess: false,\n            offset: 0,\n            length: uint8Array.length,\n            position: this.position\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/strtok3/lib/AbstractTokenizer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/strtok3/lib/BufferTokenizer.js":
/*!*****************************************************!*\
  !*** ./node_modules/strtok3/lib/BufferTokenizer.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BufferTokenizer: () => (/* binding */ BufferTokenizer)\n/* harmony export */ });\n/* harmony import */ var peek_readable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! peek-readable */ \"(rsc)/./node_modules/peek-readable/lib/index.js\");\n/* harmony import */ var _AbstractTokenizer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./AbstractTokenizer.js */ \"(rsc)/./node_modules/strtok3/lib/AbstractTokenizer.js\");\n\n\nclass BufferTokenizer extends _AbstractTokenizer_js__WEBPACK_IMPORTED_MODULE_1__.AbstractTokenizer {\n    /**\n     * Construct BufferTokenizer\n     * @param uint8Array - Uint8Array to tokenize\n     * @param options Tokenizer options\n     */\n    constructor(uint8Array, options) {\n        super(options);\n        this.uint8Array = uint8Array;\n        this.fileInfo.size = this.fileInfo.size ? this.fileInfo.size : uint8Array.length;\n    }\n    /**\n     * Read buffer from tokenizer\n     * @param uint8Array - Uint8Array to tokenize\n     * @param options - Read behaviour options\n     * @returns {Promise<number>}\n     */\n    async readBuffer(uint8Array, options) {\n        if (options?.position) {\n            if (options.position < this.position) {\n                throw new Error('`options.position` must be equal or greater than `tokenizer.position`');\n            }\n            this.position = options.position;\n        }\n        const bytesRead = await this.peekBuffer(uint8Array, options);\n        this.position += bytesRead;\n        return bytesRead;\n    }\n    /**\n     * Peek (read ahead) buffer from tokenizer\n     * @param uint8Array\n     * @param options - Read behaviour options\n     * @returns {Promise<number>}\n     */\n    async peekBuffer(uint8Array, options) {\n        const normOptions = this.normalizeOptions(uint8Array, options);\n        const bytes2read = Math.min(this.uint8Array.length - normOptions.position, normOptions.length);\n        if ((!normOptions.mayBeLess) && bytes2read < normOptions.length) {\n            throw new peek_readable__WEBPACK_IMPORTED_MODULE_0__.EndOfStreamError();\n        }\n        uint8Array.set(this.uint8Array.subarray(normOptions.position, normOptions.position + bytes2read), normOptions.offset);\n        return bytes2read;\n    }\n    close() {\n        return super.close();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/strtok3/lib/BufferTokenizer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/strtok3/lib/FileTokenizer.js":
/*!***************************************************!*\
  !*** ./node_modules/strtok3/lib/FileTokenizer.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FileTokenizer: () => (/* binding */ FileTokenizer),\n/* harmony export */   fromFile: () => (/* binding */ fromFile)\n/* harmony export */ });\n/* harmony import */ var _AbstractTokenizer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./AbstractTokenizer.js */ \"(rsc)/./node_modules/strtok3/lib/AbstractTokenizer.js\");\n/* harmony import */ var peek_readable__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! peek-readable */ \"(rsc)/./node_modules/peek-readable/lib/index.js\");\n/* harmony import */ var node_fs_promises__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! node:fs/promises */ \"node:fs/promises\");\n\n\n\nclass FileTokenizer extends _AbstractTokenizer_js__WEBPACK_IMPORTED_MODULE_0__.AbstractTokenizer {\n    constructor(fileHandle, options) {\n        super(options);\n        this.fileHandle = fileHandle;\n    }\n    /**\n     * Read buffer from file\n     * @param uint8Array - Uint8Array to write result to\n     * @param options - Read behaviour options\n     * @returns Promise number of bytes read\n     */\n    async readBuffer(uint8Array, options) {\n        const normOptions = this.normalizeOptions(uint8Array, options);\n        this.position = normOptions.position;\n        if (normOptions.length === 0)\n            return 0;\n        const res = await this.fileHandle.read(uint8Array, normOptions.offset, normOptions.length, normOptions.position);\n        this.position += res.bytesRead;\n        if (res.bytesRead < normOptions.length && (!options || !options.mayBeLess)) {\n            throw new peek_readable__WEBPACK_IMPORTED_MODULE_1__.EndOfStreamError();\n        }\n        return res.bytesRead;\n    }\n    /**\n     * Peek buffer from file\n     * @param uint8Array - Uint8Array (or Buffer) to write data to\n     * @param options - Read behaviour options\n     * @returns Promise number of bytes read\n     */\n    async peekBuffer(uint8Array, options) {\n        const normOptions = this.normalizeOptions(uint8Array, options);\n        const res = await this.fileHandle.read(uint8Array, normOptions.offset, normOptions.length, normOptions.position);\n        if ((!normOptions.mayBeLess) && res.bytesRead < normOptions.length) {\n            throw new peek_readable__WEBPACK_IMPORTED_MODULE_1__.EndOfStreamError();\n        }\n        return res.bytesRead;\n    }\n    async close() {\n        await this.fileHandle.close();\n        return super.close();\n    }\n}\nasync function fromFile(sourceFilePath) {\n    const fileHandle = await (0,node_fs_promises__WEBPACK_IMPORTED_MODULE_2__.open)(sourceFilePath, 'r');\n    const stat = await fileHandle.stat();\n    return new FileTokenizer(fileHandle, { fileInfo: { path: sourceFilePath, size: stat.size } });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/strtok3/lib/FileTokenizer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/strtok3/lib/ReadStreamTokenizer.js":
/*!*********************************************************!*\
  !*** ./node_modules/strtok3/lib/ReadStreamTokenizer.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReadStreamTokenizer: () => (/* binding */ ReadStreamTokenizer)\n/* harmony export */ });\n/* harmony import */ var _AbstractTokenizer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./AbstractTokenizer.js */ \"(rsc)/./node_modules/strtok3/lib/AbstractTokenizer.js\");\n/* harmony import */ var peek_readable__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! peek-readable */ \"(rsc)/./node_modules/peek-readable/lib/index.js\");\n\n\nconst maxBufferSize = 256000;\nclass ReadStreamTokenizer extends _AbstractTokenizer_js__WEBPACK_IMPORTED_MODULE_0__.AbstractTokenizer {\n    /**\n     * Constructor\n     * @param streamReader stream-reader to read from\n     * @param options Tokenizer options\n     */\n    constructor(streamReader, options) {\n        super(options);\n        this.streamReader = streamReader;\n    }\n    /**\n     * Read buffer from tokenizer\n     * @param uint8Array - Target Uint8Array to fill with data read from the tokenizer-stream\n     * @param options - Read behaviour options\n     * @returns Promise with number of bytes read\n     */\n    async readBuffer(uint8Array, options) {\n        const normOptions = this.normalizeOptions(uint8Array, options);\n        const skipBytes = normOptions.position - this.position;\n        if (skipBytes > 0) {\n            await this.ignore(skipBytes);\n            return this.readBuffer(uint8Array, options);\n        }\n        if (skipBytes < 0) {\n            throw new Error('`options.position` must be equal or greater than `tokenizer.position`');\n        }\n        if (normOptions.length === 0) {\n            return 0;\n        }\n        const bytesRead = await this.streamReader.read(uint8Array, normOptions.offset, normOptions.length);\n        this.position += bytesRead;\n        if ((!options || !options.mayBeLess) && bytesRead < normOptions.length) {\n            throw new peek_readable__WEBPACK_IMPORTED_MODULE_1__.EndOfStreamError();\n        }\n        return bytesRead;\n    }\n    /**\n     * Peek (read ahead) buffer from tokenizer\n     * @param uint8Array - Uint8Array (or Buffer) to write data to\n     * @param options - Read behaviour options\n     * @returns Promise with number of bytes peeked\n     */\n    async peekBuffer(uint8Array, options) {\n        const normOptions = this.normalizeOptions(uint8Array, options);\n        let bytesRead = 0;\n        if (normOptions.position) {\n            const skipBytes = normOptions.position - this.position;\n            if (skipBytes > 0) {\n                const skipBuffer = new Uint8Array(normOptions.length + skipBytes);\n                bytesRead = await this.peekBuffer(skipBuffer, { mayBeLess: normOptions.mayBeLess });\n                uint8Array.set(skipBuffer.subarray(skipBytes), normOptions.offset);\n                return bytesRead - skipBytes;\n            }\n            if (skipBytes < 0) {\n                throw new Error('Cannot peek from a negative offset in a stream');\n            }\n        }\n        if (normOptions.length > 0) {\n            try {\n                bytesRead = await this.streamReader.peek(uint8Array, normOptions.offset, normOptions.length);\n            }\n            catch (err) {\n                if (options?.mayBeLess && err instanceof peek_readable__WEBPACK_IMPORTED_MODULE_1__.EndOfStreamError) {\n                    return 0;\n                }\n                throw err;\n            }\n            if ((!normOptions.mayBeLess) && bytesRead < normOptions.length) {\n                throw new peek_readable__WEBPACK_IMPORTED_MODULE_1__.EndOfStreamError();\n            }\n        }\n        return bytesRead;\n    }\n    async ignore(length) {\n        // debug(`ignore ${this.position}...${this.position + length - 1}`);\n        const bufSize = Math.min(maxBufferSize, length);\n        const buf = new Uint8Array(bufSize);\n        let totBytesRead = 0;\n        while (totBytesRead < length) {\n            const remaining = length - totBytesRead;\n            const bytesRead = await this.readBuffer(buf, { length: Math.min(bufSize, remaining) });\n            if (bytesRead < 0) {\n                return bytesRead;\n            }\n            totBytesRead += bytesRead;\n        }\n        return totBytesRead;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/strtok3/lib/ReadStreamTokenizer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/strtok3/lib/core.js":
/*!******************************************!*\
  !*** ./node_modules/strtok3/lib/core.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AbstractTokenizer: () => (/* reexport safe */ _AbstractTokenizer_js__WEBPACK_IMPORTED_MODULE_3__.AbstractTokenizer),\n/* harmony export */   EndOfStreamError: () => (/* reexport safe */ peek_readable__WEBPACK_IMPORTED_MODULE_0__.EndOfStreamError),\n/* harmony export */   fromBuffer: () => (/* binding */ fromBuffer),\n/* harmony export */   fromStream: () => (/* binding */ fromStream),\n/* harmony export */   fromWebStream: () => (/* binding */ fromWebStream)\n/* harmony export */ });\n/* harmony import */ var peek_readable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! peek-readable */ \"(rsc)/./node_modules/peek-readable/lib/index.js\");\n/* harmony import */ var _ReadStreamTokenizer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ReadStreamTokenizer.js */ \"(rsc)/./node_modules/strtok3/lib/ReadStreamTokenizer.js\");\n/* harmony import */ var _BufferTokenizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./BufferTokenizer.js */ \"(rsc)/./node_modules/strtok3/lib/BufferTokenizer.js\");\n/* harmony import */ var _AbstractTokenizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AbstractTokenizer.js */ \"(rsc)/./node_modules/strtok3/lib/AbstractTokenizer.js\");\n\n\n\n\n\n/**\n * Construct ReadStreamTokenizer from given Stream.\n * Will set fileSize, if provided given Stream has set the .path property/\n * @param stream - Read from Node.js Stream.Readable\n * @param options - Tokenizer options\n * @returns ReadStreamTokenizer\n */\nfunction fromStream(stream, options) {\n    return new _ReadStreamTokenizer_js__WEBPACK_IMPORTED_MODULE_1__.ReadStreamTokenizer(new peek_readable__WEBPACK_IMPORTED_MODULE_0__.StreamReader(stream), options);\n}\n/**\n * Construct ReadStreamTokenizer from given ReadableStream (WebStream API).\n * Will set fileSize, if provided given Stream has set the .path property/\n * @param webStream - Read from Node.js Stream.Readable (must be a byte stream)\n * @param options - Tokenizer options\n * @returns ReadStreamTokenizer\n */\nfunction fromWebStream(webStream, options) {\n    return new _ReadStreamTokenizer_js__WEBPACK_IMPORTED_MODULE_1__.ReadStreamTokenizer(new peek_readable__WEBPACK_IMPORTED_MODULE_0__.WebStreamReader(webStream), options);\n}\n/**\n * Construct ReadStreamTokenizer from given Buffer.\n * @param uint8Array - Uint8Array to tokenize\n * @param options - Tokenizer options\n * @returns BufferTokenizer\n */\nfunction fromBuffer(uint8Array, options) {\n    return new _BufferTokenizer_js__WEBPACK_IMPORTED_MODULE_2__.BufferTokenizer(uint8Array, options);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/strtok3/lib/core.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/strtok3/lib/index.js":
/*!*******************************************!*\
  !*** ./node_modules/strtok3/lib/index.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AbstractTokenizer: () => (/* reexport safe */ _core_js__WEBPACK_IMPORTED_MODULE_1__.AbstractTokenizer),\n/* harmony export */   EndOfStreamError: () => (/* reexport safe */ _core_js__WEBPACK_IMPORTED_MODULE_1__.EndOfStreamError),\n/* harmony export */   fromBuffer: () => (/* reexport safe */ _core_js__WEBPACK_IMPORTED_MODULE_1__.fromBuffer),\n/* harmony export */   fromFile: () => (/* reexport safe */ _FileTokenizer_js__WEBPACK_IMPORTED_MODULE_2__.fromFile),\n/* harmony export */   fromStream: () => (/* binding */ fromStream),\n/* harmony export */   fromWebStream: () => (/* reexport safe */ _core_js__WEBPACK_IMPORTED_MODULE_1__.fromWebStream)\n/* harmony export */ });\n/* harmony import */ var node_fs_promises__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:fs/promises */ \"node:fs/promises\");\n/* harmony import */ var _core_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./core.js */ \"(rsc)/./node_modules/strtok3/lib/core.js\");\n/* harmony import */ var _FileTokenizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./FileTokenizer.js */ \"(rsc)/./node_modules/strtok3/lib/FileTokenizer.js\");\n\n\n\n\n/**\n * Construct ReadStreamTokenizer from given Stream.\n * Will set fileSize, if provided given Stream has set the .path property.\n * @param stream - Node.js Stream.Readable\n * @param options - Pass additional file information to the tokenizer\n * @returns Tokenizer\n */\nasync function fromStream(stream, options) {\n    const augmentedOptions = options ?? {};\n    augmentedOptions.fileInfo = augmentedOptions.fileInfo ?? {};\n    if (stream.path) {\n        const stat = await (0,node_fs_promises__WEBPACK_IMPORTED_MODULE_0__.stat)(stream.path);\n        augmentedOptions.fileInfo.path = stream.path;\n        augmentedOptions.fileInfo.size = stat.size;\n    }\n    return (0,_core_js__WEBPACK_IMPORTED_MODULE_1__.fromStream)(stream, augmentedOptions);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvc3RydG9rMy9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQWtEO0FBQ087QUFDWDtBQUNwQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLHNEQUFNO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBLFdBQVcsb0RBQWM7QUFDekIiLCJzb3VyY2VzIjpbIkQ6XFxXb3JrXFxQYXlsb2FkXFx0ZXN0LXBheWxvYWQtanNvblxcY29ycG9yYXRlLXdlYnNpdGVcXG5vZGVfbW9kdWxlc1xcc3RydG9rM1xcbGliXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBzdGF0IGFzIGZzU3RhdCB9IGZyb20gJ25vZGU6ZnMvcHJvbWlzZXMnO1xuaW1wb3J0IHsgZnJvbVN0cmVhbSBhcyBjb3JlRnJvbVN0cmVhbSB9IGZyb20gJy4vY29yZS5qcyc7XG5leHBvcnQgeyBmcm9tRmlsZSB9IGZyb20gJy4vRmlsZVRva2VuaXplci5qcyc7XG5leHBvcnQgKiBmcm9tICcuL2NvcmUuanMnO1xuLyoqXG4gKiBDb25zdHJ1Y3QgUmVhZFN0cmVhbVRva2VuaXplciBmcm9tIGdpdmVuIFN0cmVhbS5cbiAqIFdpbGwgc2V0IGZpbGVTaXplLCBpZiBwcm92aWRlZCBnaXZlbiBTdHJlYW0gaGFzIHNldCB0aGUgLnBhdGggcHJvcGVydHkuXG4gKiBAcGFyYW0gc3RyZWFtIC0gTm9kZS5qcyBTdHJlYW0uUmVhZGFibGVcbiAqIEBwYXJhbSBvcHRpb25zIC0gUGFzcyBhZGRpdGlvbmFsIGZpbGUgaW5mb3JtYXRpb24gdG8gdGhlIHRva2VuaXplclxuICogQHJldHVybnMgVG9rZW5pemVyXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBmcm9tU3RyZWFtKHN0cmVhbSwgb3B0aW9ucykge1xuICAgIGNvbnN0IGF1Z21lbnRlZE9wdGlvbnMgPSBvcHRpb25zID8/IHt9O1xuICAgIGF1Z21lbnRlZE9wdGlvbnMuZmlsZUluZm8gPSBhdWdtZW50ZWRPcHRpb25zLmZpbGVJbmZvID8/IHt9O1xuICAgIGlmIChzdHJlYW0ucGF0aCkge1xuICAgICAgICBjb25zdCBzdGF0ID0gYXdhaXQgZnNTdGF0KHN0cmVhbS5wYXRoKTtcbiAgICAgICAgYXVnbWVudGVkT3B0aW9ucy5maWxlSW5mby5wYXRoID0gc3RyZWFtLnBhdGg7XG4gICAgICAgIGF1Z21lbnRlZE9wdGlvbnMuZmlsZUluZm8uc2l6ZSA9IHN0YXQuc2l6ZTtcbiAgICB9XG4gICAgcmV0dXJuIGNvcmVGcm9tU3RyZWFtKHN0cmVhbSwgYXVnbWVudGVkT3B0aW9ucyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/strtok3/lib/index.js\n");

/***/ })

};
;