"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/file-type";
exports.ids = ["vendor-chunks/file-type"];
exports.modules = {

/***/ "(rsc)/./node_modules/file-type/core.js":
/*!****************************************!*\
  !*** ./node_modules/file-type/core.js ***!
  \****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FileTypeParser: () => (/* binding */ FileTypeParser),\n/* harmony export */   fileTypeFromBlob: () => (/* binding */ fileTypeFromBlob),\n/* harmony export */   fileTypeFromBuffer: () => (/* binding */ fileTypeFromBuffer),\n/* harmony export */   fileTypeFromStream: () => (/* binding */ fileTypeFromStream),\n/* harmony export */   fileTypeFromTokenizer: () => (/* binding */ fileTypeFromTokenizer),\n/* harmony export */   reasonableDetectionSizeInBytes: () => (/* binding */ reasonableDetectionSizeInBytes),\n/* harmony export */   supportedExtensions: () => (/* binding */ supportedExtensions),\n/* harmony export */   supportedMimeTypes: () => (/* binding */ supportedMimeTypes)\n/* harmony export */ });\n/* harmony import */ var token_types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! token-types */ \"(rsc)/./node_modules/token-types/lib/index.js\");\n/* harmony import */ var strtok3_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! strtok3/core */ \"(rsc)/./node_modules/strtok3/lib/core.js\");\n/* harmony import */ var uint8array_extras__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! uint8array-extras */ \"(rsc)/./node_modules/uint8array-extras/index.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util.js */ \"(rsc)/./node_modules/file-type/util.js\");\n/* harmony import */ var _supported_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./supported.js */ \"(rsc)/./node_modules/file-type/supported.js\");\n/**\nPrimary entry point, Node.js specific entry point is index.js\n*/\n\n\n\n\n\n\n\nconst reasonableDetectionSizeInBytes = 4100; // A fair amount of file-types are detectable within this range.\n\nasync function fileTypeFromStream(stream) {\n\treturn new FileTypeParser().fromStream(stream);\n}\n\nasync function fileTypeFromBuffer(input) {\n\treturn new FileTypeParser().fromBuffer(input);\n}\n\nasync function fileTypeFromBlob(blob) {\n\treturn new FileTypeParser().fromBlob(blob);\n}\n\nfunction _check(buffer, headers, options) {\n\toptions = {\n\t\toffset: 0,\n\t\t...options,\n\t};\n\n\tfor (const [index, header] of headers.entries()) {\n\t\t// If a bitmask is set\n\t\tif (options.mask) {\n\t\t\t// If header doesn't equal `buf` with bits masked off\n\t\t\tif (header !== (options.mask[index] & buffer[index + options.offset])) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t} else if (header !== buffer[index + options.offset]) {\n\t\t\treturn false;\n\t\t}\n\t}\n\n\treturn true;\n}\n\nasync function fileTypeFromTokenizer(tokenizer) {\n\treturn new FileTypeParser().fromTokenizer(tokenizer);\n}\n\nclass FileTypeParser {\n\tconstructor(options) {\n\t\tthis.detectors = options?.customDetectors;\n\n\t\tthis.fromTokenizer = this.fromTokenizer.bind(this);\n\t\tthis.fromBuffer = this.fromBuffer.bind(this);\n\t\tthis.parse = this.parse.bind(this);\n\t}\n\n\tasync fromTokenizer(tokenizer) {\n\t\tconst initialPosition = tokenizer.position;\n\n\t\tfor (const detector of this.detectors || []) {\n\t\t\tconst fileType = await detector(tokenizer);\n\t\t\tif (fileType) {\n\t\t\t\treturn fileType;\n\t\t\t}\n\n\t\t\tif (initialPosition !== tokenizer.position) {\n\t\t\t\treturn undefined; // Cannot proceed scanning of the tokenizer is at an arbitrary position\n\t\t\t}\n\t\t}\n\n\t\treturn this.parse(tokenizer);\n\t}\n\n\tasync fromBuffer(input) {\n\t\tif (!(input instanceof Uint8Array || input instanceof ArrayBuffer)) {\n\t\t\tthrow new TypeError(`Expected the \\`input\\` argument to be of type \\`Uint8Array\\` or \\`ArrayBuffer\\`, got \\`${typeof input}\\``);\n\t\t}\n\n\t\tconst buffer = input instanceof Uint8Array ? input : new Uint8Array(input);\n\n\t\tif (!(buffer?.length > 1)) {\n\t\t\treturn;\n\t\t}\n\n\t\treturn this.fromTokenizer(strtok3_core__WEBPACK_IMPORTED_MODULE_1__.fromBuffer(buffer));\n\t}\n\n\tasync fromBlob(blob) {\n\t\treturn this.fromStream(blob.stream());\n\t}\n\n\tasync fromStream(stream) {\n\t\tconst tokenizer = await strtok3_core__WEBPACK_IMPORTED_MODULE_1__.fromWebStream(stream);\n\t\ttry {\n\t\t\treturn await this.fromTokenizer(tokenizer);\n\t\t} finally {\n\t\t\tawait tokenizer.close();\n\t\t}\n\t}\n\n\tcheck(header, options) {\n\t\treturn _check(this.buffer, header, options);\n\t}\n\n\tcheckString(header, options) {\n\t\treturn this.check((0,_util_js__WEBPACK_IMPORTED_MODULE_2__.stringToBytes)(header), options);\n\t}\n\n\tasync parse(tokenizer) {\n\t\tthis.buffer = new Uint8Array(reasonableDetectionSizeInBytes);\n\n\t\t// Keep reading until EOF if the file size is unknown.\n\t\tif (tokenizer.fileInfo.size === undefined) {\n\t\t\ttokenizer.fileInfo.size = Number.MAX_SAFE_INTEGER;\n\t\t}\n\n\t\tthis.tokenizer = tokenizer;\n\n\t\tawait tokenizer.peekBuffer(this.buffer, {length: 12, mayBeLess: true});\n\n\t\t// -- 2-byte signatures --\n\n\t\tif (this.check([0x42, 0x4D])) {\n\t\t\treturn {\n\t\t\t\text: 'bmp',\n\t\t\t\tmime: 'image/bmp',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x0B, 0x77])) {\n\t\t\treturn {\n\t\t\t\text: 'ac3',\n\t\t\t\tmime: 'audio/vnd.dolby.dd-raw',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x78, 0x01])) {\n\t\t\treturn {\n\t\t\t\text: 'dmg',\n\t\t\t\tmime: 'application/x-apple-diskimage',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x4D, 0x5A])) {\n\t\t\treturn {\n\t\t\t\text: 'exe',\n\t\t\t\tmime: 'application/x-msdownload',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x25, 0x21])) {\n\t\t\tawait tokenizer.peekBuffer(this.buffer, {length: 24, mayBeLess: true});\n\n\t\t\tif (\n\t\t\t\tthis.checkString('PS-Adobe-', {offset: 2})\n\t\t\t\t&& this.checkString(' EPSF-', {offset: 14})\n\t\t\t) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'eps',\n\t\t\t\t\tmime: 'application/eps',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\text: 'ps',\n\t\t\t\tmime: 'application/postscript',\n\t\t\t};\n\t\t}\n\n\t\tif (\n\t\t\tthis.check([0x1F, 0xA0])\n\t\t\t|| this.check([0x1F, 0x9D])\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'Z',\n\t\t\t\tmime: 'application/x-compress',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0xC7, 0x71])) {\n\t\t\treturn {\n\t\t\t\text: 'cpio',\n\t\t\t\tmime: 'application/x-cpio',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x60, 0xEA])) {\n\t\t\treturn {\n\t\t\t\text: 'arj',\n\t\t\t\tmime: 'application/x-arj',\n\t\t\t};\n\t\t}\n\n\t\t// -- 3-byte signatures --\n\n\t\tif (this.check([0xEF, 0xBB, 0xBF])) { // UTF-8-BOM\n\t\t\t// Strip off UTF-8-BOM\n\t\t\tthis.tokenizer.ignore(3);\n\t\t\treturn this.parse(tokenizer);\n\t\t}\n\n\t\tif (this.check([0x47, 0x49, 0x46])) {\n\t\t\treturn {\n\t\t\t\text: 'gif',\n\t\t\t\tmime: 'image/gif',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x49, 0x49, 0xBC])) {\n\t\t\treturn {\n\t\t\t\text: 'jxr',\n\t\t\t\tmime: 'image/vnd.ms-photo',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x1F, 0x8B, 0x8])) {\n\t\t\treturn {\n\t\t\t\text: 'gz',\n\t\t\t\tmime: 'application/gzip',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x42, 0x5A, 0x68])) {\n\t\t\treturn {\n\t\t\t\text: 'bz2',\n\t\t\t\tmime: 'application/x-bzip2',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('ID3')) {\n\t\t\tawait tokenizer.ignore(6); // Skip ID3 header until the header size\n\t\t\tconst id3HeaderLength = await tokenizer.readToken(_util_js__WEBPACK_IMPORTED_MODULE_2__.uint32SyncSafeToken);\n\t\t\tif (tokenizer.position + id3HeaderLength > tokenizer.fileInfo.size) {\n\t\t\t\t// Guess file type based on ID3 header for backward compatibility\n\t\t\t\treturn {\n\t\t\t\t\text: 'mp3',\n\t\t\t\t\tmime: 'audio/mpeg',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tawait tokenizer.ignore(id3HeaderLength);\n\t\t\treturn this.fromTokenizer(tokenizer); // Skip ID3 header, recursion\n\t\t}\n\n\t\t// Musepack, SV7\n\t\tif (this.checkString('MP+')) {\n\t\t\treturn {\n\t\t\t\text: 'mpc',\n\t\t\t\tmime: 'audio/x-musepack',\n\t\t\t};\n\t\t}\n\n\t\tif (\n\t\t\t(this.buffer[0] === 0x43 || this.buffer[0] === 0x46)\n\t\t\t&& this.check([0x57, 0x53], {offset: 1})\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'swf',\n\t\t\t\tmime: 'application/x-shockwave-flash',\n\t\t\t};\n\t\t}\n\n\t\t// -- 4-byte signatures --\n\n\t\t// Requires a sample size of 4 bytes\n\t\tif (this.check([0xFF, 0xD8, 0xFF])) {\n\t\t\tif (this.check([0xF7], {offset: 3})) { // JPG7/SOF55, indicating a ISO/IEC 14495 / JPEG-LS file\n\t\t\t\treturn {\n\t\t\t\t\text: 'jls',\n\t\t\t\t\tmime: 'image/jls',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\text: 'jpg',\n\t\t\t\tmime: 'image/jpeg',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x4F, 0x62, 0x6A, 0x01])) {\n\t\t\treturn {\n\t\t\t\text: 'avro',\n\t\t\t\tmime: 'application/avro',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('FLIF')) {\n\t\t\treturn {\n\t\t\t\text: 'flif',\n\t\t\t\tmime: 'image/flif',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('8BPS')) {\n\t\t\treturn {\n\t\t\t\text: 'psd',\n\t\t\t\tmime: 'image/vnd.adobe.photoshop',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('WEBP', {offset: 8})) {\n\t\t\treturn {\n\t\t\t\text: 'webp',\n\t\t\t\tmime: 'image/webp',\n\t\t\t};\n\t\t}\n\n\t\t// Musepack, SV8\n\t\tif (this.checkString('MPCK')) {\n\t\t\treturn {\n\t\t\t\text: 'mpc',\n\t\t\t\tmime: 'audio/x-musepack',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('FORM')) {\n\t\t\treturn {\n\t\t\t\text: 'aif',\n\t\t\t\tmime: 'audio/aiff',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('icns', {offset: 0})) {\n\t\t\treturn {\n\t\t\t\text: 'icns',\n\t\t\t\tmime: 'image/icns',\n\t\t\t};\n\t\t}\n\n\t\t// Zip-based file formats\n\t\t// Need to be before the `zip` check\n\t\tif (this.check([0x50, 0x4B, 0x3, 0x4])) { // Local file header signature\n\t\t\ttry {\n\t\t\t\twhile (tokenizer.position + 30 < tokenizer.fileInfo.size) {\n\t\t\t\t\tawait tokenizer.readBuffer(this.buffer, {length: 30});\n\n\t\t\t\t\tconst view = new DataView(this.buffer.buffer);\n\n\t\t\t\t\t// https://en.wikipedia.org/wiki/Zip_(file_format)#File_headers\n\t\t\t\t\tconst zipHeader = {\n\t\t\t\t\t\tcompressedSize: view.getUint32(18, true),\n\t\t\t\t\t\tuncompressedSize: view.getUint32(22, true),\n\t\t\t\t\t\tfilenameLength: view.getUint16(26, true),\n\t\t\t\t\t\textraFieldLength: view.getUint16(28, true),\n\t\t\t\t\t};\n\n\t\t\t\t\tzipHeader.filename = await tokenizer.readToken(new token_types__WEBPACK_IMPORTED_MODULE_0__.StringType(zipHeader.filenameLength, 'utf-8'));\n\t\t\t\t\tawait tokenizer.ignore(zipHeader.extraFieldLength);\n\n\t\t\t\t\t// Assumes signed `.xpi` from addons.mozilla.org\n\t\t\t\t\tif (zipHeader.filename === 'META-INF/mozilla.rsa') {\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\text: 'xpi',\n\t\t\t\t\t\t\tmime: 'application/x-xpinstall',\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\n\t\t\t\t\tif (zipHeader.filename.endsWith('.rels') || zipHeader.filename.endsWith('.xml')) {\n\t\t\t\t\t\tconst type = zipHeader.filename.split('/')[0];\n\t\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\t\tcase '_rels':\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\tcase 'word':\n\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\text: 'docx',\n\t\t\t\t\t\t\t\t\tmime: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tcase 'ppt':\n\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\text: 'pptx',\n\t\t\t\t\t\t\t\t\tmime: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tcase 'xl':\n\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\text: 'xlsx',\n\t\t\t\t\t\t\t\t\tmime: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tcase 'visio':\n\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\text: 'vsdx',\n\t\t\t\t\t\t\t\t\tmime: 'application/vnd.visio',\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tif (zipHeader.filename.startsWith('xl/')) {\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\text: 'xlsx',\n\t\t\t\t\t\t\tmime: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\n\t\t\t\t\tif (zipHeader.filename.startsWith('3D/') && zipHeader.filename.endsWith('.model')) {\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\text: '3mf',\n\t\t\t\t\t\t\tmime: 'model/3mf',\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\n\t\t\t\t\t// The docx, xlsx and pptx file types extend the Office Open XML file format:\n\t\t\t\t\t// https://en.wikipedia.org/wiki/Office_Open_XML_file_formats\n\t\t\t\t\t// We look for:\n\t\t\t\t\t// - one entry named '[Content_Types].xml' or '_rels/.rels',\n\t\t\t\t\t// - one entry indicating specific type of file.\n\t\t\t\t\t// MS Office, OpenOffice and LibreOffice may put the parts in different order, so the check should not rely on it.\n\t\t\t\t\tif (zipHeader.filename === 'mimetype' && zipHeader.compressedSize === zipHeader.uncompressedSize) {\n\t\t\t\t\t\tlet mimeType = await tokenizer.readToken(new token_types__WEBPACK_IMPORTED_MODULE_0__.StringType(zipHeader.compressedSize, 'utf-8'));\n\t\t\t\t\t\tmimeType = mimeType.trim();\n\n\t\t\t\t\t\tswitch (mimeType) {\n\t\t\t\t\t\t\tcase 'application/epub+zip':\n\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\text: 'epub',\n\t\t\t\t\t\t\t\t\tmime: 'application/epub+zip',\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tcase 'application/vnd.oasis.opendocument.text':\n\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\text: 'odt',\n\t\t\t\t\t\t\t\t\tmime: 'application/vnd.oasis.opendocument.text',\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tcase 'application/vnd.oasis.opendocument.spreadsheet':\n\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\text: 'ods',\n\t\t\t\t\t\t\t\t\tmime: 'application/vnd.oasis.opendocument.spreadsheet',\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tcase 'application/vnd.oasis.opendocument.presentation':\n\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\text: 'odp',\n\t\t\t\t\t\t\t\t\tmime: 'application/vnd.oasis.opendocument.presentation',\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// Try to find next header manually when current one is corrupted\n\t\t\t\t\tif (zipHeader.compressedSize === 0) {\n\t\t\t\t\t\tlet nextHeaderIndex = -1;\n\n\t\t\t\t\t\twhile (nextHeaderIndex < 0 && (tokenizer.position < tokenizer.fileInfo.size)) {\n\t\t\t\t\t\t\tawait tokenizer.peekBuffer(this.buffer, {mayBeLess: true});\n\n\t\t\t\t\t\t\tnextHeaderIndex = (0,uint8array_extras__WEBPACK_IMPORTED_MODULE_3__.indexOf)(this.buffer, new Uint8Array([0x50, 0x4B, 0x03, 0x04]));\n\n\t\t\t\t\t\t\t// Move position to the next header if found, skip the whole buffer otherwise\n\t\t\t\t\t\t\tawait tokenizer.ignore(nextHeaderIndex >= 0 ? nextHeaderIndex : this.buffer.length);\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tawait tokenizer.ignore(zipHeader.compressedSize);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tif (!(error instanceof strtok3_core__WEBPACK_IMPORTED_MODULE_1__.EndOfStreamError)) {\n\t\t\t\t\tthrow error;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\text: 'zip',\n\t\t\t\tmime: 'application/zip',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('OggS')) {\n\t\t\t// This is an OGG container\n\t\t\tawait tokenizer.ignore(28);\n\t\t\tconst type = new Uint8Array(8);\n\t\t\tawait tokenizer.readBuffer(type);\n\n\t\t\t// Needs to be before `ogg` check\n\t\t\tif (_check(type, [0x4F, 0x70, 0x75, 0x73, 0x48, 0x65, 0x61, 0x64])) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'opus',\n\t\t\t\t\tmime: 'audio/opus',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t// If ' theora' in header.\n\t\t\tif (_check(type, [0x80, 0x74, 0x68, 0x65, 0x6F, 0x72, 0x61])) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'ogv',\n\t\t\t\t\tmime: 'video/ogg',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t// If '\\x01video' in header.\n\t\t\tif (_check(type, [0x01, 0x76, 0x69, 0x64, 0x65, 0x6F, 0x00])) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'ogm',\n\t\t\t\t\tmime: 'video/ogg',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t// If ' FLAC' in header  https://xiph.org/flac/faq.html\n\t\t\tif (_check(type, [0x7F, 0x46, 0x4C, 0x41, 0x43])) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'oga',\n\t\t\t\t\tmime: 'audio/ogg',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t// 'Speex  ' in header https://en.wikipedia.org/wiki/Speex\n\t\t\tif (_check(type, [0x53, 0x70, 0x65, 0x65, 0x78, 0x20, 0x20])) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'spx',\n\t\t\t\t\tmime: 'audio/ogg',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t// If '\\x01vorbis' in header\n\t\t\tif (_check(type, [0x01, 0x76, 0x6F, 0x72, 0x62, 0x69, 0x73])) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'ogg',\n\t\t\t\t\tmime: 'audio/ogg',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t// Default OGG container https://www.iana.org/assignments/media-types/application/ogg\n\t\t\treturn {\n\t\t\t\text: 'ogx',\n\t\t\t\tmime: 'application/ogg',\n\t\t\t};\n\t\t}\n\n\t\tif (\n\t\t\tthis.check([0x50, 0x4B])\n\t\t\t&& (this.buffer[2] === 0x3 || this.buffer[2] === 0x5 || this.buffer[2] === 0x7)\n\t\t\t&& (this.buffer[3] === 0x4 || this.buffer[3] === 0x6 || this.buffer[3] === 0x8)\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'zip',\n\t\t\t\tmime: 'application/zip',\n\t\t\t};\n\t\t}\n\n\t\t//\n\n\t\t// File Type Box (https://en.wikipedia.org/wiki/ISO_base_media_file_format)\n\t\t// It's not required to be first, but it's recommended to be. Almost all ISO base media files start with `ftyp` box.\n\t\t// `ftyp` box must contain a brand major identifier, which must consist of ISO 8859-1 printable characters.\n\t\t// Here we check for 8859-1 printable characters (for simplicity, it's a mask which also catches one non-printable character).\n\t\tif (\n\t\t\tthis.checkString('ftyp', {offset: 4})\n\t\t\t&& (this.buffer[8] & 0x60) !== 0x00 // Brand major, first character ASCII?\n\t\t) {\n\t\t\t// They all can have MIME `video/mp4` except `application/mp4` special-case which is hard to detect.\n\t\t\t// For some cases, we're specific, everything else falls to `video/mp4` with `mp4` extension.\n\t\t\tconst brandMajor = new token_types__WEBPACK_IMPORTED_MODULE_0__.StringType(4, 'latin1').get(this.buffer, 8).replace('\\0', ' ').trim();\n\t\t\tswitch (brandMajor) {\n\t\t\t\tcase 'avif':\n\t\t\t\tcase 'avis':\n\t\t\t\t\treturn {ext: 'avif', mime: 'image/avif'};\n\t\t\t\tcase 'mif1':\n\t\t\t\t\treturn {ext: 'heic', mime: 'image/heif'};\n\t\t\t\tcase 'msf1':\n\t\t\t\t\treturn {ext: 'heic', mime: 'image/heif-sequence'};\n\t\t\t\tcase 'heic':\n\t\t\t\tcase 'heix':\n\t\t\t\t\treturn {ext: 'heic', mime: 'image/heic'};\n\t\t\t\tcase 'hevc':\n\t\t\t\tcase 'hevx':\n\t\t\t\t\treturn {ext: 'heic', mime: 'image/heic-sequence'};\n\t\t\t\tcase 'qt':\n\t\t\t\t\treturn {ext: 'mov', mime: 'video/quicktime'};\n\t\t\t\tcase 'M4V':\n\t\t\t\tcase 'M4VH':\n\t\t\t\tcase 'M4VP':\n\t\t\t\t\treturn {ext: 'm4v', mime: 'video/x-m4v'};\n\t\t\t\tcase 'M4P':\n\t\t\t\t\treturn {ext: 'm4p', mime: 'video/mp4'};\n\t\t\t\tcase 'M4B':\n\t\t\t\t\treturn {ext: 'm4b', mime: 'audio/mp4'};\n\t\t\t\tcase 'M4A':\n\t\t\t\t\treturn {ext: 'm4a', mime: 'audio/x-m4a'};\n\t\t\t\tcase 'F4V':\n\t\t\t\t\treturn {ext: 'f4v', mime: 'video/mp4'};\n\t\t\t\tcase 'F4P':\n\t\t\t\t\treturn {ext: 'f4p', mime: 'video/mp4'};\n\t\t\t\tcase 'F4A':\n\t\t\t\t\treturn {ext: 'f4a', mime: 'audio/mp4'};\n\t\t\t\tcase 'F4B':\n\t\t\t\t\treturn {ext: 'f4b', mime: 'audio/mp4'};\n\t\t\t\tcase 'crx':\n\t\t\t\t\treturn {ext: 'cr3', mime: 'image/x-canon-cr3'};\n\t\t\t\tdefault:\n\t\t\t\t\tif (brandMajor.startsWith('3g')) {\n\t\t\t\t\t\tif (brandMajor.startsWith('3g2')) {\n\t\t\t\t\t\t\treturn {ext: '3g2', mime: 'video/3gpp2'};\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn {ext: '3gp', mime: 'video/3gpp'};\n\t\t\t\t\t}\n\n\t\t\t\t\treturn {ext: 'mp4', mime: 'video/mp4'};\n\t\t\t}\n\t\t}\n\n\t\tif (this.checkString('MThd')) {\n\t\t\treturn {\n\t\t\t\text: 'mid',\n\t\t\t\tmime: 'audio/midi',\n\t\t\t};\n\t\t}\n\n\t\tif (\n\t\t\tthis.checkString('wOFF')\n\t\t\t&& (\n\t\t\t\tthis.check([0x00, 0x01, 0x00, 0x00], {offset: 4})\n\t\t\t\t|| this.checkString('OTTO', {offset: 4})\n\t\t\t)\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'woff',\n\t\t\t\tmime: 'font/woff',\n\t\t\t};\n\t\t}\n\n\t\tif (\n\t\t\tthis.checkString('wOF2')\n\t\t\t&& (\n\t\t\t\tthis.check([0x00, 0x01, 0x00, 0x00], {offset: 4})\n\t\t\t\t|| this.checkString('OTTO', {offset: 4})\n\t\t\t)\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'woff2',\n\t\t\t\tmime: 'font/woff2',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0xD4, 0xC3, 0xB2, 0xA1]) || this.check([0xA1, 0xB2, 0xC3, 0xD4])) {\n\t\t\treturn {\n\t\t\t\text: 'pcap',\n\t\t\t\tmime: 'application/vnd.tcpdump.pcap',\n\t\t\t};\n\t\t}\n\n\t\t// Sony DSD Stream File (DSF)\n\t\tif (this.checkString('DSD ')) {\n\t\t\treturn {\n\t\t\t\text: 'dsf',\n\t\t\t\tmime: 'audio/x-dsf', // Non-standard\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('LZIP')) {\n\t\t\treturn {\n\t\t\t\text: 'lz',\n\t\t\t\tmime: 'application/x-lzip',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('fLaC')) {\n\t\t\treturn {\n\t\t\t\text: 'flac',\n\t\t\t\tmime: 'audio/x-flac',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x42, 0x50, 0x47, 0xFB])) {\n\t\t\treturn {\n\t\t\t\text: 'bpg',\n\t\t\t\tmime: 'image/bpg',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('wvpk')) {\n\t\t\treturn {\n\t\t\t\text: 'wv',\n\t\t\t\tmime: 'audio/wavpack',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('%PDF')) {\n\t\t\ttry {\n\t\t\t\tawait tokenizer.ignore(1350);\n\t\t\t\tconst maxBufferSize = 10 * 1024 * 1024;\n\t\t\t\tconst buffer = new Uint8Array(Math.min(maxBufferSize, tokenizer.fileInfo.size));\n\t\t\t\tawait tokenizer.readBuffer(buffer, {mayBeLess: true});\n\n\t\t\t\t// Check if this is an Adobe Illustrator file\n\t\t\t\tif ((0,uint8array_extras__WEBPACK_IMPORTED_MODULE_3__.includes)(buffer, new TextEncoder().encode('AIPrivateData'))) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\text: 'ai',\n\t\t\t\t\t\tmime: 'application/postscript',\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\t// Swallow end of stream error if file is too small for the Adobe AI check\n\t\t\t\tif (!(error instanceof strtok3_core__WEBPACK_IMPORTED_MODULE_1__.EndOfStreamError)) {\n\t\t\t\t\tthrow error;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Assume this is just a normal PDF\n\t\t\treturn {\n\t\t\t\text: 'pdf',\n\t\t\t\tmime: 'application/pdf',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x00, 0x61, 0x73, 0x6D])) {\n\t\t\treturn {\n\t\t\t\text: 'wasm',\n\t\t\t\tmime: 'application/wasm',\n\t\t\t};\n\t\t}\n\n\t\t// TIFF, little-endian type\n\t\tif (this.check([0x49, 0x49])) {\n\t\t\tconst fileType = await this.readTiffHeader(false);\n\t\t\tif (fileType) {\n\t\t\t\treturn fileType;\n\t\t\t}\n\t\t}\n\n\t\t// TIFF, big-endian type\n\t\tif (this.check([0x4D, 0x4D])) {\n\t\t\tconst fileType = await this.readTiffHeader(true);\n\t\t\tif (fileType) {\n\t\t\t\treturn fileType;\n\t\t\t}\n\t\t}\n\n\t\tif (this.checkString('MAC ')) {\n\t\t\treturn {\n\t\t\t\text: 'ape',\n\t\t\t\tmime: 'audio/ape',\n\t\t\t};\n\t\t}\n\n\t\t// https://github.com/file/file/blob/master/magic/Magdir/matroska\n\t\tif (this.check([0x1A, 0x45, 0xDF, 0xA3])) { // Root element: EBML\n\t\t\tasync function readField() {\n\t\t\t\tconst msb = await tokenizer.peekNumber(token_types__WEBPACK_IMPORTED_MODULE_0__.UINT8);\n\t\t\t\tlet mask = 0x80;\n\t\t\t\tlet ic = 0; // 0 = A, 1 = B, 2 = C, 3 = D\n\n\t\t\t\twhile ((msb & mask) === 0 && mask !== 0) {\n\t\t\t\t\t++ic;\n\t\t\t\t\tmask >>= 1;\n\t\t\t\t}\n\n\t\t\t\tconst id = new Uint8Array(ic + 1);\n\t\t\t\tawait tokenizer.readBuffer(id);\n\t\t\t\treturn id;\n\t\t\t}\n\n\t\t\tasync function readElement() {\n\t\t\t\tconst idField = await readField();\n\t\t\t\tconst lengthField = await readField();\n\n\t\t\t\tlengthField[0] ^= 0x80 >> (lengthField.length - 1);\n\t\t\t\tconst nrLength = Math.min(6, lengthField.length); // JavaScript can max read 6 bytes integer\n\n\t\t\t\tconst idView = new DataView(idField.buffer);\n\t\t\t\tconst lengthView = new DataView(lengthField.buffer, lengthField.length - nrLength, nrLength);\n\n\t\t\t\treturn {\n\t\t\t\t\tid: (0,uint8array_extras__WEBPACK_IMPORTED_MODULE_3__.getUintBE)(idView),\n\t\t\t\t\tlen: (0,uint8array_extras__WEBPACK_IMPORTED_MODULE_3__.getUintBE)(lengthView),\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tasync function readChildren(children) {\n\t\t\t\twhile (children > 0) {\n\t\t\t\t\tconst element = await readElement();\n\t\t\t\t\tif (element.id === 0x42_82) {\n\t\t\t\t\t\tconst rawValue = await tokenizer.readToken(new token_types__WEBPACK_IMPORTED_MODULE_0__.StringType(element.len));\n\t\t\t\t\t\treturn rawValue.replaceAll(/\\00.*$/g, ''); // Return DocType\n\t\t\t\t\t}\n\n\t\t\t\t\tawait tokenizer.ignore(element.len); // ignore payload\n\t\t\t\t\t--children;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconst re = await readElement();\n\t\t\tconst docType = await readChildren(re.len);\n\n\t\t\tswitch (docType) {\n\t\t\t\tcase 'webm':\n\t\t\t\t\treturn {\n\t\t\t\t\t\text: 'webm',\n\t\t\t\t\t\tmime: 'video/webm',\n\t\t\t\t\t};\n\n\t\t\t\tcase 'matroska':\n\t\t\t\t\treturn {\n\t\t\t\t\t\text: 'mkv',\n\t\t\t\t\t\tmime: 'video/x-matroska',\n\t\t\t\t\t};\n\n\t\t\t\tdefault:\n\t\t\t\t\treturn;\n\t\t\t}\n\t\t}\n\n\t\t// RIFF file format which might be AVI, WAV, QCP, etc\n\t\tif (this.check([0x52, 0x49, 0x46, 0x46])) {\n\t\t\tif (this.check([0x41, 0x56, 0x49], {offset: 8})) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'avi',\n\t\t\t\t\tmime: 'video/vnd.avi',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tif (this.check([0x57, 0x41, 0x56, 0x45], {offset: 8})) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'wav',\n\t\t\t\t\tmime: 'audio/wav',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t// QLCM, QCP file\n\t\t\tif (this.check([0x51, 0x4C, 0x43, 0x4D], {offset: 8})) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'qcp',\n\t\t\t\t\tmime: 'audio/qcelp',\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\n\t\tif (this.checkString('SQLi')) {\n\t\t\treturn {\n\t\t\t\text: 'sqlite',\n\t\t\t\tmime: 'application/x-sqlite3',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x4E, 0x45, 0x53, 0x1A])) {\n\t\t\treturn {\n\t\t\t\text: 'nes',\n\t\t\t\tmime: 'application/x-nintendo-nes-rom',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('Cr24')) {\n\t\t\treturn {\n\t\t\t\text: 'crx',\n\t\t\t\tmime: 'application/x-google-chrome-extension',\n\t\t\t};\n\t\t}\n\n\t\tif (\n\t\t\tthis.checkString('MSCF')\n\t\t\t|| this.checkString('ISc(')\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'cab',\n\t\t\t\tmime: 'application/vnd.ms-cab-compressed',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0xED, 0xAB, 0xEE, 0xDB])) {\n\t\t\treturn {\n\t\t\t\text: 'rpm',\n\t\t\t\tmime: 'application/x-rpm',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0xC5, 0xD0, 0xD3, 0xC6])) {\n\t\t\treturn {\n\t\t\t\text: 'eps',\n\t\t\t\tmime: 'application/eps',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x28, 0xB5, 0x2F, 0xFD])) {\n\t\t\treturn {\n\t\t\t\text: 'zst',\n\t\t\t\tmime: 'application/zstd',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x7F, 0x45, 0x4C, 0x46])) {\n\t\t\treturn {\n\t\t\t\text: 'elf',\n\t\t\t\tmime: 'application/x-elf',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x21, 0x42, 0x44, 0x4E])) {\n\t\t\treturn {\n\t\t\t\text: 'pst',\n\t\t\t\tmime: 'application/vnd.ms-outlook',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('PAR1')) {\n\t\t\treturn {\n\t\t\t\text: 'parquet',\n\t\t\t\tmime: 'application/x-parquet',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0xCF, 0xFA, 0xED, 0xFE])) {\n\t\t\treturn {\n\t\t\t\text: 'macho',\n\t\t\t\tmime: 'application/x-mach-binary',\n\t\t\t};\n\t\t}\n\n\t\t// -- 5-byte signatures --\n\n\t\tif (this.check([0x4F, 0x54, 0x54, 0x4F, 0x00])) {\n\t\t\treturn {\n\t\t\t\text: 'otf',\n\t\t\t\tmime: 'font/otf',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('#!AMR')) {\n\t\t\treturn {\n\t\t\t\text: 'amr',\n\t\t\t\tmime: 'audio/amr',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('{\\\\rtf')) {\n\t\t\treturn {\n\t\t\t\text: 'rtf',\n\t\t\t\tmime: 'application/rtf',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x46, 0x4C, 0x56, 0x01])) {\n\t\t\treturn {\n\t\t\t\text: 'flv',\n\t\t\t\tmime: 'video/x-flv',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('IMPM')) {\n\t\t\treturn {\n\t\t\t\text: 'it',\n\t\t\t\tmime: 'audio/x-it',\n\t\t\t};\n\t\t}\n\n\t\tif (\n\t\t\tthis.checkString('-lh0-', {offset: 2})\n\t\t\t|| this.checkString('-lh1-', {offset: 2})\n\t\t\t|| this.checkString('-lh2-', {offset: 2})\n\t\t\t|| this.checkString('-lh3-', {offset: 2})\n\t\t\t|| this.checkString('-lh4-', {offset: 2})\n\t\t\t|| this.checkString('-lh5-', {offset: 2})\n\t\t\t|| this.checkString('-lh6-', {offset: 2})\n\t\t\t|| this.checkString('-lh7-', {offset: 2})\n\t\t\t|| this.checkString('-lzs-', {offset: 2})\n\t\t\t|| this.checkString('-lz4-', {offset: 2})\n\t\t\t|| this.checkString('-lz5-', {offset: 2})\n\t\t\t|| this.checkString('-lhd-', {offset: 2})\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'lzh',\n\t\t\t\tmime: 'application/x-lzh-compressed',\n\t\t\t};\n\t\t}\n\n\t\t// MPEG program stream (PS or MPEG-PS)\n\t\tif (this.check([0x00, 0x00, 0x01, 0xBA])) {\n\t\t\t//  MPEG-PS, MPEG-1 Part 1\n\t\t\tif (this.check([0x21], {offset: 4, mask: [0xF1]})) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'mpg', // May also be .ps, .mpeg\n\t\t\t\t\tmime: 'video/MP1S',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t// MPEG-PS, MPEG-2 Part 1\n\t\t\tif (this.check([0x44], {offset: 4, mask: [0xC4]})) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'mpg', // May also be .mpg, .m2p, .vob or .sub\n\t\t\t\t\tmime: 'video/MP2P',\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\n\t\tif (this.checkString('ITSF')) {\n\t\t\treturn {\n\t\t\t\text: 'chm',\n\t\t\t\tmime: 'application/vnd.ms-htmlhelp',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0xCA, 0xFE, 0xBA, 0xBE])) {\n\t\t\treturn {\n\t\t\t\text: 'class',\n\t\t\t\tmime: 'application/java-vm',\n\t\t\t};\n\t\t}\n\n\t\t// -- 6-byte signatures --\n\n\t\tif (this.check([0xFD, 0x37, 0x7A, 0x58, 0x5A, 0x00])) {\n\t\t\treturn {\n\t\t\t\text: 'xz',\n\t\t\t\tmime: 'application/x-xz',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('<?xml ')) {\n\t\t\treturn {\n\t\t\t\text: 'xml',\n\t\t\t\tmime: 'application/xml',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x37, 0x7A, 0xBC, 0xAF, 0x27, 0x1C])) {\n\t\t\treturn {\n\t\t\t\text: '7z',\n\t\t\t\tmime: 'application/x-7z-compressed',\n\t\t\t};\n\t\t}\n\n\t\tif (\n\t\t\tthis.check([0x52, 0x61, 0x72, 0x21, 0x1A, 0x7])\n\t\t\t&& (this.buffer[6] === 0x0 || this.buffer[6] === 0x1)\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'rar',\n\t\t\t\tmime: 'application/x-rar-compressed',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('solid ')) {\n\t\t\treturn {\n\t\t\t\text: 'stl',\n\t\t\t\tmime: 'model/stl',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('AC')) {\n\t\t\tconst version = new token_types__WEBPACK_IMPORTED_MODULE_0__.StringType(4, 'latin1').get(this.buffer, 2);\n\t\t\tif (version.match('^d*') && version >= 1000 && version <= 1050) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'dwg',\n\t\t\t\t\tmime: 'image/vnd.dwg',\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\n\t\tif (this.checkString('070707')) {\n\t\t\treturn {\n\t\t\t\text: 'cpio',\n\t\t\t\tmime: 'application/x-cpio',\n\t\t\t};\n\t\t}\n\n\t\t// -- 7-byte signatures --\n\n\t\tif (this.checkString('BLENDER')) {\n\t\t\treturn {\n\t\t\t\text: 'blend',\n\t\t\t\tmime: 'application/x-blender',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('!<arch>')) {\n\t\t\tawait tokenizer.ignore(8);\n\t\t\tconst string = await tokenizer.readToken(new token_types__WEBPACK_IMPORTED_MODULE_0__.StringType(13, 'ascii'));\n\t\t\tif (string === 'debian-binary') {\n\t\t\t\treturn {\n\t\t\t\t\text: 'deb',\n\t\t\t\t\tmime: 'application/x-deb',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\text: 'ar',\n\t\t\t\tmime: 'application/x-unix-archive',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('**ACE', {offset: 7})) {\n\t\t\tawait tokenizer.peekBuffer(this.buffer, {length: 14, mayBeLess: true});\n\t\t\tif (this.checkString('**', {offset: 12})) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'ace',\n\t\t\t\t\tmime: 'application/x-ace-compressed',\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\n\t\t// -- 8-byte signatures --\n\n\t\tif (this.check([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A])) {\n\t\t\t// APNG format (https://wiki.mozilla.org/APNG_Specification)\n\t\t\t// 1. Find the first IDAT (image data) chunk (49 44 41 54)\n\t\t\t// 2. Check if there is an \"acTL\" chunk before the IDAT one (61 63 54 4C)\n\n\t\t\t// Offset calculated as follows:\n\t\t\t// - 8 bytes: PNG signature\n\t\t\t// - 4 (length) + 4 (chunk type) + 13 (chunk data) + 4 (CRC): IHDR chunk\n\n\t\t\tawait tokenizer.ignore(8); // ignore PNG signature\n\n\t\t\tasync function readChunkHeader() {\n\t\t\t\treturn {\n\t\t\t\t\tlength: await tokenizer.readToken(token_types__WEBPACK_IMPORTED_MODULE_0__.INT32_BE),\n\t\t\t\t\ttype: await tokenizer.readToken(new token_types__WEBPACK_IMPORTED_MODULE_0__.StringType(4, 'latin1')),\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tdo {\n\t\t\t\tconst chunk = await readChunkHeader();\n\t\t\t\tif (chunk.length < 0) {\n\t\t\t\t\treturn; // Invalid chunk length\n\t\t\t\t}\n\n\t\t\t\tswitch (chunk.type) {\n\t\t\t\t\tcase 'IDAT':\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\text: 'png',\n\t\t\t\t\t\t\tmime: 'image/png',\n\t\t\t\t\t\t};\n\t\t\t\t\tcase 'acTL':\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\text: 'apng',\n\t\t\t\t\t\t\tmime: 'image/apng',\n\t\t\t\t\t\t};\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tawait tokenizer.ignore(chunk.length + 4); // Ignore chunk-data + CRC\n\t\t\t\t}\n\t\t\t} while (tokenizer.position + 8 < tokenizer.fileInfo.size);\n\n\t\t\treturn {\n\t\t\t\text: 'png',\n\t\t\t\tmime: 'image/png',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x41, 0x52, 0x52, 0x4F, 0x57, 0x31, 0x00, 0x00])) {\n\t\t\treturn {\n\t\t\t\text: 'arrow',\n\t\t\t\tmime: 'application/x-apache-arrow',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x67, 0x6C, 0x54, 0x46, 0x02, 0x00, 0x00, 0x00])) {\n\t\t\treturn {\n\t\t\t\text: 'glb',\n\t\t\t\tmime: 'model/gltf-binary',\n\t\t\t};\n\t\t}\n\n\t\t// `mov` format variants\n\t\tif (\n\t\t\tthis.check([0x66, 0x72, 0x65, 0x65], {offset: 4}) // `free`\n\t\t\t|| this.check([0x6D, 0x64, 0x61, 0x74], {offset: 4}) // `mdat` MJPEG\n\t\t\t|| this.check([0x6D, 0x6F, 0x6F, 0x76], {offset: 4}) // `moov`\n\t\t\t|| this.check([0x77, 0x69, 0x64, 0x65], {offset: 4}) // `wide`\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'mov',\n\t\t\t\tmime: 'video/quicktime',\n\t\t\t};\n\t\t}\n\n\t\t// -- 9-byte signatures --\n\n\t\tif (this.check([0x49, 0x49, 0x52, 0x4F, 0x08, 0x00, 0x00, 0x00, 0x18])) {\n\t\t\treturn {\n\t\t\t\text: 'orf',\n\t\t\t\tmime: 'image/x-olympus-orf',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('gimp xcf ')) {\n\t\t\treturn {\n\t\t\t\text: 'xcf',\n\t\t\t\tmime: 'image/x-xcf',\n\t\t\t};\n\t\t}\n\n\t\t// -- 12-byte signatures --\n\n\t\tif (this.check([0x49, 0x49, 0x55, 0x00, 0x18, 0x00, 0x00, 0x00, 0x88, 0xE7, 0x74, 0xD8])) {\n\t\t\treturn {\n\t\t\t\text: 'rw2',\n\t\t\t\tmime: 'image/x-panasonic-rw2',\n\t\t\t};\n\t\t}\n\n\t\t// ASF_Header_Object first 80 bytes\n\t\tif (this.check([0x30, 0x26, 0xB2, 0x75, 0x8E, 0x66, 0xCF, 0x11, 0xA6, 0xD9])) {\n\t\t\tasync function readHeader() {\n\t\t\t\tconst guid = new Uint8Array(16);\n\t\t\t\tawait tokenizer.readBuffer(guid);\n\t\t\t\treturn {\n\t\t\t\t\tid: guid,\n\t\t\t\t\tsize: Number(await tokenizer.readToken(token_types__WEBPACK_IMPORTED_MODULE_0__.UINT64_LE)),\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tawait tokenizer.ignore(30);\n\t\t\t// Search for header should be in first 1KB of file.\n\t\t\twhile (tokenizer.position + 24 < tokenizer.fileInfo.size) {\n\t\t\t\tconst header = await readHeader();\n\t\t\t\tlet payload = header.size - 24;\n\t\t\t\tif (_check(header.id, [0x91, 0x07, 0xDC, 0xB7, 0xB7, 0xA9, 0xCF, 0x11, 0x8E, 0xE6, 0x00, 0xC0, 0x0C, 0x20, 0x53, 0x65])) {\n\t\t\t\t\t// Sync on Stream-Properties-Object (B7DC0791-A9B7-11CF-8EE6-00C00C205365)\n\t\t\t\t\tconst typeId = new Uint8Array(16);\n\t\t\t\t\tpayload -= await tokenizer.readBuffer(typeId);\n\n\t\t\t\t\tif (_check(typeId, [0x40, 0x9E, 0x69, 0xF8, 0x4D, 0x5B, 0xCF, 0x11, 0xA8, 0xFD, 0x00, 0x80, 0x5F, 0x5C, 0x44, 0x2B])) {\n\t\t\t\t\t\t// Found audio:\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\text: 'asf',\n\t\t\t\t\t\t\tmime: 'audio/x-ms-asf',\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\n\t\t\t\t\tif (_check(typeId, [0xC0, 0xEF, 0x19, 0xBC, 0x4D, 0x5B, 0xCF, 0x11, 0xA8, 0xFD, 0x00, 0x80, 0x5F, 0x5C, 0x44, 0x2B])) {\n\t\t\t\t\t\t// Found video:\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\text: 'asf',\n\t\t\t\t\t\t\tmime: 'video/x-ms-asf',\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t\tawait tokenizer.ignore(payload);\n\t\t\t}\n\n\t\t\t// Default to ASF generic extension\n\t\t\treturn {\n\t\t\t\text: 'asf',\n\t\t\t\tmime: 'application/vnd.ms-asf',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0xAB, 0x4B, 0x54, 0x58, 0x20, 0x31, 0x31, 0xBB, 0x0D, 0x0A, 0x1A, 0x0A])) {\n\t\t\treturn {\n\t\t\t\text: 'ktx',\n\t\t\t\tmime: 'image/ktx',\n\t\t\t};\n\t\t}\n\n\t\tif ((this.check([0x7E, 0x10, 0x04]) || this.check([0x7E, 0x18, 0x04])) && this.check([0x30, 0x4D, 0x49, 0x45], {offset: 4})) {\n\t\t\treturn {\n\t\t\t\text: 'mie',\n\t\t\t\tmime: 'application/x-mie',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x27, 0x0A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], {offset: 2})) {\n\t\t\treturn {\n\t\t\t\text: 'shp',\n\t\t\t\tmime: 'application/x-esri-shape',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0xFF, 0x4F, 0xFF, 0x51])) {\n\t\t\treturn {\n\t\t\t\text: 'j2c',\n\t\t\t\tmime: 'image/j2c',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x00, 0x00, 0x00, 0x0C, 0x6A, 0x50, 0x20, 0x20, 0x0D, 0x0A, 0x87, 0x0A])) {\n\t\t\t// JPEG-2000 family\n\n\t\t\tawait tokenizer.ignore(20);\n\t\t\tconst type = await tokenizer.readToken(new token_types__WEBPACK_IMPORTED_MODULE_0__.StringType(4, 'ascii'));\n\t\t\tswitch (type) {\n\t\t\t\tcase 'jp2 ':\n\t\t\t\t\treturn {\n\t\t\t\t\t\text: 'jp2',\n\t\t\t\t\t\tmime: 'image/jp2',\n\t\t\t\t\t};\n\t\t\t\tcase 'jpx ':\n\t\t\t\t\treturn {\n\t\t\t\t\t\text: 'jpx',\n\t\t\t\t\t\tmime: 'image/jpx',\n\t\t\t\t\t};\n\t\t\t\tcase 'jpm ':\n\t\t\t\t\treturn {\n\t\t\t\t\t\text: 'jpm',\n\t\t\t\t\t\tmime: 'image/jpm',\n\t\t\t\t\t};\n\t\t\t\tcase 'mjp2':\n\t\t\t\t\treturn {\n\t\t\t\t\t\text: 'mj2',\n\t\t\t\t\t\tmime: 'image/mj2',\n\t\t\t\t\t};\n\t\t\t\tdefault:\n\t\t\t\t\treturn;\n\t\t\t}\n\t\t}\n\n\t\tif (\n\t\t\tthis.check([0xFF, 0x0A])\n\t\t\t|| this.check([0x00, 0x00, 0x00, 0x0C, 0x4A, 0x58, 0x4C, 0x20, 0x0D, 0x0A, 0x87, 0x0A])\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'jxl',\n\t\t\t\tmime: 'image/jxl',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0xFE, 0xFF])) { // UTF-16-BOM-LE\n\t\t\tif (this.check([0, 60, 0, 63, 0, 120, 0, 109, 0, 108], {offset: 2})) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'xml',\n\t\t\t\t\tmime: 'application/xml',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\treturn undefined; // Some unknown text based format\n\t\t}\n\n\t\t// -- Unsafe signatures --\n\n\t\tif (\n\t\t\tthis.check([0x0, 0x0, 0x1, 0xBA])\n\t\t\t|| this.check([0x0, 0x0, 0x1, 0xB3])\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'mpg',\n\t\t\t\tmime: 'video/mpeg',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x00, 0x01, 0x00, 0x00, 0x00])) {\n\t\t\treturn {\n\t\t\t\text: 'ttf',\n\t\t\t\tmime: 'font/ttf',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x00, 0x00, 0x01, 0x00])) {\n\t\t\treturn {\n\t\t\t\text: 'ico',\n\t\t\t\tmime: 'image/x-icon',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x00, 0x00, 0x02, 0x00])) {\n\t\t\treturn {\n\t\t\t\text: 'cur',\n\t\t\t\tmime: 'image/x-icon',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1])) {\n\t\t\t// Detected Microsoft Compound File Binary File (MS-CFB) Format.\n\t\t\treturn {\n\t\t\t\text: 'cfb',\n\t\t\t\tmime: 'application/x-cfb',\n\t\t\t};\n\t\t}\n\n\t\t// Increase sample size from 12 to 256.\n\t\tawait tokenizer.peekBuffer(this.buffer, {length: Math.min(256, tokenizer.fileInfo.size), mayBeLess: true});\n\n\t\tif (this.check([0x61, 0x63, 0x73, 0x70], {offset: 36})) {\n\t\t\treturn {\n\t\t\t\text: 'icc',\n\t\t\t\tmime: 'application/vnd.iccprofile',\n\t\t\t};\n\t\t}\n\n\t\t// -- 15-byte signatures --\n\n\t\tif (this.checkString('BEGIN:')) {\n\t\t\tif (this.checkString('VCARD', {offset: 6})) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'vcf',\n\t\t\t\t\tmime: 'text/vcard',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tif (this.checkString('VCALENDAR', {offset: 6})) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'ics',\n\t\t\t\t\tmime: 'text/calendar',\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\n\t\t// `raf` is here just to keep all the raw image detectors together.\n\t\tif (this.checkString('FUJIFILMCCD-RAW')) {\n\t\t\treturn {\n\t\t\t\text: 'raf',\n\t\t\t\tmime: 'image/x-fujifilm-raf',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('Extended Module:')) {\n\t\t\treturn {\n\t\t\t\text: 'xm',\n\t\t\t\tmime: 'audio/x-xm',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('Creative Voice File')) {\n\t\t\treturn {\n\t\t\t\text: 'voc',\n\t\t\t\tmime: 'audio/x-voc',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x04, 0x00, 0x00, 0x00]) && this.buffer.length >= 16) { // Rough & quick check Pickle/ASAR\n\t\t\tconst jsonSize = new DataView(this.buffer.buffer).getUint32(12, true);\n\n\t\t\tif (jsonSize > 12 && this.buffer.length >= jsonSize + 16) {\n\t\t\t\ttry {\n\t\t\t\t\tconst header = new TextDecoder().decode(this.buffer.slice(16, jsonSize + 16));\n\t\t\t\t\tconst json = JSON.parse(header);\n\t\t\t\t\t// Check if Pickle is ASAR\n\t\t\t\t\tif (json.files) { // Final check, assuring Pickle/ASAR format\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\text: 'asar',\n\t\t\t\t\t\t\tmime: 'application/x-asar',\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t} catch {}\n\t\t\t}\n\t\t}\n\n\t\tif (this.check([0x06, 0x0E, 0x2B, 0x34, 0x02, 0x05, 0x01, 0x01, 0x0D, 0x01, 0x02, 0x01, 0x01, 0x02])) {\n\t\t\treturn {\n\t\t\t\text: 'mxf',\n\t\t\t\tmime: 'application/mxf',\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('SCRM', {offset: 44})) {\n\t\t\treturn {\n\t\t\t\text: 's3m',\n\t\t\t\tmime: 'audio/x-s3m',\n\t\t\t};\n\t\t}\n\n\t\t// Raw MPEG-2 transport stream (188-byte packets)\n\t\tif (this.check([0x47]) && this.check([0x47], {offset: 188})) {\n\t\t\treturn {\n\t\t\t\text: 'mts',\n\t\t\t\tmime: 'video/mp2t',\n\t\t\t};\n\t\t}\n\n\t\t// Blu-ray Disc Audio-Video (BDAV) MPEG-2 transport stream has 4-byte TP_extra_header before each 188-byte packet\n\t\tif (this.check([0x47], {offset: 4}) && this.check([0x47], {offset: 196})) {\n\t\t\treturn {\n\t\t\t\text: 'mts',\n\t\t\t\tmime: 'video/mp2t',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x42, 0x4F, 0x4F, 0x4B, 0x4D, 0x4F, 0x42, 0x49], {offset: 60})) {\n\t\t\treturn {\n\t\t\t\text: 'mobi',\n\t\t\t\tmime: 'application/x-mobipocket-ebook',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x44, 0x49, 0x43, 0x4D], {offset: 128})) {\n\t\t\treturn {\n\t\t\t\text: 'dcm',\n\t\t\t\tmime: 'application/dicom',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x4C, 0x00, 0x00, 0x00, 0x01, 0x14, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x46])) {\n\t\t\treturn {\n\t\t\t\text: 'lnk',\n\t\t\t\tmime: 'application/x.ms.shortcut', // Invented by us\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x62, 0x6F, 0x6F, 0x6B, 0x00, 0x00, 0x00, 0x00, 0x6D, 0x61, 0x72, 0x6B, 0x00, 0x00, 0x00, 0x00])) {\n\t\t\treturn {\n\t\t\t\text: 'alias',\n\t\t\t\tmime: 'application/x.apple.alias', // Invented by us\n\t\t\t};\n\t\t}\n\n\t\tif (this.checkString('Kaydara FBX Binary  \\u0000')) {\n\t\t\treturn {\n\t\t\t\text: 'fbx',\n\t\t\t\tmime: 'application/x.autodesk.fbx', // Invented by us\n\t\t\t};\n\t\t}\n\n\t\tif (\n\t\t\tthis.check([0x4C, 0x50], {offset: 34})\n\t\t\t&& (\n\t\t\t\tthis.check([0x00, 0x00, 0x01], {offset: 8})\n\t\t\t\t|| this.check([0x01, 0x00, 0x02], {offset: 8})\n\t\t\t\t|| this.check([0x02, 0x00, 0x02], {offset: 8})\n\t\t\t)\n\t\t) {\n\t\t\treturn {\n\t\t\t\text: 'eot',\n\t\t\t\tmime: 'application/vnd.ms-fontobject',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0x06, 0x06, 0xED, 0xF5, 0xD8, 0x1D, 0x46, 0xE5, 0xBD, 0x31, 0xEF, 0xE7, 0xFE, 0x74, 0xB7, 0x1D])) {\n\t\t\treturn {\n\t\t\t\text: 'indd',\n\t\t\t\tmime: 'application/x-indesign',\n\t\t\t};\n\t\t}\n\n\t\t// Increase sample size from 256 to 512\n\t\tawait tokenizer.peekBuffer(this.buffer, {length: Math.min(512, tokenizer.fileInfo.size), mayBeLess: true});\n\n\t\t// Requires a buffer size of 512 bytes\n\t\tif ((0,_util_js__WEBPACK_IMPORTED_MODULE_2__.tarHeaderChecksumMatches)(this.buffer)) {\n\t\t\treturn {\n\t\t\t\text: 'tar',\n\t\t\t\tmime: 'application/x-tar',\n\t\t\t};\n\t\t}\n\n\t\tif (this.check([0xFF, 0xFE])) { // UTF-16-BOM-BE\n\t\t\tif (this.check([60, 0, 63, 0, 120, 0, 109, 0, 108, 0], {offset: 2})) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'xml',\n\t\t\t\t\tmime: 'application/xml',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tif (this.check([0xFF, 0x0E, 0x53, 0x00, 0x6B, 0x00, 0x65, 0x00, 0x74, 0x00, 0x63, 0x00, 0x68, 0x00, 0x55, 0x00, 0x70, 0x00, 0x20, 0x00, 0x4D, 0x00, 0x6F, 0x00, 0x64, 0x00, 0x65, 0x00, 0x6C, 0x00], {offset: 2})) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'skp',\n\t\t\t\t\tmime: 'application/vnd.sketchup.skp',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\treturn undefined; // Some text based format\n\t\t}\n\n\t\tif (this.checkString('-----BEGIN PGP MESSAGE-----')) {\n\t\t\treturn {\n\t\t\t\text: 'pgp',\n\t\t\t\tmime: 'application/pgp-encrypted',\n\t\t\t};\n\t\t}\n\n\t\t// Check MPEG 1 or 2 Layer 3 header, or 'layer 0' for ADTS (MPEG sync-word 0xFFE)\n\t\tif (this.buffer.length >= 2 && this.check([0xFF, 0xE0], {offset: 0, mask: [0xFF, 0xE0]})) {\n\t\t\tif (this.check([0x10], {offset: 1, mask: [0x16]})) {\n\t\t\t\t// Check for (ADTS) MPEG-2\n\t\t\t\tif (this.check([0x08], {offset: 1, mask: [0x08]})) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\text: 'aac',\n\t\t\t\t\t\tmime: 'audio/aac',\n\t\t\t\t\t};\n\t\t\t\t}\n\n\t\t\t\t// Must be (ADTS) MPEG-4\n\t\t\t\treturn {\n\t\t\t\t\text: 'aac',\n\t\t\t\t\tmime: 'audio/aac',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t// MPEG 1 or 2 Layer 3 header\n\t\t\t// Check for MPEG layer 3\n\t\t\tif (this.check([0x02], {offset: 1, mask: [0x06]})) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'mp3',\n\t\t\t\t\tmime: 'audio/mpeg',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t// Check for MPEG layer 2\n\t\t\tif (this.check([0x04], {offset: 1, mask: [0x06]})) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'mp2',\n\t\t\t\t\tmime: 'audio/mpeg',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t// Check for MPEG layer 1\n\t\t\tif (this.check([0x06], {offset: 1, mask: [0x06]})) {\n\t\t\t\treturn {\n\t\t\t\t\text: 'mp1',\n\t\t\t\t\tmime: 'audio/mpeg',\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t}\n\n\tasync readTiffTag(bigEndian) {\n\t\tconst tagId = await this.tokenizer.readToken(bigEndian ? token_types__WEBPACK_IMPORTED_MODULE_0__.UINT16_BE : token_types__WEBPACK_IMPORTED_MODULE_0__.UINT16_LE);\n\t\tthis.tokenizer.ignore(10);\n\t\tswitch (tagId) {\n\t\t\tcase 50_341:\n\t\t\t\treturn {\n\t\t\t\t\text: 'arw',\n\t\t\t\t\tmime: 'image/x-sony-arw',\n\t\t\t\t};\n\t\t\tcase 50_706:\n\t\t\t\treturn {\n\t\t\t\t\text: 'dng',\n\t\t\t\t\tmime: 'image/x-adobe-dng',\n\t\t\t\t};\n\t\t\tdefault:\n\t\t}\n\t}\n\n\tasync readTiffIFD(bigEndian) {\n\t\tconst numberOfTags = await this.tokenizer.readToken(bigEndian ? token_types__WEBPACK_IMPORTED_MODULE_0__.UINT16_BE : token_types__WEBPACK_IMPORTED_MODULE_0__.UINT16_LE);\n\t\tfor (let n = 0; n < numberOfTags; ++n) {\n\t\t\tconst fileType = await this.readTiffTag(bigEndian);\n\t\t\tif (fileType) {\n\t\t\t\treturn fileType;\n\t\t\t}\n\t\t}\n\t}\n\n\tasync readTiffHeader(bigEndian) {\n\t\tconst version = (bigEndian ? token_types__WEBPACK_IMPORTED_MODULE_0__.UINT16_BE : token_types__WEBPACK_IMPORTED_MODULE_0__.UINT16_LE).get(this.buffer, 2);\n\t\tconst ifdOffset = (bigEndian ? token_types__WEBPACK_IMPORTED_MODULE_0__.UINT32_BE : token_types__WEBPACK_IMPORTED_MODULE_0__.UINT32_LE).get(this.buffer, 4);\n\n\t\tif (version === 42) {\n\t\t\t// TIFF file header\n\t\t\tif (ifdOffset >= 6) {\n\t\t\t\tif (this.checkString('CR', {offset: 8})) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\text: 'cr2',\n\t\t\t\t\t\tmime: 'image/x-canon-cr2',\n\t\t\t\t\t};\n\t\t\t\t}\n\n\t\t\t\tif (ifdOffset >= 8 && (this.check([0x1C, 0x00, 0xFE, 0x00], {offset: 8}) || this.check([0x1F, 0x00, 0x0B, 0x00], {offset: 8}))) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\text: 'nef',\n\t\t\t\t\t\tmime: 'image/x-nikon-nef',\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tawait this.tokenizer.ignore(ifdOffset);\n\t\t\tconst fileType = await this.readTiffIFD(bigEndian);\n\t\t\treturn fileType ?? {\n\t\t\t\text: 'tif',\n\t\t\t\tmime: 'image/tiff',\n\t\t\t};\n\t\t}\n\n\t\tif (version === 43) {\t// Big TIFF file header\n\t\t\treturn {\n\t\t\t\text: 'tif',\n\t\t\t\tmime: 'image/tiff',\n\t\t\t};\n\t\t}\n\t}\n}\n\nconst supportedExtensions = new Set(_supported_js__WEBPACK_IMPORTED_MODULE_4__.extensions);\nconst supportedMimeTypes = new Set(_supported_js__WEBPACK_IMPORTED_MODULE_4__.mimeTypes);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/file-type/core.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/file-type/index.js":
/*!*****************************************!*\
  !*** ./node_modules/file-type/index.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FileTypeParser: () => (/* reexport safe */ _core_js__WEBPACK_IMPORTED_MODULE_2__.FileTypeParser),\n/* harmony export */   NodeFileTypeParser: () => (/* binding */ NodeFileTypeParser),\n/* harmony export */   fileTypeFromBlob: () => (/* reexport safe */ _core_js__WEBPACK_IMPORTED_MODULE_2__.fileTypeFromBlob),\n/* harmony export */   fileTypeFromBuffer: () => (/* reexport safe */ _core_js__WEBPACK_IMPORTED_MODULE_2__.fileTypeFromBuffer),\n/* harmony export */   fileTypeFromFile: () => (/* binding */ fileTypeFromFile),\n/* harmony export */   fileTypeFromStream: () => (/* binding */ fileTypeFromStream),\n/* harmony export */   fileTypeFromTokenizer: () => (/* reexport safe */ _core_js__WEBPACK_IMPORTED_MODULE_2__.fileTypeFromTokenizer),\n/* harmony export */   fileTypeStream: () => (/* binding */ fileTypeStream),\n/* harmony export */   supportedExtensions: () => (/* reexport safe */ _core_js__WEBPACK_IMPORTED_MODULE_2__.supportedExtensions),\n/* harmony export */   supportedMimeTypes: () => (/* reexport safe */ _core_js__WEBPACK_IMPORTED_MODULE_2__.supportedMimeTypes)\n/* harmony export */ });\n/* harmony import */ var node_stream_web__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:stream/web */ \"node:stream/web\");\n/* harmony import */ var strtok3__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! strtok3 */ \"(rsc)/./node_modules/strtok3/lib/index.js\");\n/* harmony import */ var _core_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./core.js */ \"(rsc)/./node_modules/file-type/core.js\");\n/**\nNode.js specific entry point.\n*/\n\n\n\n\n\nclass NodeFileTypeParser extends _core_js__WEBPACK_IMPORTED_MODULE_2__.FileTypeParser {\n\tasync fromStream(stream) {\n\t\tconst tokenizer = await (stream instanceof node_stream_web__WEBPACK_IMPORTED_MODULE_0__.ReadableStream ? strtok3__WEBPACK_IMPORTED_MODULE_1__.fromWebStream(stream) : strtok3__WEBPACK_IMPORTED_MODULE_1__.fromStream(stream));\n\t\ttry {\n\t\t\treturn super.fromTokenizer(tokenizer);\n\t\t} finally {\n\t\t\tawait tokenizer.close();\n\t\t}\n\t}\n\n\tasync fromFile(path) {\n\t\tconst tokenizer = await strtok3__WEBPACK_IMPORTED_MODULE_1__.fromFile(path);\n\t\ttry {\n\t\t\treturn await super.fromTokenizer(tokenizer);\n\t\t} finally {\n\t\t\tawait tokenizer.close();\n\t\t}\n\t}\n\n\tasync toDetectionStream(readableStream, options = {}) {\n\t\tconst {default: stream} = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! node:stream */ \"node:stream\", 19));\n\t\tconst {sampleSize = _core_js__WEBPACK_IMPORTED_MODULE_2__.reasonableDetectionSizeInBytes} = options;\n\n\t\treturn new Promise((resolve, reject) => {\n\t\t\treadableStream.on('error', reject);\n\n\t\t\treadableStream.once('readable', () => {\n\t\t\t\t(async () => {\n\t\t\t\t\ttry {\n\t\t\t\t\t\t// Set up output stream\n\t\t\t\t\t\tconst pass = new stream.PassThrough();\n\t\t\t\t\t\tconst outputStream = stream.pipeline ? stream.pipeline(readableStream, pass, () => {}) : readableStream.pipe(pass);\n\n\t\t\t\t\t\t// Read the input stream and detect the filetype\n\t\t\t\t\t\tconst chunk = readableStream.read(sampleSize) ?? readableStream.read() ?? new Uint8Array(0);\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tpass.fileType = await this.fromBuffer(chunk);\n\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\tif (error instanceof strtok3__WEBPACK_IMPORTED_MODULE_1__.EndOfStreamError) {\n\t\t\t\t\t\t\t\tpass.fileType = undefined;\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\treject(error);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tresolve(outputStream);\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\treject(error);\n\t\t\t\t\t}\n\t\t\t\t})();\n\t\t\t});\n\t\t});\n\t}\n}\n\nasync function fileTypeFromFile(path, fileTypeOptions) {\n\treturn (new NodeFileTypeParser(fileTypeOptions)).fromFile(path, fileTypeOptions);\n}\n\nasync function fileTypeFromStream(stream, fileTypeOptions) {\n\treturn (new NodeFileTypeParser(fileTypeOptions)).fromStream(stream);\n}\n\nasync function fileTypeStream(readableStream, options = {}) {\n\treturn new NodeFileTypeParser().toDetectionStream(readableStream, options);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/file-type/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/file-type/supported.js":
/*!*********************************************!*\
  !*** ./node_modules/file-type/supported.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extensions: () => (/* binding */ extensions),\n/* harmony export */   mimeTypes: () => (/* binding */ mimeTypes)\n/* harmony export */ });\nconst extensions = [\n\t'jpg',\n\t'png',\n\t'apng',\n\t'gif',\n\t'webp',\n\t'flif',\n\t'xcf',\n\t'cr2',\n\t'cr3',\n\t'orf',\n\t'arw',\n\t'dng',\n\t'nef',\n\t'rw2',\n\t'raf',\n\t'tif',\n\t'bmp',\n\t'icns',\n\t'jxr',\n\t'psd',\n\t'indd',\n\t'zip',\n\t'tar',\n\t'rar',\n\t'gz',\n\t'bz2',\n\t'7z',\n\t'dmg',\n\t'mp4',\n\t'mid',\n\t'mkv',\n\t'webm',\n\t'mov',\n\t'avi',\n\t'mpg',\n\t'mp2',\n\t'mp3',\n\t'm4a',\n\t'oga',\n\t'ogg',\n\t'ogv',\n\t'opus',\n\t'flac',\n\t'wav',\n\t'spx',\n\t'amr',\n\t'pdf',\n\t'epub',\n\t'elf',\n\t'macho',\n\t'exe',\n\t'swf',\n\t'rtf',\n\t'wasm',\n\t'woff',\n\t'woff2',\n\t'eot',\n\t'ttf',\n\t'otf',\n\t'ico',\n\t'flv',\n\t'ps',\n\t'xz',\n\t'sqlite',\n\t'nes',\n\t'crx',\n\t'xpi',\n\t'cab',\n\t'deb',\n\t'ar',\n\t'rpm',\n\t'Z',\n\t'lz',\n\t'cfb',\n\t'mxf',\n\t'mts',\n\t'blend',\n\t'bpg',\n\t'docx',\n\t'pptx',\n\t'xlsx',\n\t'3gp',\n\t'3g2',\n\t'j2c',\n\t'jp2',\n\t'jpm',\n\t'jpx',\n\t'mj2',\n\t'aif',\n\t'qcp',\n\t'odt',\n\t'ods',\n\t'odp',\n\t'xml',\n\t'mobi',\n\t'heic',\n\t'cur',\n\t'ktx',\n\t'ape',\n\t'wv',\n\t'dcm',\n\t'ics',\n\t'glb',\n\t'pcap',\n\t'dsf',\n\t'lnk',\n\t'alias',\n\t'voc',\n\t'ac3',\n\t'm4v',\n\t'm4p',\n\t'm4b',\n\t'f4v',\n\t'f4p',\n\t'f4b',\n\t'f4a',\n\t'mie',\n\t'asf',\n\t'ogm',\n\t'ogx',\n\t'mpc',\n\t'arrow',\n\t'shp',\n\t'aac',\n\t'mp1',\n\t'it',\n\t's3m',\n\t'xm',\n\t'ai',\n\t'skp',\n\t'avif',\n\t'eps',\n\t'lzh',\n\t'pgp',\n\t'asar',\n\t'stl',\n\t'chm',\n\t'3mf',\n\t'zst',\n\t'jxl',\n\t'vcf',\n\t'jls',\n\t'pst',\n\t'dwg',\n\t'parquet',\n\t'class',\n\t'arj',\n\t'cpio',\n\t'ace',\n\t'avro',\n\t'icc',\n\t'fbx',\n\t'vsdx',\n];\n\nconst mimeTypes = [\n\t'image/jpeg',\n\t'image/png',\n\t'image/gif',\n\t'image/webp',\n\t'image/flif',\n\t'image/x-xcf',\n\t'image/x-canon-cr2',\n\t'image/x-canon-cr3',\n\t'image/tiff',\n\t'image/bmp',\n\t'image/vnd.ms-photo',\n\t'image/vnd.adobe.photoshop',\n\t'application/x-indesign',\n\t'application/epub+zip',\n\t'application/x-xpinstall',\n\t'application/vnd.oasis.opendocument.text',\n\t'application/vnd.oasis.opendocument.spreadsheet',\n\t'application/vnd.oasis.opendocument.presentation',\n\t'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n\t'application/vnd.openxmlformats-officedocument.presentationml.presentation',\n\t'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n\t'application/zip',\n\t'application/x-tar',\n\t'application/x-rar-compressed',\n\t'application/gzip',\n\t'application/x-bzip2',\n\t'application/x-7z-compressed',\n\t'application/x-apple-diskimage',\n\t'application/x-apache-arrow',\n\t'video/mp4',\n\t'audio/midi',\n\t'video/x-matroska',\n\t'video/webm',\n\t'video/quicktime',\n\t'video/vnd.avi',\n\t'audio/wav',\n\t'audio/qcelp',\n\t'audio/x-ms-asf',\n\t'video/x-ms-asf',\n\t'application/vnd.ms-asf',\n\t'video/mpeg',\n\t'video/3gpp',\n\t'audio/mpeg',\n\t'audio/mp4', // RFC 4337\n\t'audio/opus',\n\t'video/ogg',\n\t'audio/ogg',\n\t'application/ogg',\n\t'audio/x-flac',\n\t'audio/ape',\n\t'audio/wavpack',\n\t'audio/amr',\n\t'application/pdf',\n\t'application/x-elf',\n\t'application/x-mach-binary',\n\t'application/x-msdownload',\n\t'application/x-shockwave-flash',\n\t'application/rtf',\n\t'application/wasm',\n\t'font/woff',\n\t'font/woff2',\n\t'application/vnd.ms-fontobject',\n\t'font/ttf',\n\t'font/otf',\n\t'image/x-icon',\n\t'video/x-flv',\n\t'application/postscript',\n\t'application/eps',\n\t'application/x-xz',\n\t'application/x-sqlite3',\n\t'application/x-nintendo-nes-rom',\n\t'application/x-google-chrome-extension',\n\t'application/vnd.ms-cab-compressed',\n\t'application/x-deb',\n\t'application/x-unix-archive',\n\t'application/x-rpm',\n\t'application/x-compress',\n\t'application/x-lzip',\n\t'application/x-cfb',\n\t'application/x-mie',\n\t'application/mxf',\n\t'video/mp2t',\n\t'application/x-blender',\n\t'image/bpg',\n\t'image/j2c',\n\t'image/jp2',\n\t'image/jpx',\n\t'image/jpm',\n\t'image/mj2',\n\t'audio/aiff',\n\t'application/xml',\n\t'application/x-mobipocket-ebook',\n\t'image/heif',\n\t'image/heif-sequence',\n\t'image/heic',\n\t'image/heic-sequence',\n\t'image/icns',\n\t'image/ktx',\n\t'application/dicom',\n\t'audio/x-musepack',\n\t'text/calendar',\n\t'text/vcard',\n\t'model/gltf-binary',\n\t'application/vnd.tcpdump.pcap',\n\t'audio/x-dsf', // Non-standard\n\t'application/x.ms.shortcut', // Invented by us\n\t'application/x.apple.alias', // Invented by us\n\t'audio/x-voc',\n\t'audio/vnd.dolby.dd-raw',\n\t'audio/x-m4a',\n\t'image/apng',\n\t'image/x-olympus-orf',\n\t'image/x-sony-arw',\n\t'image/x-adobe-dng',\n\t'image/x-nikon-nef',\n\t'image/x-panasonic-rw2',\n\t'image/x-fujifilm-raf',\n\t'video/x-m4v',\n\t'video/3gpp2',\n\t'application/x-esri-shape',\n\t'audio/aac',\n\t'audio/x-it',\n\t'audio/x-s3m',\n\t'audio/x-xm',\n\t'video/MP1S',\n\t'video/MP2P',\n\t'application/vnd.sketchup.skp',\n\t'image/avif',\n\t'application/x-lzh-compressed',\n\t'application/pgp-encrypted',\n\t'application/x-asar',\n\t'model/stl',\n\t'application/vnd.ms-htmlhelp',\n\t'model/3mf',\n\t'image/jxl',\n\t'application/zstd',\n\t'image/jls',\n\t'application/vnd.ms-outlook',\n\t'image/vnd.dwg',\n\t'application/x-parquet',\n\t'application/java-vm',\n\t'application/x-arj',\n\t'application/x-cpio',\n\t'application/x-ace-compressed',\n\t'application/avro',\n\t'application/vnd.iccprofile',\n\t'application/x.autodesk.fbx', // Invented by us\n\t'application/vnd.visio',\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/file-type/supported.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/file-type/util.js":
/*!****************************************!*\
  !*** ./node_modules/file-type/util.js ***!
  \****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stringToBytes: () => (/* binding */ stringToBytes),\n/* harmony export */   tarHeaderChecksumMatches: () => (/* binding */ tarHeaderChecksumMatches),\n/* harmony export */   uint32SyncSafeToken: () => (/* binding */ uint32SyncSafeToken)\n/* harmony export */ });\n/* harmony import */ var token_types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! token-types */ \"(rsc)/./node_modules/token-types/lib/index.js\");\n\n\nfunction stringToBytes(string) {\n\treturn [...string].map(character => character.charCodeAt(0)); // eslint-disable-line unicorn/prefer-code-point\n}\n\n/**\nChecks whether the TAR checksum is valid.\n\n@param {Uint8Array} arrayBuffer - The TAR header `[offset ... offset + 512]`.\n@param {number} offset - TAR header offset.\n@returns {boolean} `true` if the TAR checksum is valid, otherwise `false`.\n*/\nfunction tarHeaderChecksumMatches(arrayBuffer, offset = 0) {\n\tconst readSum = Number.parseInt(new token_types__WEBPACK_IMPORTED_MODULE_0__.StringType(6).get(arrayBuffer, 148).replace(/\\0.*$/, '').trim(), 8); // Read sum in header\n\tif (Number.isNaN(readSum)) {\n\t\treturn false;\n\t}\n\n\tlet sum = 8 * 0x20; // Initialize signed bit sum\n\n\tfor (let index = offset; index < offset + 148; index++) {\n\t\tsum += arrayBuffer[index];\n\t}\n\n\tfor (let index = offset + 156; index < offset + 512; index++) {\n\t\tsum += arrayBuffer[index];\n\t}\n\n\treturn readSum === sum;\n}\n\n/**\nID3 UINT32 sync-safe tokenizer token.\n28 bits (representing up to 256MB) integer, the msb is 0 to avoid \"false syncsignals\".\n*/\nconst uint32SyncSafeToken = {\n\tget: (buffer, offset) => (buffer[offset + 3] & 0x7F) | ((buffer[offset + 2]) << 7) | ((buffer[offset + 1]) << 14) | ((buffer[offset]) << 21),\n\tlen: 4,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZmlsZS10eXBlL3V0aWwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUF1Qzs7QUFFaEM7QUFDUCwrREFBK0Q7QUFDL0Q7O0FBRUE7QUFDQTs7QUFFQSxRQUFRLFlBQVk7QUFDcEIsUUFBUSxRQUFRO0FBQ2hCLFVBQVUsU0FBUztBQUNuQjtBQUNPO0FBQ1AscUNBQXFDLG1EQUFVLDJEQUEyRDtBQUMxRztBQUNBO0FBQ0E7O0FBRUEscUJBQXFCOztBQUVyQiwwQkFBMEIsc0JBQXNCO0FBQ2hEO0FBQ0E7O0FBRUEsZ0NBQWdDLHNCQUFzQjtBQUN0RDtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXFdvcmtcXFBheWxvYWRcXHRlc3QtcGF5bG9hZC1qc29uXFxjb3Jwb3JhdGUtd2Vic2l0ZVxcbm9kZV9tb2R1bGVzXFxmaWxlLXR5cGVcXHV0aWwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtTdHJpbmdUeXBlfSBmcm9tICd0b2tlbi10eXBlcyc7XG5cbmV4cG9ydCBmdW5jdGlvbiBzdHJpbmdUb0J5dGVzKHN0cmluZykge1xuXHRyZXR1cm4gWy4uLnN0cmluZ10ubWFwKGNoYXJhY3RlciA9PiBjaGFyYWN0ZXIuY2hhckNvZGVBdCgwKSk7IC8vIGVzbGludC1kaXNhYmxlLWxpbmUgdW5pY29ybi9wcmVmZXItY29kZS1wb2ludFxufVxuXG4vKipcbkNoZWNrcyB3aGV0aGVyIHRoZSBUQVIgY2hlY2tzdW0gaXMgdmFsaWQuXG5cbkBwYXJhbSB7VWludDhBcnJheX0gYXJyYXlCdWZmZXIgLSBUaGUgVEFSIGhlYWRlciBgW29mZnNldCAuLi4gb2Zmc2V0ICsgNTEyXWAuXG5AcGFyYW0ge251bWJlcn0gb2Zmc2V0IC0gVEFSIGhlYWRlciBvZmZzZXQuXG5AcmV0dXJucyB7Ym9vbGVhbn0gYHRydWVgIGlmIHRoZSBUQVIgY2hlY2tzdW0gaXMgdmFsaWQsIG90aGVyd2lzZSBgZmFsc2VgLlxuKi9cbmV4cG9ydCBmdW5jdGlvbiB0YXJIZWFkZXJDaGVja3N1bU1hdGNoZXMoYXJyYXlCdWZmZXIsIG9mZnNldCA9IDApIHtcblx0Y29uc3QgcmVhZFN1bSA9IE51bWJlci5wYXJzZUludChuZXcgU3RyaW5nVHlwZSg2KS5nZXQoYXJyYXlCdWZmZXIsIDE0OCkucmVwbGFjZSgvXFwwLiokLywgJycpLnRyaW0oKSwgOCk7IC8vIFJlYWQgc3VtIGluIGhlYWRlclxuXHRpZiAoTnVtYmVyLmlzTmFOKHJlYWRTdW0pKSB7XG5cdFx0cmV0dXJuIGZhbHNlO1xuXHR9XG5cblx0bGV0IHN1bSA9IDggKiAweDIwOyAvLyBJbml0aWFsaXplIHNpZ25lZCBiaXQgc3VtXG5cblx0Zm9yIChsZXQgaW5kZXggPSBvZmZzZXQ7IGluZGV4IDwgb2Zmc2V0ICsgMTQ4OyBpbmRleCsrKSB7XG5cdFx0c3VtICs9IGFycmF5QnVmZmVyW2luZGV4XTtcblx0fVxuXG5cdGZvciAobGV0IGluZGV4ID0gb2Zmc2V0ICsgMTU2OyBpbmRleCA8IG9mZnNldCArIDUxMjsgaW5kZXgrKykge1xuXHRcdHN1bSArPSBhcnJheUJ1ZmZlcltpbmRleF07XG5cdH1cblxuXHRyZXR1cm4gcmVhZFN1bSA9PT0gc3VtO1xufVxuXG4vKipcbklEMyBVSU5UMzIgc3luYy1zYWZlIHRva2VuaXplciB0b2tlbi5cbjI4IGJpdHMgKHJlcHJlc2VudGluZyB1cCB0byAyNTZNQikgaW50ZWdlciwgdGhlIG1zYiBpcyAwIHRvIGF2b2lkIFwiZmFsc2Ugc3luY3NpZ25hbHNcIi5cbiovXG5leHBvcnQgY29uc3QgdWludDMyU3luY1NhZmVUb2tlbiA9IHtcblx0Z2V0OiAoYnVmZmVyLCBvZmZzZXQpID0+IChidWZmZXJbb2Zmc2V0ICsgM10gJiAweDdGKSB8ICgoYnVmZmVyW29mZnNldCArIDJdKSA8PCA3KSB8ICgoYnVmZmVyW29mZnNldCArIDFdKSA8PCAxNCkgfCAoKGJ1ZmZlcltvZmZzZXRdKSA8PCAyMSksXG5cdGxlbjogNCxcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/file-type/util.js\n");

/***/ })

};
;