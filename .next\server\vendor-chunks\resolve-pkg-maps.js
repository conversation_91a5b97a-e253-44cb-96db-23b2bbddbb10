"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/resolve-pkg-maps";
exports.ids = ["vendor-chunks/resolve-pkg-maps"];
exports.modules = {

/***/ "(rsc)/./node_modules/resolve-pkg-maps/dist/index.mjs":
/*!******************************************************!*\
  !*** ./node_modules/resolve-pkg-maps/dist/index.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveExports: () => (/* binding */ v),\n/* harmony export */   resolveImports: () => (/* binding */ T)\n/* harmony export */ });\nconst A=r=>r!==null&&typeof r==\"object\",a=(r,t)=>Object.assign(new Error(`[${r}]: ${t}`),{code:r}),_=\"ERR_INVALID_PACKAGE_CONFIG\",E=\"ERR_INVALID_PACKAGE_TARGET\",I=\"ERR_PACKAGE_PATH_NOT_EXPORTED\",P=\"ERR_PACKAGE_IMPORT_NOT_DEFINED\",R=/^\\d+$/,O=/^(\\.{1,2}|node_modules)$/i,w=/\\/|\\\\/;var h=(r=>(r.Export=\"exports\",r.Import=\"imports\",r))(h||{});const f=(r,t,e,o,c)=>{if(t==null)return[];if(typeof t==\"string\"){const[n,...i]=t.split(w);if(n===\"..\"||i.some(l=>O.test(l)))throw a(E,`Invalid \"${r}\" target \"${t}\" defined in the package config`);return[c?t.replace(/\\*/g,c):t]}if(Array.isArray(t))return t.flatMap(n=>f(r,n,e,o,c));if(A(t)){for(const n of Object.keys(t)){if(R.test(n))throw a(_,\"Cannot contain numeric property keys\");if(n===\"default\"||o.includes(n))return f(r,t[n],e,o,c)}return[]}throw a(E,`Invalid \"${r}\" target \"${t}\"`)},s=\"*\",m=(r,t)=>{const e=r.indexOf(s),o=t.indexOf(s);return e===o?t.length>r.length:o>e};function d(r,t){if(!t.includes(s)&&r.hasOwnProperty(t))return[t];let e,o;for(const c of Object.keys(r))if(c.includes(s)){const[n,i,l]=c.split(s);if(l===void 0&&t.startsWith(n)&&t.endsWith(i)){const g=t.slice(n.length,-i.length||void 0);g&&(!e||m(e,c))&&(e=c,o=g)}}return[e,o]}const p=r=>Object.keys(r).reduce((t,e)=>{const o=e===\"\"||e[0]!==\".\";if(t===void 0||t===o)return o;throw a(_,'\"exports\" cannot contain some keys starting with \".\" and some not')},void 0),u=/^\\w+:/,v=(r,t,e)=>{if(!r)throw new Error('\"exports\" is required');t=t===\"\"?\".\":`./${t}`,(typeof r==\"string\"||Array.isArray(r)||A(r)&&p(r))&&(r={\".\":r});const[o,c]=d(r,t),n=f(h.Export,r[o],t,e,c);if(n.length===0)throw a(I,t===\".\"?'No \"exports\" main defined':`Package subpath '${t}' is not defined by \"exports\"`);for(const i of n)if(!i.startsWith(\"./\")&&!u.test(i))throw a(E,`Invalid \"exports\" target \"${i}\" defined in the package config`);return n},T=(r,t,e)=>{if(!r)throw new Error('\"imports\" is required');const[o,c]=d(r,t),n=f(h.Import,r[o],t,e,c);if(n.length===0)throw a(P,`Package import specifier \"${t}\" is not defined in package`);return n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/resolve-pkg-maps/dist/index.mjs\n");

/***/ })

};
;