"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/sanitize-filename";
exports.ids = ["vendor-chunks/sanitize-filename"];
exports.modules = {

/***/ "(rsc)/./node_modules/sanitize-filename/index.js":
/*!*************************************************!*\
  !*** ./node_modules/sanitize-filename/index.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*jshint node:true*/\n\n\n/**\n * Replaces characters in strings that are illegal/unsafe for filenames.\n * Unsafe characters are either removed or replaced by a substitute set\n * in the optional `options` object.\n *\n * Illegal Characters on Various Operating Systems\n * / ? < > \\ : * | \"\n * https://kb.acronis.com/content/39790\n *\n * Unicode Control codes\n * C0 0x00-0x1f & C1 (0x80-0x9f)\n * http://en.wikipedia.org/wiki/C0_and_C1_control_codes\n *\n * Reserved filenames on Unix-based systems (\".\", \"..\")\n * Reserved filenames in Windows (\"CON\", \"PRN\", \"AUX\", \"NUL\", \"COM1\",\n * \"COM2\", \"COM3\", \"COM4\", \"COM5\", \"COM6\", \"COM7\", \"COM8\", \"COM9\",\n * \"LPT1\", \"LPT2\", \"LPT3\", \"LPT4\", \"LPT5\", \"LPT6\", \"LPT7\", \"LPT8\", and\n * \"LPT9\") case-insesitively and with or without filename extensions.\n *\n * Capped at 255 characters in length.\n * http://unix.stackexchange.com/questions/32795/what-is-the-maximum-allowed-filename-and-folder-size-with-ecryptfs\n *\n * @param  {String} input   Original filename\n * @param  {Object} options {replacement: String | Function }\n * @return {String}         Sanitized filename\n */\n\nvar truncate = __webpack_require__(/*! truncate-utf8-bytes */ \"(rsc)/./node_modules/truncate-utf8-bytes/index.js\");\n\nvar illegalRe = /[\\/\\?<>\\\\:\\*\\|\"]/g;\nvar controlRe = /[\\x00-\\x1f\\x80-\\x9f]/g;\nvar reservedRe = /^\\.+$/;\nvar windowsReservedRe = /^(con|prn|aux|nul|com[0-9]|lpt[0-9])(\\..*)?$/i;\nvar windowsTrailingRe = /[\\. ]+$/;\n\nfunction sanitize(input, replacement) {\n  if (typeof input !== 'string') {\n    throw new Error('Input must be string');\n  }\n  var sanitized = input\n    .replace(illegalRe, replacement)\n    .replace(controlRe, replacement)\n    .replace(reservedRe, replacement)\n    .replace(windowsReservedRe, replacement)\n    .replace(windowsTrailingRe, replacement);\n  return truncate(sanitized, 255);\n}\n\nmodule.exports = function (input, options) {\n  var replacement = (options && options.replacement) || '';\n  var output = sanitize(input, replacement);\n  if (replacement === '') {\n    return output;\n  }\n  return sanitize(output, '');\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/sanitize-filename/index.js\n");

/***/ })

};
;