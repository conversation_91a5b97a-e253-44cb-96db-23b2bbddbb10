{"name": "corporate-website", "version": "1.0.0", "type": "module", "main": "index.js", "scripts": {"build": "cross-env NODE_OPTIONS=--no-deprecation next build", "postbuild": "next-sitemap --config next-sitemap.config.cjs", "dev": "cross-env NODE_OPTIONS=--no-deprecation next dev", "dev:payload": "node --import tsx/esm src/server.ts", "start": "cross-env NODE_OPTIONS=--no-deprecation next start", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "lint": "cross-env NODE_OPTIONS=--no-deprecation next lint", "lint:fix": "cross-env NODE_OPTIONS=--no-deprecation next lint --fix"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@payloadcms/next": "^3.53.0", "@payloadcms/richtext-lexical": "^3.53.0", "@payloadcms/ui": "^3.53.0", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "bcrypt": "^6.0.0", "chalk": "^5.6.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "commander": "^14.0.0", "cross-env": "^7.0.3", "express": "^5.1.0", "fs-extra": "^11.3.1", "geist": "^1.3.0", "graphql": "^16.8.2", "inquirer": "^12.9.4", "lucide-react": "^0.263.1", "next": "^15.5.2", "next-sitemap": "^4.2.3", "payload": "^3.53.0", "payload-db-json": "file:./payload-db-json", "react": "^19.0.0-rc-65a56d0e-20241020", "react-dom": "^19.0.0-rc-65a56d0e-20241020", "react-hook-form": "7.45.4", "sharp": "^0.34.3", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0"}, "devDependencies": {"@swc/core": "^1.13.5", "@tailwindcss/typography": "^0.5.13", "@types/express": "^5.0.3", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.9", "@types/jest": "^30.0.0", "@types/node": "^24.3.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.19", "eslint": "^9.16.0", "eslint-config-next": "15.4.4", "jest": "^30.1.1", "nodemon": "^3.1.10", "postcss": "^8.4.38", "prettier": "^3.4.2", "tailwindcss": "^3.4.3", "ts-jest": "^29.4.1", "ts-node": "^10.9.2", "tsx": "^4.20.5", "typescript": "^5.9.2"}}