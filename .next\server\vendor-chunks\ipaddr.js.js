/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ipaddr.js";
exports.ids = ["vendor-chunks/ipaddr.js"];
exports.modules = {

/***/ "(rsc)/./node_modules/ipaddr.js/lib/ipaddr.js":
/*!**********************************************!*\
  !*** ./node_modules/ipaddr.js/lib/ipaddr.js ***!
  \**********************************************/
/***/ (function(module) {

eval("(function (root) {\n    'use strict';\n    // A list of regular expressions that match arbitrary IPv4 addresses,\n    // for which a number of weird notations exist.\n    // Note that an address like 0010.0xa5.1.1 is considered legal.\n    const ipv4Part = '(0?\\\\d+|0x[a-f0-9]+)';\n    const ipv4Regexes = {\n        fourOctet: new RegExp(`^${ipv4Part}\\\\.${ipv4Part}\\\\.${ipv4Part}\\\\.${ipv4Part}$`, 'i'),\n        threeOctet: new RegExp(`^${ipv4Part}\\\\.${ipv4Part}\\\\.${ipv4Part}$`, 'i'),\n        twoOctet: new RegExp(`^${ipv4Part}\\\\.${ipv4Part}$`, 'i'),\n        longValue: new RegExp(`^${ipv4Part}$`, 'i')\n    };\n\n    // Regular Expression for checking Octal numbers\n    const octalRegex = new RegExp(`^0[0-7]+$`, 'i');\n    const hexRegex = new RegExp(`^0x[a-f0-9]+$`, 'i');\n\n    const zoneIndex = '%[0-9a-z]{1,}';\n\n    // IPv6-matching regular expressions.\n    // For IPv6, the task is simpler: it is enough to match the colon-delimited\n    // hexadecimal IPv6 and a transitional variant with dotted-decimal IPv4 at\n    // the end.\n    const ipv6Part = '(?:[0-9a-f]+::?)+';\n    const ipv6Regexes = {\n        zoneIndex: new RegExp(zoneIndex, 'i'),\n        'native': new RegExp(`^(::)?(${ipv6Part})?([0-9a-f]+)?(::)?(${zoneIndex})?$`, 'i'),\n        deprecatedTransitional: new RegExp(`^(?:::)(${ipv4Part}\\\\.${ipv4Part}\\\\.${ipv4Part}\\\\.${ipv4Part}(${zoneIndex})?)$`, 'i'),\n        transitional: new RegExp(`^((?:${ipv6Part})|(?:::)(?:${ipv6Part})?)${ipv4Part}\\\\.${ipv4Part}\\\\.${ipv4Part}\\\\.${ipv4Part}(${zoneIndex})?$`, 'i')\n    };\n\n    // Expand :: in an IPv6 address or address part consisting of `parts` groups.\n    function expandIPv6 (string, parts) {\n        // More than one '::' means invalid adddress\n        if (string.indexOf('::') !== string.lastIndexOf('::')) {\n            return null;\n        }\n\n        let colonCount = 0;\n        let lastColon = -1;\n        let zoneId = (string.match(ipv6Regexes.zoneIndex) || [])[0];\n        let replacement, replacementCount;\n\n        // Remove zone index and save it for later\n        if (zoneId) {\n            zoneId = zoneId.substring(1);\n            string = string.replace(/%.+$/, '');\n        }\n\n        // How many parts do we already have?\n        while ((lastColon = string.indexOf(':', lastColon + 1)) >= 0) {\n            colonCount++;\n        }\n\n        // 0::0 is two parts more than ::\n        if (string.substr(0, 2) === '::') {\n            colonCount--;\n        }\n\n        if (string.substr(-2, 2) === '::') {\n            colonCount--;\n        }\n\n        // The following loop would hang if colonCount > parts\n        if (colonCount > parts) {\n            return null;\n        }\n\n        // replacement = ':' + '0:' * (parts - colonCount)\n        replacementCount = parts - colonCount;\n        replacement = ':';\n        while (replacementCount--) {\n            replacement += '0:';\n        }\n\n        // Insert the missing zeroes\n        string = string.replace('::', replacement);\n\n        // Trim any garbage which may be hanging around if :: was at the edge in\n        // the source strin\n        if (string[0] === ':') {\n            string = string.slice(1);\n        }\n\n        if (string[string.length - 1] === ':') {\n            string = string.slice(0, -1);\n        }\n\n        parts = (function () {\n            const ref = string.split(':');\n            const results = [];\n\n            for (let i = 0; i < ref.length; i++) {\n                results.push(parseInt(ref[i], 16));\n            }\n\n            return results;\n        })();\n\n        return {\n            parts: parts,\n            zoneId: zoneId\n        };\n    }\n\n    // A generic CIDR (Classless Inter-Domain Routing) RFC1518 range matcher.\n    function matchCIDR (first, second, partSize, cidrBits) {\n        if (first.length !== second.length) {\n            throw new Error('ipaddr: cannot match CIDR for objects with different lengths');\n        }\n\n        let part = 0;\n        let shift;\n\n        while (cidrBits > 0) {\n            shift = partSize - cidrBits;\n            if (shift < 0) {\n                shift = 0;\n            }\n\n            if (first[part] >> shift !== second[part] >> shift) {\n                return false;\n            }\n\n            cidrBits -= partSize;\n            part += 1;\n        }\n\n        return true;\n    }\n\n    function parseIntAuto (string) {\n        // Hexadedimal base 16 (0x#)\n        if (hexRegex.test(string)) {\n            return parseInt(string, 16);\n        }\n        // While octal representation is discouraged by ECMAScript 3\n        // and forbidden by ECMAScript 5, we silently allow it to\n        // work only if the rest of the string has numbers less than 8.\n        if (string[0] === '0' && !isNaN(parseInt(string[1], 10))) {\n        if (octalRegex.test(string)) {\n            return parseInt(string, 8);\n        }\n            throw new Error(`ipaddr: cannot parse ${string} as octal`);\n        }\n        // Always include the base 10 radix!\n        return parseInt(string, 10);\n    }\n\n    function padPart (part, length) {\n        while (part.length < length) {\n            part = `0${part}`;\n        }\n\n        return part;\n    }\n\n    const ipaddr = {};\n\n    // An IPv4 address (RFC791).\n    ipaddr.IPv4 = (function () {\n        // Constructs a new IPv4 address from an array of four octets\n        // in network order (MSB first)\n        // Verifies the input.\n        function IPv4 (octets) {\n            if (octets.length !== 4) {\n                throw new Error('ipaddr: ipv4 octet count should be 4');\n            }\n\n            let i, octet;\n\n            for (i = 0; i < octets.length; i++) {\n                octet = octets[i];\n                if (!((0 <= octet && octet <= 255))) {\n                    throw new Error('ipaddr: ipv4 octet should fit in 8 bits');\n                }\n            }\n\n            this.octets = octets;\n        }\n\n        // Special IPv4 address ranges.\n        // See also https://en.wikipedia.org/wiki/Reserved_IP_addresses\n        IPv4.prototype.SpecialRanges = {\n            unspecified: [[new IPv4([0, 0, 0, 0]), 8]],\n            broadcast: [[new IPv4([255, 255, 255, 255]), 32]],\n            // RFC3171\n            multicast: [[new IPv4([224, 0, 0, 0]), 4]],\n            // RFC3927\n            linkLocal: [[new IPv4([169, 254, 0, 0]), 16]],\n            // RFC5735\n            loopback: [[new IPv4([127, 0, 0, 0]), 8]],\n            // RFC6598\n            carrierGradeNat: [[new IPv4([100, 64, 0, 0]), 10]],\n            // RFC1918\n            'private': [\n                [new IPv4([10, 0, 0, 0]), 8],\n                [new IPv4([172, 16, 0, 0]), 12],\n                [new IPv4([192, 168, 0, 0]), 16]\n            ],\n            // Reserved and testing-only ranges; RFCs 5735, 5737, 2544, 1700\n            reserved: [\n                [new IPv4([192, 0, 0, 0]), 24],\n                [new IPv4([192, 0, 2, 0]), 24],\n                [new IPv4([192, 88, 99, 0]), 24],\n                [new IPv4([198, 18, 0, 0]), 15],\n                [new IPv4([198, 51, 100, 0]), 24],\n                [new IPv4([203, 0, 113, 0]), 24],\n                [new IPv4([240, 0, 0, 0]), 4]\n            ],\n            // RFC7534, RFC7535\n            as112: [\n                [new IPv4([192, 175, 48, 0]), 24],\n                [new IPv4([192, 31, 196, 0]), 24],\n            ],\n            // RFC7450\n            amt: [\n                [new IPv4([192, 52, 193, 0]), 24],\n            ],\n        };\n\n        // The 'kind' method exists on both IPv4 and IPv6 classes.\n        IPv4.prototype.kind = function () {\n            return 'ipv4';\n        };\n\n        // Checks if this address matches other one within given CIDR range.\n        IPv4.prototype.match = function (other, cidrRange) {\n            let ref;\n            if (cidrRange === undefined) {\n                ref = other;\n                other = ref[0];\n                cidrRange = ref[1];\n            }\n\n            if (other.kind() !== 'ipv4') {\n                throw new Error('ipaddr: cannot match ipv4 address with non-ipv4 one');\n            }\n\n            return matchCIDR(this.octets, other.octets, 8, cidrRange);\n        };\n\n        // returns a number of leading ones in IPv4 address, making sure that\n        // the rest is a solid sequence of 0's (valid netmask)\n        // returns either the CIDR length or null if mask is not valid\n        IPv4.prototype.prefixLengthFromSubnetMask = function () {\n            let cidr = 0;\n            // non-zero encountered stop scanning for zeroes\n            let stop = false;\n            // number of zeroes in octet\n            const zerotable = {\n                0: 8,\n                128: 7,\n                192: 6,\n                224: 5,\n                240: 4,\n                248: 3,\n                252: 2,\n                254: 1,\n                255: 0\n            };\n            let i, octet, zeros;\n\n            for (i = 3; i >= 0; i -= 1) {\n                octet = this.octets[i];\n                if (octet in zerotable) {\n                    zeros = zerotable[octet];\n                    if (stop && zeros !== 0) {\n                        return null;\n                    }\n\n                    if (zeros !== 8) {\n                        stop = true;\n                    }\n\n                    cidr += zeros;\n                } else {\n                    return null;\n                }\n            }\n\n            return 32 - cidr;\n        };\n\n        // Checks if the address corresponds to one of the special ranges.\n        IPv4.prototype.range = function () {\n            return ipaddr.subnetMatch(this, this.SpecialRanges);\n        };\n\n        // Returns an array of byte-sized values in network order (MSB first)\n        IPv4.prototype.toByteArray = function () {\n            return this.octets.slice(0);\n        };\n\n        // Converts this IPv4 address to an IPv4-mapped IPv6 address.\n        IPv4.prototype.toIPv4MappedAddress = function () {\n            return ipaddr.IPv6.parse(`::ffff:${this.toString()}`);\n        };\n\n        // Symmetrical method strictly for aligning with the IPv6 methods.\n        IPv4.prototype.toNormalizedString = function () {\n            return this.toString();\n        };\n\n        // Returns the address in convenient, decimal-dotted format.\n        IPv4.prototype.toString = function () {\n            return this.octets.join('.');\n        };\n\n        return IPv4;\n    })();\n\n    // A utility function to return broadcast address given the IPv4 interface and prefix length in CIDR notation\n    ipaddr.IPv4.broadcastAddressFromCIDR = function (string) {\n\n        try {\n            const cidr = this.parseCIDR(string);\n            const ipInterfaceOctets = cidr[0].toByteArray();\n            const subnetMaskOctets = this.subnetMaskFromPrefixLength(cidr[1]).toByteArray();\n            const octets = [];\n            let i = 0;\n            while (i < 4) {\n                // Broadcast address is bitwise OR between ip interface and inverted mask\n                octets.push(parseInt(ipInterfaceOctets[i], 10) | parseInt(subnetMaskOctets[i], 10) ^ 255);\n                i++;\n            }\n\n            return new this(octets);\n        } catch (e) {\n            throw new Error('ipaddr: the address does not have IPv4 CIDR format');\n        }\n    };\n\n    // Checks if a given string is formatted like IPv4 address.\n    ipaddr.IPv4.isIPv4 = function (string) {\n        return this.parser(string) !== null;\n    };\n\n    // Checks if a given string is a valid IPv4 address.\n    ipaddr.IPv4.isValid = function (string) {\n        try {\n            new this(this.parser(string));\n            return true;\n        } catch (e) {\n            return false;\n        }\n    };\n\n    // Checks if a given string is a valid IPv4 address in CIDR notation.\n    ipaddr.IPv4.isValidCIDR = function (string) {\n        try {\n            this.parseCIDR(string);\n            return true;\n        } catch (e) {\n            return false;\n        }\n    };\n\n    // Checks if a given string is a full four-part IPv4 Address.\n    ipaddr.IPv4.isValidFourPartDecimal = function (string) {\n        if (ipaddr.IPv4.isValid(string) && string.match(/^(0|[1-9]\\d*)(\\.(0|[1-9]\\d*)){3}$/)) {\n            return true;\n        } else {\n            return false;\n        }\n    };\n\n    // A utility function to return network address given the IPv4 interface and prefix length in CIDR notation\n    ipaddr.IPv4.networkAddressFromCIDR = function (string) {\n        let cidr, i, ipInterfaceOctets, octets, subnetMaskOctets;\n\n        try {\n            cidr = this.parseCIDR(string);\n            ipInterfaceOctets = cidr[0].toByteArray();\n            subnetMaskOctets = this.subnetMaskFromPrefixLength(cidr[1]).toByteArray();\n            octets = [];\n            i = 0;\n            while (i < 4) {\n                // Network address is bitwise AND between ip interface and mask\n                octets.push(parseInt(ipInterfaceOctets[i], 10) & parseInt(subnetMaskOctets[i], 10));\n                i++;\n            }\n\n            return new this(octets);\n        } catch (e) {\n            throw new Error('ipaddr: the address does not have IPv4 CIDR format');\n        }\n    };\n\n    // Tries to parse and validate a string with IPv4 address.\n    // Throws an error if it fails.\n    ipaddr.IPv4.parse = function (string) {\n        const parts = this.parser(string);\n\n        if (parts === null) {\n            throw new Error('ipaddr: string is not formatted like an IPv4 Address');\n        }\n\n        return new this(parts);\n    };\n\n    // Parses the string as an IPv4 Address with CIDR Notation.\n    ipaddr.IPv4.parseCIDR = function (string) {\n        let match;\n\n        if ((match = string.match(/^(.+)\\/(\\d+)$/))) {\n            const maskLength = parseInt(match[2]);\n            if (maskLength >= 0 && maskLength <= 32) {\n                const parsed = [this.parse(match[1]), maskLength];\n                Object.defineProperty(parsed, 'toString', {\n                    value: function () {\n                        return this.join('/');\n                    }\n                });\n                return parsed;\n            }\n        }\n\n        throw new Error('ipaddr: string is not formatted like an IPv4 CIDR range');\n    };\n\n    // Classful variants (like a.b, where a is an octet, and b is a 24-bit\n    // value representing last three octets; this corresponds to a class C\n    // address) are omitted due to classless nature of modern Internet.\n    ipaddr.IPv4.parser = function (string) {\n        let match, part, value;\n\n        // parseInt recognizes all that octal & hexadecimal weirdness for us\n        if ((match = string.match(ipv4Regexes.fourOctet))) {\n            return (function () {\n                const ref = match.slice(1, 6);\n                const results = [];\n\n                for (let i = 0; i < ref.length; i++) {\n                    part = ref[i];\n                    results.push(parseIntAuto(part));\n                }\n\n                return results;\n            })();\n        } else if ((match = string.match(ipv4Regexes.longValue))) {\n            value = parseIntAuto(match[1]);\n            if (value > 0xffffffff || value < 0) {\n                throw new Error('ipaddr: address outside defined range');\n            }\n\n            return ((function () {\n                const results = [];\n                let shift;\n\n                for (shift = 0; shift <= 24; shift += 8) {\n                    results.push((value >> shift) & 0xff);\n                }\n\n                return results;\n            })()).reverse();\n        } else if ((match = string.match(ipv4Regexes.twoOctet))) {\n            return (function () {\n                const ref = match.slice(1, 4);\n                const results = [];\n\n                value = parseIntAuto(ref[1]);\n                if (value > 0xffffff || value < 0) {\n                    throw new Error('ipaddr: address outside defined range');\n                }\n\n                results.push(parseIntAuto(ref[0]));\n                results.push((value >> 16) & 0xff);\n                results.push((value >>  8) & 0xff);\n                results.push( value        & 0xff);\n\n                return results;\n            })();\n        } else if ((match = string.match(ipv4Regexes.threeOctet))) {\n            return (function () {\n                const ref = match.slice(1, 5);\n                const results = [];\n\n                value = parseIntAuto(ref[2]);\n                if (value > 0xffff || value < 0) {\n                    throw new Error('ipaddr: address outside defined range');\n                }\n\n                results.push(parseIntAuto(ref[0]));\n                results.push(parseIntAuto(ref[1]));\n                results.push((value >> 8) & 0xff);\n                results.push( value       & 0xff);\n\n                return results;\n            })();\n        } else {\n            return null;\n        }\n    };\n\n    // A utility function to return subnet mask in IPv4 format given the prefix length\n    ipaddr.IPv4.subnetMaskFromPrefixLength = function (prefix) {\n        prefix = parseInt(prefix);\n        if (prefix < 0 || prefix > 32) {\n            throw new Error('ipaddr: invalid IPv4 prefix length');\n        }\n\n        const octets = [0, 0, 0, 0];\n        let j = 0;\n        const filledOctetCount = Math.floor(prefix / 8);\n\n        while (j < filledOctetCount) {\n            octets[j] = 255;\n            j++;\n        }\n\n        if (filledOctetCount < 4) {\n            octets[filledOctetCount] = Math.pow(2, prefix % 8) - 1 << 8 - (prefix % 8);\n        }\n\n        return new this(octets);\n    };\n\n    // An IPv6 address (RFC2460)\n    ipaddr.IPv6 = (function () {\n        // Constructs an IPv6 address from an array of eight 16 - bit parts\n        // or sixteen 8 - bit parts in network order(MSB first).\n        // Throws an error if the input is invalid.\n        function IPv6 (parts, zoneId) {\n            let i, part;\n\n            if (parts.length === 16) {\n                this.parts = [];\n                for (i = 0; i <= 14; i += 2) {\n                    this.parts.push((parts[i] << 8) | parts[i + 1]);\n                }\n            } else if (parts.length === 8) {\n                this.parts = parts;\n            } else {\n                throw new Error('ipaddr: ipv6 part count should be 8 or 16');\n            }\n\n            for (i = 0; i < this.parts.length; i++) {\n                part = this.parts[i];\n                if (!((0 <= part && part <= 0xffff))) {\n                    throw new Error('ipaddr: ipv6 part should fit in 16 bits');\n                }\n            }\n\n            if (zoneId) {\n                this.zoneId = zoneId;\n            }\n        }\n\n        // Special IPv6 ranges\n        IPv6.prototype.SpecialRanges = {\n            // RFC4291, here and after\n            unspecified: [new IPv6([0, 0, 0, 0, 0, 0, 0, 0]), 128],\n            linkLocal: [new IPv6([0xfe80, 0, 0, 0, 0, 0, 0, 0]), 10],\n            multicast: [new IPv6([0xff00, 0, 0, 0, 0, 0, 0, 0]), 8],\n            loopback: [new IPv6([0, 0, 0, 0, 0, 0, 0, 1]), 128],\n            uniqueLocal: [new IPv6([0xfc00, 0, 0, 0, 0, 0, 0, 0]), 7],\n            ipv4Mapped: [new IPv6([0, 0, 0, 0, 0, 0xffff, 0, 0]), 96],\n            // RFC6666\n            discard: [new IPv6([0x100, 0, 0, 0, 0, 0, 0, 0]), 64],\n            // RFC6145\n            rfc6145: [new IPv6([0, 0, 0, 0, 0xffff, 0, 0, 0]), 96],\n            // RFC6052\n            rfc6052: [new IPv6([0x64, 0xff9b, 0, 0, 0, 0, 0, 0]), 96],\n            // RFC3056\n            '6to4': [new IPv6([0x2002, 0, 0, 0, 0, 0, 0, 0]), 16],\n            // RFC6052, RFC6146\n            teredo: [new IPv6([0x2001, 0, 0, 0, 0, 0, 0, 0]), 32],\n            // RFC5180\n            benchmarking: [new IPv6([0x2001, 0x2, 0, 0, 0, 0, 0, 0]), 48],\n            // RFC7450\n            amt: [new IPv6([0x2001, 0x3, 0, 0, 0, 0, 0, 0]), 32],\n            as112v6: [\n                [new IPv6([0x2001, 0x4, 0x112, 0, 0, 0, 0, 0]), 48],\n                [new IPv6([0x2620, 0x4f, 0x8000, 0, 0, 0, 0, 0]), 48],\n            ],\n            deprecated: [new IPv6([0x2001, 0x10, 0, 0, 0, 0, 0, 0]), 28],\n            orchid2: [new IPv6([0x2001, 0x20, 0, 0, 0, 0, 0, 0]), 28],\n            droneRemoteIdProtocolEntityTags: [new IPv6([0x2001, 0x30, 0, 0, 0, 0, 0, 0]), 28],\n            reserved: [\n                // RFC3849\n                [new IPv6([0x2001, 0, 0, 0, 0, 0, 0, 0]), 23],\n                // RFC2928\n                [new IPv6([0x2001, 0xdb8, 0, 0, 0, 0, 0, 0]), 32],\n            ],\n        };\n\n        // Checks if this address is an IPv4-mapped IPv6 address.\n        IPv6.prototype.isIPv4MappedAddress = function () {\n            return this.range() === 'ipv4Mapped';\n        };\n\n        // The 'kind' method exists on both IPv4 and IPv6 classes.\n        IPv6.prototype.kind = function () {\n            return 'ipv6';\n        };\n\n        // Checks if this address matches other one within given CIDR range.\n        IPv6.prototype.match = function (other, cidrRange) {\n            let ref;\n\n            if (cidrRange === undefined) {\n                ref = other;\n                other = ref[0];\n                cidrRange = ref[1];\n            }\n\n            if (other.kind() !== 'ipv6') {\n                throw new Error('ipaddr: cannot match ipv6 address with non-ipv6 one');\n            }\n\n            return matchCIDR(this.parts, other.parts, 16, cidrRange);\n        };\n\n        // returns a number of leading ones in IPv6 address, making sure that\n        // the rest is a solid sequence of 0's (valid netmask)\n        // returns either the CIDR length or null if mask is not valid\n        IPv6.prototype.prefixLengthFromSubnetMask = function () {\n            let cidr = 0;\n            // non-zero encountered stop scanning for zeroes\n            let stop = false;\n            // number of zeroes in octet\n            const zerotable = {\n                0: 16,\n                32768: 15,\n                49152: 14,\n                57344: 13,\n                61440: 12,\n                63488: 11,\n                64512: 10,\n                65024: 9,\n                65280: 8,\n                65408: 7,\n                65472: 6,\n                65504: 5,\n                65520: 4,\n                65528: 3,\n                65532: 2,\n                65534: 1,\n                65535: 0\n            };\n            let part, zeros;\n\n            for (let i = 7; i >= 0; i -= 1) {\n                part = this.parts[i];\n                if (part in zerotable) {\n                    zeros = zerotable[part];\n                    if (stop && zeros !== 0) {\n                        return null;\n                    }\n\n                    if (zeros !== 16) {\n                        stop = true;\n                    }\n\n                    cidr += zeros;\n                } else {\n                    return null;\n                }\n            }\n\n            return 128 - cidr;\n        };\n\n\n        // Checks if the address corresponds to one of the special ranges.\n        IPv6.prototype.range = function () {\n            return ipaddr.subnetMatch(this, this.SpecialRanges);\n        };\n\n        // Returns an array of byte-sized values in network order (MSB first)\n        IPv6.prototype.toByteArray = function () {\n            let part;\n            const bytes = [];\n            const ref = this.parts;\n            for (let i = 0; i < ref.length; i++) {\n                part = ref[i];\n                bytes.push(part >> 8);\n                bytes.push(part & 0xff);\n            }\n\n            return bytes;\n        };\n\n        // Returns the address in expanded format with all zeroes included, like\n        // 2001:0db8:0008:0066:0000:0000:0000:0001\n        IPv6.prototype.toFixedLengthString = function () {\n            const addr = ((function () {\n                const results = [];\n                for (let i = 0; i < this.parts.length; i++) {\n                    results.push(padPart(this.parts[i].toString(16), 4));\n                }\n\n                return results;\n            }).call(this)).join(':');\n\n            let suffix = '';\n\n            if (this.zoneId) {\n                suffix = `%${this.zoneId}`;\n            }\n\n            return addr + suffix;\n        };\n\n        // Converts this address to IPv4 address if it is an IPv4-mapped IPv6 address.\n        // Throws an error otherwise.\n        IPv6.prototype.toIPv4Address = function () {\n            if (!this.isIPv4MappedAddress()) {\n                throw new Error('ipaddr: trying to convert a generic ipv6 address to ipv4');\n            }\n\n            const ref = this.parts.slice(-2);\n            const high = ref[0];\n            const low = ref[1];\n\n            return new ipaddr.IPv4([high >> 8, high & 0xff, low >> 8, low & 0xff]);\n        };\n\n        // Returns the address in expanded format with all zeroes included, like\n        // 2001:db8:8:66:0:0:0:1\n        //\n        // Deprecated: use toFixedLengthString() instead.\n        IPv6.prototype.toNormalizedString = function () {\n            const addr = ((function () {\n                const results = [];\n\n                for (let i = 0; i < this.parts.length; i++) {\n                    results.push(this.parts[i].toString(16));\n                }\n\n                return results;\n            }).call(this)).join(':');\n\n            let suffix = '';\n\n            if (this.zoneId) {\n                suffix = `%${this.zoneId}`;\n            }\n\n            return addr + suffix;\n        };\n\n        // Returns the address in compact, human-readable format like\n        // 2001:db8:8:66::1\n        // in line with RFC 5952 (see https://tools.ietf.org/html/rfc5952#section-4)\n        IPv6.prototype.toRFC5952String = function () {\n            const regex = /((^|:)(0(:|$)){2,})/g;\n            const string = this.toNormalizedString();\n            let bestMatchIndex = 0;\n            let bestMatchLength = -1;\n            let match;\n\n            while ((match = regex.exec(string))) {\n                if (match[0].length > bestMatchLength) {\n                    bestMatchIndex = match.index;\n                    bestMatchLength = match[0].length;\n                }\n            }\n\n            if (bestMatchLength < 0) {\n                return string;\n            }\n\n            return `${string.substring(0, bestMatchIndex)}::${string.substring(bestMatchIndex + bestMatchLength)}`;\n        };\n\n        // Returns the address in compact, human-readable format like\n        // 2001:db8:8:66::1\n        // Calls toRFC5952String under the hood.\n        IPv6.prototype.toString = function () {\n            return this.toRFC5952String();\n        };\n\n        return IPv6;\n\n    })();\n\n    // A utility function to return broadcast address given the IPv6 interface and prefix length in CIDR notation\n    ipaddr.IPv6.broadcastAddressFromCIDR = function (string) {\n        try {\n            const cidr = this.parseCIDR(string);\n            const ipInterfaceOctets = cidr[0].toByteArray();\n            const subnetMaskOctets = this.subnetMaskFromPrefixLength(cidr[1]).toByteArray();\n            const octets = [];\n            let i = 0;\n            while (i < 16) {\n                // Broadcast address is bitwise OR between ip interface and inverted mask\n                octets.push(parseInt(ipInterfaceOctets[i], 10) | parseInt(subnetMaskOctets[i], 10) ^ 255);\n                i++;\n            }\n\n            return new this(octets);\n        } catch (e) {\n            throw new Error(`ipaddr: the address does not have IPv6 CIDR format (${e})`);\n        }\n    };\n\n    // Checks if a given string is formatted like IPv6 address.\n    ipaddr.IPv6.isIPv6 = function (string) {\n        return this.parser(string) !== null;\n    };\n\n    // Checks to see if string is a valid IPv6 Address\n    ipaddr.IPv6.isValid = function (string) {\n\n        // Since IPv6.isValid is always called first, this shortcut\n        // provides a substantial performance gain.\n        if (typeof string === 'string' && string.indexOf(':') === -1) {\n            return false;\n        }\n\n        try {\n            const addr = this.parser(string);\n            new this(addr.parts, addr.zoneId);\n            return true;\n        } catch (e) {\n            return false;\n        }\n    };\n\n    // Checks if a given string is a valid IPv6 address in CIDR notation.\n    ipaddr.IPv6.isValidCIDR = function (string) {\n\n        // See note in IPv6.isValid\n        if (typeof string === 'string' && string.indexOf(':') === -1) {\n            return false;\n        }\n\n        try {\n            this.parseCIDR(string);\n            return true;\n        } catch (e) {\n            return false;\n        }\n    };\n\n    // A utility function to return network address given the IPv6 interface and prefix length in CIDR notation\n    ipaddr.IPv6.networkAddressFromCIDR = function (string) {\n        let cidr, i, ipInterfaceOctets, octets, subnetMaskOctets;\n\n        try {\n            cidr = this.parseCIDR(string);\n            ipInterfaceOctets = cidr[0].toByteArray();\n            subnetMaskOctets = this.subnetMaskFromPrefixLength(cidr[1]).toByteArray();\n            octets = [];\n            i = 0;\n            while (i < 16) {\n                // Network address is bitwise AND between ip interface and mask\n                octets.push(parseInt(ipInterfaceOctets[i], 10) & parseInt(subnetMaskOctets[i], 10));\n                i++;\n            }\n\n            return new this(octets);\n        } catch (e) {\n            throw new Error(`ipaddr: the address does not have IPv6 CIDR format (${e})`);\n        }\n    };\n\n    // Tries to parse and validate a string with IPv6 address.\n    // Throws an error if it fails.\n    ipaddr.IPv6.parse = function (string) {\n        const addr = this.parser(string);\n\n        if (addr.parts === null) {\n            throw new Error('ipaddr: string is not formatted like an IPv6 Address');\n        }\n\n        return new this(addr.parts, addr.zoneId);\n    };\n\n    ipaddr.IPv6.parseCIDR = function (string) {\n        let maskLength, match, parsed;\n\n        if ((match = string.match(/^(.+)\\/(\\d+)$/))) {\n            maskLength = parseInt(match[2]);\n            if (maskLength >= 0 && maskLength <= 128) {\n                parsed = [this.parse(match[1]), maskLength];\n                Object.defineProperty(parsed, 'toString', {\n                    value: function () {\n                        return this.join('/');\n                    }\n                });\n                return parsed;\n            }\n        }\n\n        throw new Error('ipaddr: string is not formatted like an IPv6 CIDR range');\n    };\n\n    // Parse an IPv6 address.\n    ipaddr.IPv6.parser = function (string) {\n        let addr, i, match, octet, octets, zoneId;\n\n        if ((match = string.match(ipv6Regexes.deprecatedTransitional))) {\n            return this.parser(`::ffff:${match[1]}`);\n        }\n        if (ipv6Regexes.native.test(string)) {\n            return expandIPv6(string, 8);\n        }\n        if ((match = string.match(ipv6Regexes.transitional))) {\n            zoneId = match[6] || '';\n            addr = match[1]\n            if (!match[1].endsWith('::')) {\n                addr = addr.slice(0, -1)\n            }\n            addr = expandIPv6(addr + zoneId, 6);\n            if (addr.parts) {\n                octets = [\n                    parseInt(match[2]),\n                    parseInt(match[3]),\n                    parseInt(match[4]),\n                    parseInt(match[5])\n                ];\n                for (i = 0; i < octets.length; i++) {\n                    octet = octets[i];\n                    if (!((0 <= octet && octet <= 255))) {\n                        return null;\n                    }\n                }\n\n                addr.parts.push(octets[0] << 8 | octets[1]);\n                addr.parts.push(octets[2] << 8 | octets[3]);\n                return {\n                    parts: addr.parts,\n                    zoneId: addr.zoneId\n                };\n            }\n        }\n\n        return null;\n    };\n\n    // A utility function to return subnet mask in IPv6 format given the prefix length\n    ipaddr.IPv6.subnetMaskFromPrefixLength = function (prefix) {\n        prefix = parseInt(prefix);\n        if (prefix < 0 || prefix > 128) {\n            throw new Error('ipaddr: invalid IPv6 prefix length');\n        }\n\n        const octets = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];\n        let j = 0;\n        const filledOctetCount = Math.floor(prefix / 8);\n\n        while (j < filledOctetCount) {\n            octets[j] = 255;\n            j++;\n        }\n\n        if (filledOctetCount < 16) {\n            octets[filledOctetCount] = Math.pow(2, prefix % 8) - 1 << 8 - (prefix % 8);\n        }\n\n        return new this(octets);\n    };\n\n    // Try to parse an array in network order (MSB first) for IPv4 and IPv6\n    ipaddr.fromByteArray = function (bytes) {\n        const length = bytes.length;\n\n        if (length === 4) {\n            return new ipaddr.IPv4(bytes);\n        } else if (length === 16) {\n            return new ipaddr.IPv6(bytes);\n        } else {\n            throw new Error('ipaddr: the binary input is neither an IPv6 nor IPv4 address');\n        }\n    };\n\n    // Checks if the address is valid IP address\n    ipaddr.isValid = function (string) {\n        return ipaddr.IPv6.isValid(string) || ipaddr.IPv4.isValid(string);\n    };\n\n    // Checks if the address is valid IP address in CIDR notation\n    ipaddr.isValidCIDR = function (string) {\n        return ipaddr.IPv6.isValidCIDR(string) || ipaddr.IPv4.isValidCIDR(string);\n    };\n\n\n    // Attempts to parse an IP Address, first through IPv6 then IPv4.\n    // Throws an error if it could not be parsed.\n    ipaddr.parse = function (string) {\n        if (ipaddr.IPv6.isValid(string)) {\n            return ipaddr.IPv6.parse(string);\n        } else if (ipaddr.IPv4.isValid(string)) {\n            return ipaddr.IPv4.parse(string);\n        } else {\n            throw new Error('ipaddr: the address has neither IPv6 nor IPv4 format');\n        }\n    };\n\n    // Attempt to parse CIDR notation, first through IPv6 then IPv4.\n    // Throws an error if it could not be parsed.\n    ipaddr.parseCIDR = function (string) {\n        try {\n            return ipaddr.IPv6.parseCIDR(string);\n        } catch (e) {\n            try {\n                return ipaddr.IPv4.parseCIDR(string);\n            } catch (e2) {\n                throw new Error('ipaddr: the address has neither IPv6 nor IPv4 CIDR format');\n            }\n        }\n    };\n\n    // Parse an address and return plain IPv4 address if it is an IPv4-mapped address\n    ipaddr.process = function (string) {\n        const addr = this.parse(string);\n\n        if (addr.kind() === 'ipv6' && addr.isIPv4MappedAddress()) {\n            return addr.toIPv4Address();\n        } else {\n            return addr;\n        }\n    };\n\n    // An utility function to ease named range matching. See examples below.\n    // rangeList can contain both IPv4 and IPv6 subnet entries and will not throw errors\n    // on matching IPv4 addresses to IPv6 ranges or vice versa.\n    ipaddr.subnetMatch = function (address, rangeList, defaultName) {\n        let i, rangeName, rangeSubnets, subnet;\n\n        if (defaultName === undefined || defaultName === null) {\n            defaultName = 'unicast';\n        }\n\n        for (rangeName in rangeList) {\n            if (Object.prototype.hasOwnProperty.call(rangeList, rangeName)) {\n                rangeSubnets = rangeList[rangeName];\n                // ECMA5 Array.isArray isn't available everywhere\n                if (rangeSubnets[0] && !(rangeSubnets[0] instanceof Array)) {\n                    rangeSubnets = [rangeSubnets];\n                }\n\n                for (i = 0; i < rangeSubnets.length; i++) {\n                    subnet = rangeSubnets[i];\n                    if (address.kind() === subnet[0].kind() && address.match.apply(address, subnet)) {\n                        return rangeName;\n                    }\n                }\n            }\n        }\n\n        return defaultName;\n    };\n\n    // Export for both the CommonJS and browser-like environment\n    if ( true && module.exports) {\n        module.exports = ipaddr;\n\n    } else {\n        root.ipaddr = ipaddr;\n    }\n\n}(this));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/ipaddr.js/lib/ipaddr.js\n");

/***/ })

};
;