"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/uint8array-extras";
exports.ids = ["vendor-chunks/uint8array-extras"];
exports.modules = {

/***/ "(rsc)/./node_modules/uint8array-extras/index.js":
/*!*************************************************!*\
  !*** ./node_modules/uint8array-extras/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   areUint8ArraysEqual: () => (/* binding */ areUint8ArraysEqual),\n/* harmony export */   assertUint8Array: () => (/* binding */ assertUint8Array),\n/* harmony export */   assertUint8ArrayOrArrayBuffer: () => (/* binding */ assertUint8ArrayOrArrayBuffer),\n/* harmony export */   base64ToString: () => (/* binding */ base64ToString),\n/* harmony export */   base64ToUint8Array: () => (/* binding */ base64ToUint8Array),\n/* harmony export */   compareUint8Arrays: () => (/* binding */ compareUint8Arrays),\n/* harmony export */   concatUint8Arrays: () => (/* binding */ concatUint8Arrays),\n/* harmony export */   getUintBE: () => (/* binding */ getUintBE),\n/* harmony export */   hexToUint8Array: () => (/* binding */ hexToUint8Array),\n/* harmony export */   includes: () => (/* binding */ includes),\n/* harmony export */   indexOf: () => (/* binding */ indexOf),\n/* harmony export */   isUint8Array: () => (/* binding */ isUint8Array),\n/* harmony export */   stringToBase64: () => (/* binding */ stringToBase64),\n/* harmony export */   stringToUint8Array: () => (/* binding */ stringToUint8Array),\n/* harmony export */   toUint8Array: () => (/* binding */ toUint8Array),\n/* harmony export */   uint8ArrayToBase64: () => (/* binding */ uint8ArrayToBase64),\n/* harmony export */   uint8ArrayToHex: () => (/* binding */ uint8ArrayToHex),\n/* harmony export */   uint8ArrayToString: () => (/* binding */ uint8ArrayToString)\n/* harmony export */ });\nconst objectToString = Object.prototype.toString;\nconst uint8ArrayStringified = '[object Uint8Array]';\nconst arrayBufferStringified = '[object ArrayBuffer]';\n\nfunction isType(value, typeConstructor, typeStringified) {\n\tif (!value) {\n\t\treturn false;\n\t}\n\n\tif (value.constructor === typeConstructor) {\n\t\treturn true;\n\t}\n\n\treturn objectToString.call(value) === typeStringified;\n}\n\nfunction isUint8Array(value) {\n\treturn isType(value, Uint8Array, uint8ArrayStringified);\n}\n\nfunction isArrayBuffer(value) {\n\treturn isType(value, ArrayBuffer, arrayBufferStringified);\n}\n\nfunction isUint8ArrayOrArrayBuffer(value) {\n\treturn isUint8Array(value) || isArrayBuffer(value);\n}\n\nfunction assertUint8Array(value) {\n\tif (!isUint8Array(value)) {\n\t\tthrow new TypeError(`Expected \\`Uint8Array\\`, got \\`${typeof value}\\``);\n\t}\n}\n\nfunction assertUint8ArrayOrArrayBuffer(value) {\n\tif (!isUint8ArrayOrArrayBuffer(value)) {\n\t\tthrow new TypeError(`Expected \\`Uint8Array\\` or \\`ArrayBuffer\\`, got \\`${typeof value}\\``);\n\t}\n}\n\nfunction toUint8Array(value) {\n\tif (value instanceof ArrayBuffer) {\n\t\treturn new Uint8Array(value);\n\t}\n\n\tif (ArrayBuffer.isView(value)) {\n\t\treturn new Uint8Array(value.buffer, value.byteOffset, value.byteLength);\n\t}\n\n\tthrow new TypeError(`Unsupported value, got \\`${typeof value}\\`.`);\n}\n\nfunction concatUint8Arrays(arrays, totalLength) {\n\tif (arrays.length === 0) {\n\t\treturn new Uint8Array(0);\n\t}\n\n\ttotalLength ??= arrays.reduce((accumulator, currentValue) => accumulator + currentValue.length, 0);\n\n\tconst returnValue = new Uint8Array(totalLength);\n\n\tlet offset = 0;\n\tfor (const array of arrays) {\n\t\tassertUint8Array(array);\n\t\treturnValue.set(array, offset);\n\t\toffset += array.length;\n\t}\n\n\treturn returnValue;\n}\n\nfunction areUint8ArraysEqual(a, b) {\n\tassertUint8Array(a);\n\tassertUint8Array(b);\n\n\tif (a === b) {\n\t\treturn true;\n\t}\n\n\tif (a.length !== b.length) {\n\t\treturn false;\n\t}\n\n\t// eslint-disable-next-line unicorn/no-for-loop\n\tfor (let index = 0; index < a.length; index++) {\n\t\tif (a[index] !== b[index]) {\n\t\t\treturn false;\n\t\t}\n\t}\n\n\treturn true;\n}\n\nfunction compareUint8Arrays(a, b) {\n\tassertUint8Array(a);\n\tassertUint8Array(b);\n\n\tconst length = Math.min(a.length, b.length);\n\n\tfor (let index = 0; index < length; index++) {\n\t\tconst diff = a[index] - b[index];\n\t\tif (diff !== 0) {\n\t\t\treturn Math.sign(diff);\n\t\t}\n\t}\n\n\t// At this point, all the compared elements are equal.\n\t// The shorter array should come first if the arrays are of different lengths.\n\treturn Math.sign(a.length - b.length);\n}\n\nconst cachedDecoders = {\n\tutf8: new globalThis.TextDecoder('utf8'),\n};\n\nfunction uint8ArrayToString(array, encoding = 'utf8') {\n\tassertUint8ArrayOrArrayBuffer(array);\n\tcachedDecoders[encoding] ??= new globalThis.TextDecoder(encoding);\n\treturn cachedDecoders[encoding].decode(array);\n}\n\nfunction assertString(value) {\n\tif (typeof value !== 'string') {\n\t\tthrow new TypeError(`Expected \\`string\\`, got \\`${typeof value}\\``);\n\t}\n}\n\nconst cachedEncoder = new globalThis.TextEncoder();\n\nfunction stringToUint8Array(string) {\n\tassertString(string);\n\treturn cachedEncoder.encode(string);\n}\n\nfunction base64ToBase64Url(base64) {\n\treturn base64.replaceAll('+', '-').replaceAll('/', '_').replace(/=+$/, '');\n}\n\nfunction base64UrlToBase64(base64url) {\n\tconst base64 = base64url.replaceAll('-', '+').replaceAll('_', '/');\n\tconst padding = (4 - (base64.length % 4)) % 4;\n\treturn base64 + '='.repeat(padding);\n}\n\n// Reference: https://phuoc.ng/collection/this-vs-that/concat-vs-push/\n// Important: Keep this value divisible by 3 so intermediate chunks produce no Base64 padding.\nconst MAX_BLOCK_SIZE = 65_535;\n\nfunction uint8ArrayToBase64(array, {urlSafe = false} = {}) {\n\tassertUint8Array(array);\n\n\tlet base64 = '';\n\n\tfor (let index = 0; index < array.length; index += MAX_BLOCK_SIZE) {\n\t\tconst chunk = array.subarray(index, index + MAX_BLOCK_SIZE);\n\t\t// Required as `btoa` and `atob` don't properly support Unicode: https://developer.mozilla.org/en-US/docs/Glossary/Base64#the_unicode_problem\n\t\tbase64 += globalThis.btoa(String.fromCodePoint.apply(undefined, chunk));\n\t}\n\n\treturn urlSafe ? base64ToBase64Url(base64) : base64;\n}\n\nfunction base64ToUint8Array(base64String) {\n\tassertString(base64String);\n\treturn Uint8Array.from(globalThis.atob(base64UrlToBase64(base64String)), x => x.codePointAt(0));\n}\n\nfunction stringToBase64(string, {urlSafe = false} = {}) {\n\tassertString(string);\n\treturn uint8ArrayToBase64(stringToUint8Array(string), {urlSafe});\n}\n\nfunction base64ToString(base64String) {\n\tassertString(base64String);\n\treturn uint8ArrayToString(base64ToUint8Array(base64String));\n}\n\nconst byteToHexLookupTable = Array.from({length: 256}, (_, index) => index.toString(16).padStart(2, '0'));\n\nfunction uint8ArrayToHex(array) {\n\tassertUint8Array(array);\n\n\t// Concatenating a string is faster than using an array.\n\tlet hexString = '';\n\n\t// eslint-disable-next-line unicorn/no-for-loop -- Max performance is critical.\n\tfor (let index = 0; index < array.length; index++) {\n\t\thexString += byteToHexLookupTable[array[index]];\n\t}\n\n\treturn hexString;\n}\n\nconst hexToDecimalLookupTable = {\n\t0: 0,\n\t1: 1,\n\t2: 2,\n\t3: 3,\n\t4: 4,\n\t5: 5,\n\t6: 6,\n\t7: 7,\n\t8: 8,\n\t9: 9,\n\ta: 10,\n\tb: 11,\n\tc: 12,\n\td: 13,\n\te: 14,\n\tf: 15,\n\tA: 10,\n\tB: 11,\n\tC: 12,\n\tD: 13,\n\tE: 14,\n\tF: 15,\n};\n\nfunction hexToUint8Array(hexString) {\n\tassertString(hexString);\n\n\tif (hexString.length % 2 !== 0) {\n\t\tthrow new Error('Invalid Hex string length.');\n\t}\n\n\tconst resultLength = hexString.length / 2;\n\tconst bytes = new Uint8Array(resultLength);\n\n\tfor (let index = 0; index < resultLength; index++) {\n\t\tconst highNibble = hexToDecimalLookupTable[hexString[index * 2]];\n\t\tconst lowNibble = hexToDecimalLookupTable[hexString[(index * 2) + 1]];\n\n\t\tif (highNibble === undefined || lowNibble === undefined) {\n\t\t\tthrow new Error(`Invalid Hex character encountered at position ${index * 2}`);\n\t\t}\n\n\t\tbytes[index] = (highNibble << 4) | lowNibble; // eslint-disable-line no-bitwise\n\t}\n\n\treturn bytes;\n}\n\n/**\n@param {DataView} view\n@returns {number}\n*/\nfunction getUintBE(view) {\n\tconst {byteLength} = view;\n\n\tif (byteLength === 6) {\n\t\treturn (view.getUint16(0) * (2 ** 32)) + view.getUint32(2);\n\t}\n\n\tif (byteLength === 5) {\n\t\treturn (view.getUint8(0) * (2 ** 32)) + view.getUint32(1);\n\t}\n\n\tif (byteLength === 4) {\n\t\treturn view.getUint32(0);\n\t}\n\n\tif (byteLength === 3) {\n\t\treturn (view.getUint8(0) * (2 ** 16)) + view.getUint16(1);\n\t}\n\n\tif (byteLength === 2) {\n\t\treturn view.getUint16(0);\n\t}\n\n\tif (byteLength === 1) {\n\t\treturn view.getUint8(0);\n\t}\n}\n\n/**\n@param {Uint8Array} array\n@param {Uint8Array} value\n@returns {number}\n*/\nfunction indexOf(array, value) {\n\tconst arrayLength = array.length;\n\tconst valueLength = value.length;\n\n\tif (valueLength === 0) {\n\t\treturn -1;\n\t}\n\n\tif (valueLength > arrayLength) {\n\t\treturn -1;\n\t}\n\n\tconst validOffsetLength = arrayLength - valueLength;\n\n\tfor (let index = 0; index <= validOffsetLength; index++) {\n\t\tlet isMatch = true;\n\t\tfor (let index2 = 0; index2 < valueLength; index2++) {\n\t\t\tif (array[index + index2] !== value[index2]) {\n\t\t\t\tisMatch = false;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\tif (isMatch) {\n\t\t\treturn index;\n\t\t}\n\t}\n\n\treturn -1;\n}\n\n/**\n@param {Uint8Array} array\n@param {Uint8Array} value\n@returns {boolean}\n*/\nfunction includes(array, value) {\n\treturn indexOf(array, value) !== -1;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/uint8array-extras/index.js\n");

/***/ })

};
;