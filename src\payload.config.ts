import { buildConfig } from 'payload'
import { lexicalEditor } from '@payloadcms/richtext-lexical'
import path from 'path'
import sharp from 'sharp'

// Импортируем JSON адаптер
import { jsonAdapter } from '../payload-db-json/dist/index.js'

// Коллекции
import { Users } from './collections/Users'
import { Pages } from './collections/Pages'
import { Posts } from './collections/Posts'
import { Media } from './collections/Media'
import { Categories } from './collections/Categories'
import { Menus } from './collections/Menus'
import { Forms } from './collections/Forms'
import { FormSubmissions } from './collections/FormSubmissions'

// Глобальные настройки
import { Header } from './globals/Header'
import { Footer } from './globals/Footer'
import { Settings } from './globals/Settings'
import { SEO } from './globals/SEO'

export default buildConfig({
  // Основные настройки
  admin: {
    user: Users.slug,
    importMap: {
      baseDir: path.resolve(process.cwd()),
    },
    meta: {
      titleSuffix: '- Corporate CMS',
      favicon: '/favicon.ico',
      ogImage: '/og-image.jpg',
    },
    components: {
      // Можно добавить кастомные компоненты
    },
    dateFormat: 'dd.MM.yyyy HH:mm',
    livePreview: {
      breakpoints: [
        {
          label: 'Mobile',
          name: 'mobile',
          width: 375,
          height: 667,
        },
        {
          label: 'Tablet',
          name: 'tablet',
          width: 768,
          height: 1024,
        },
        {
          label: 'Desktop',
          name: 'desktop',
          width: 1440,
          height: 900,
        },
      ],
    },
  },

  // Коллекции
  collections: [
    Users,
    Pages,
    Posts,
    Media,
    Categories,
    Menus,
    Forms,
    FormSubmissions,
  ],

  // Глобальные настройки
  globals: [
    Header,
    Footer,
    Settings,
    SEO,
  ],

  // База данных - используем JSON адаптер
  db: jsonAdapter({
    dataDir: path.resolve(process.cwd(), 'data'),
    migrationDir: path.resolve(process.cwd(), 'migrations'),
    encryption: {
      enabled: true,
      algorithm: 'aes-256-gcm',
      key: process.env.ENCRYPTION_KEY || 'default-encryption-key-change-in-production'
    },
    cache: {
      enabled: true,
      ttl: 300000, // 5 минут
      maxSize: 1000
    },
    performance: {
      compression: true,
      enableIndexing: true,
      maxFileSize: 10 * 1024 * 1024 // 10MB
    }
  }),

  // Редактор
  editor: lexicalEditor({}),

  // Секретный ключ
  secret: process.env.PAYLOAD_SECRET || 'your-secret-here',

  // TypeScript настройки
  typescript: {
    outputFile: path.resolve(process.cwd(), 'payload-types.ts'),
  },

  // Настройки загрузки файлов
  upload: {
    limits: {
      fileSize: 50000000, // 50MB
    },
  },

  // Настройка Sharp для обработки изображений
  sharp,

  // Email адаптер отключен для разработки (будет выводиться в консоль)

  // Настройки CORS
  cors: [
    'http://localhost:3000',
    'http://localhost:3001',
    'https://localhost:3000',
    'https://localhost:3001',
  ],

  // CSRF защита
  csrf: [
    'http://localhost:3000',
    'http://localhost:3001',
    'https://localhost:3000',
    'https://localhost:3001',
  ],
})
