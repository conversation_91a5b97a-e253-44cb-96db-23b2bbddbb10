// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap, AppRouteHandlerRoutes } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/dist/lib/metadata/types/metadata-interface.js"
import type { NextRequest } from 'next/server.js'

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type RouteHandlerConfig<Route extends AppRouteHandlerRoutes = AppRouteHandlerRoutes> = {
  GET?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  POST?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PUT?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PATCH?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  DELETE?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  HEAD?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  OPTIONS?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
}


// Validate ../../src/app/(payload)/admin/[[...segments]]/page.tsx
{
  const handler = {} as typeof import("../../src/app/(payload)/admin/[[...segments]]/page.js")
  handler satisfies AppPageConfig<"/admin/[[...segments]]">
}

// Validate ../../src/app/(site)/about/page.tsx
{
  const handler = {} as typeof import("../../src/app/(site)/about/page.js")
  handler satisfies AppPageConfig<"/about">
}

// Validate ../../src/app/(site)/contact/page.tsx
{
  const handler = {} as typeof import("../../src/app/(site)/contact/page.js")
  handler satisfies AppPageConfig<"/contact">
}

// Validate ../../src/app/(site)/page.tsx
{
  const handler = {} as typeof import("../../src/app/(site)/page.js")
  handler satisfies AppPageConfig<"/">
}

// Validate ../../src/app/(site)/services/page.tsx
{
  const handler = {} as typeof import("../../src/app/(site)/services/page.js")
  handler satisfies AppPageConfig<"/services">
}

// Validate ../../src/app/(payload)/api/[[...slug]]/route.ts
{
  const handler = {} as typeof import("../../src/app/(payload)/api/[[...slug]]/route.js")
  handler satisfies RouteHandlerConfig<"/api/[[...slug]]">
}

// Validate ../../src/app/(payload)/api/graphql/route.ts
{
  const handler = {} as typeof import("../../src/app/(payload)/api/graphql/route.js")
  handler satisfies RouteHandlerConfig<"/api/graphql">
}





// Validate ../../src/app/(payload)/layout.tsx
{
  const handler = {} as typeof import("../../src/app/(payload)/layout.js")
  handler satisfies LayoutConfig<"/">
}

// Validate ../../src/app/(site)/layout.tsx
{
  const handler = {} as typeof import("../../src/app/(site)/layout.js")
  handler satisfies LayoutConfig<"/">
}
