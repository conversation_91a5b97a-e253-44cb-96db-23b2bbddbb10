{"node": {"6053084b43beec8019a5c567246c4649b880bbfb4a": {"workers": {"app/(payload)/admin/[[...segments]]/page": {"moduleId": "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5CWork%5C%5CPayload%5C%5Ctest-payload-json%5C%5Ccorporate-website%5C%5Cnode_modules%5C%5C%40payloadcms%5C%5Cnext%5C%5Cdist%5C%5Clayouts%5C%5CRoot%5C%5Cindex.js%22%2C%5B%7B%22id%22%3A%226053084b43beec8019a5c567246c4649b880bbfb4a%22%2C%22exportedName%22%3A%22%24%24RSC_SERVER_ACTION_0%22%2C%22filename%22%3A%22..%2FD%3A%5C%5CWork%5C%5CPayload%5C%5Ctest-payload-json%5C%5Ccorporate-website%5C%5Cnode_modules%5C%5C%40payloadcms%5C%5Cnext%5C%5Cdist%5C%5Clayouts%5C%5CRoot%5C%5Cindex.js%22%7D%5D%5D%2C%5B%22D%3A%5C%5CWork%5C%5CPayload%5C%5Ctest-payload-json%5C%5Ccorporate-website%5C%5Csrc%5C%5Capp%5C%5C(payload)%5C%5CserverFunction.ts%22%2C%5B%7B%22id%22%3A%22006fbcdb34218330596ed9948874aa0d01223ff462%22%2C%22exportedName%22%3A%22serverFunction%22%2C%22filename%22%3A%22..%2FD%3A%5C%5CWork%5C%5CPayload%5C%5Ctest-payload-json%5C%5Ccorporate-website%5C%5Csrc%5C%5Capp%5C%5C(payload)%5C%5CserverFunction.ts%22%7D%5D%5D%5D&__client_imported__=!", "async": false}}, "layer": {"app/(payload)/admin/[[...segments]]/page": "rsc"}, "filename": "../D:\\Work\\Payload\\test-payload-json\\corporate-website\\node_modules\\@payloadcms\\next\\dist\\layouts\\Root\\index.js", "exportedName": "$$RSC_SERVER_ACTION_0"}, "006fbcdb34218330596ed9948874aa0d01223ff462": {"workers": {"app/(payload)/admin/[[...segments]]/page": {"moduleId": "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5CWork%5C%5CPayload%5C%5Ctest-payload-json%5C%5Ccorporate-website%5C%5Cnode_modules%5C%5C%40payloadcms%5C%5Cnext%5C%5Cdist%5C%5Clayouts%5C%5CRoot%5C%5Cindex.js%22%2C%5B%7B%22id%22%3A%226053084b43beec8019a5c567246c4649b880bbfb4a%22%2C%22exportedName%22%3A%22%24%24RSC_SERVER_ACTION_0%22%2C%22filename%22%3A%22..%2FD%3A%5C%5CWork%5C%5CPayload%5C%5Ctest-payload-json%5C%5Ccorporate-website%5C%5Cnode_modules%5C%5C%40payloadcms%5C%5Cnext%5C%5Cdist%5C%5Clayouts%5C%5CRoot%5C%5Cindex.js%22%7D%5D%5D%2C%5B%22D%3A%5C%5CWork%5C%5CPayload%5C%5Ctest-payload-json%5C%5Ccorporate-website%5C%5Csrc%5C%5Capp%5C%5C(payload)%5C%5CserverFunction.ts%22%2C%5B%7B%22id%22%3A%22006fbcdb34218330596ed9948874aa0d01223ff462%22%2C%22exportedName%22%3A%22serverFunction%22%2C%22filename%22%3A%22..%2FD%3A%5C%5CWork%5C%5CPayload%5C%5Ctest-payload-json%5C%5Ccorporate-website%5C%5Csrc%5C%5Capp%5C%5C(payload)%5C%5CserverFunction.ts%22%7D%5D%5D%5D&__client_imported__=!", "async": false}}, "layer": {"app/(payload)/admin/[[...segments]]/page": "rsc"}, "filename": "../D:\\Work\\Payload\\test-payload-json\\corporate-website\\src\\app\\(payload)\\serverFunction.ts", "exportedName": "serverFunction"}}, "edge": {}, "encryptionKey": "J7qp3c48S4grlCZYAqqRJbSSDoD0yOzXA3IYhV3QMK4="}