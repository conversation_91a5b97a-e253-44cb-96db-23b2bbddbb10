/*!**********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[10].use[3]!./node_modules/react-image-crop/dist/ReactCrop.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************/
.ReactCrop{position:relative;display:inline-block;cursor:crosshair;overflow:hidden;max-width:100%}.ReactCrop *,.ReactCrop *:before,.ReactCrop *:after{-webkit-box-sizing:border-box;box-sizing:border-box}.ReactCrop--disabled,.ReactCrop--locked{cursor:inherit}.ReactCrop__child-wrapper{max-height:inherit}.ReactCrop__child-wrapper>img,.ReactCrop__child-wrapper>video{display:block;max-width:100%;max-height:inherit}.ReactCrop:not(.ReactCrop--disabled) .ReactCrop__child-wrapper>img,.ReactCrop:not(.ReactCrop--disabled) .ReactCrop__child-wrapper>video{-ms-touch-action:none;touch-action:none}.ReactCrop:not(.ReactCrop--disabled) .ReactCrop__crop-selection{-ms-touch-action:none;touch-action:none}.ReactCrop__crop-selection{position:absolute;top:0;left:0;-webkit-transform:translateZ(0);transform:translateZ(0);cursor:move;-webkit-box-shadow:0 0 0 9999em #00000080;box-shadow:0 0 0 9999em #00000080}.ReactCrop--disabled .ReactCrop__crop-selection{cursor:inherit}.ReactCrop--circular-crop .ReactCrop__crop-selection{border-radius:50%}.ReactCrop--no-animate .ReactCrop__crop-selection{outline:1px dashed white}.ReactCrop__crop-selection:not(.ReactCrop--no-animate .ReactCrop__crop-selection){-webkit-animation:marching-ants 1s;animation:marching-ants 1s;background-image:-webkit-gradient(linear,left top, right top,color-stop(50%, #fff),color-stop(50%, #444)),-webkit-gradient(linear,left top, right top,color-stop(50%, #fff),color-stop(50%, #444)),-webkit-gradient(linear,left top, left bottom,color-stop(50%, #fff),color-stop(50%, #444)),-webkit-gradient(linear,left top, left bottom,color-stop(50%, #fff),color-stop(50%, #444));background-image:linear-gradient(to right,#fff 50%,#444 50%),linear-gradient(to right,#fff 50%,#444 50%),linear-gradient(to bottom,#fff 50%,#444 50%),linear-gradient(to bottom,#fff 50%,#444 50%);background-size:10px 1px,10px 1px,1px 10px,1px 10px;background-position:0 0,0 100%,0 0,100% 0;background-repeat:repeat-x,repeat-x,repeat-y,repeat-y;color:#fff;-webkit-animation-play-state:running;animation-play-state:running;-webkit-animation-timing-function:linear;animation-timing-function:linear;-webkit-animation-iteration-count:infinite;animation-iteration-count:infinite}@-webkit-keyframes marching-ants{0%{background-position:0 0,0 100%,0 0,100% 0}to{background-position:20px 0,-20px 100%,0 -20px,100% 20px}}@keyframes marching-ants{0%{background-position:0 0,0 100%,0 0,100% 0}to{background-position:20px 0,-20px 100%,0 -20px,100% 20px}}.ReactCrop__crop-selection:focus{outline:none;border-color:#00f;border-style:solid}.ReactCrop--invisible-crop .ReactCrop__crop-selection{display:none}.ReactCrop__rule-of-thirds-vt:before,.ReactCrop__rule-of-thirds-vt:after,.ReactCrop__rule-of-thirds-hz:before,.ReactCrop__rule-of-thirds-hz:after{content:"";display:block;position:absolute;background-color:#fff6}.ReactCrop__rule-of-thirds-vt:before,.ReactCrop__rule-of-thirds-vt:after{width:1px;height:100%}.ReactCrop__rule-of-thirds-vt:before{left:33.3333333333%}.ReactCrop__rule-of-thirds-vt:after{left:66.6666666667%}.ReactCrop__rule-of-thirds-hz:before,.ReactCrop__rule-of-thirds-hz:after{width:100%;height:1px}.ReactCrop__rule-of-thirds-hz:before{top:33.3333333333%}.ReactCrop__rule-of-thirds-hz:after{top:66.6666666667%}.ReactCrop__drag-handle{position:absolute}.ReactCrop__drag-handle:after{position:absolute;content:"";display:block;width:10px;height:10px;background-color:#0003;border:1px solid rgba(255,255,255,.7);outline:1px solid transparent}.ReactCrop__drag-handle:focus:after{border-color:#00f;background:#2dbfff}.ReactCrop .ord-nw{top:0;left:0;margin-top:-5px;margin-left:-5px;cursor:nw-resize}.ReactCrop .ord-nw:after{top:0;left:0}.ReactCrop .ord-n{top:0;left:50%;margin-top:-5px;margin-left:-5px;cursor:n-resize}.ReactCrop .ord-n:after{top:0}.ReactCrop .ord-ne{top:0;right:0;margin-top:-5px;margin-right:-5px;cursor:ne-resize}.ReactCrop .ord-ne:after{top:0;right:0}.ReactCrop .ord-e{top:50%;right:0;margin-top:-5px;margin-right:-5px;cursor:e-resize}.ReactCrop .ord-e:after{right:0}.ReactCrop .ord-se{bottom:0;right:0;margin-bottom:-5px;margin-right:-5px;cursor:se-resize}.ReactCrop .ord-se:after{bottom:0;right:0}.ReactCrop .ord-s{bottom:0;left:50%;margin-bottom:-5px;margin-left:-5px;cursor:s-resize}.ReactCrop .ord-s:after{bottom:0}.ReactCrop .ord-sw{bottom:0;left:0;margin-bottom:-5px;margin-left:-5px;cursor:sw-resize}.ReactCrop .ord-sw:after{bottom:0;left:0}.ReactCrop .ord-w{top:50%;left:0;margin-top:-5px;margin-left:-5px;cursor:w-resize}.ReactCrop .ord-w:after{left:0}.ReactCrop__disabled .ReactCrop__drag-handle{cursor:inherit}.ReactCrop__drag-bar{position:absolute}.ReactCrop__drag-bar.ord-n{top:0;left:0;width:100%;height:6px;margin-top:-3px}.ReactCrop__drag-bar.ord-e{right:0;top:0;width:6px;height:100%;margin-right:-3px}.ReactCrop__drag-bar.ord-s{bottom:0;left:0;width:100%;height:6px;margin-bottom:-3px}.ReactCrop__drag-bar.ord-w{top:0;left:0;width:6px;height:100%;margin-left:-3px}.ReactCrop--new-crop .ReactCrop__drag-bar,.ReactCrop--new-crop .ReactCrop__drag-handle,.ReactCrop--fixed-aspect .ReactCrop__drag-bar,.ReactCrop--fixed-aspect .ReactCrop__drag-handle.ord-n,.ReactCrop--fixed-aspect .ReactCrop__drag-handle.ord-e,.ReactCrop--fixed-aspect .ReactCrop__drag-handle.ord-s,.ReactCrop--fixed-aspect .ReactCrop__drag-handle.ord-w{display:none}@media (pointer: coarse){.ReactCrop .ord-n,.ReactCrop .ord-e,.ReactCrop .ord-s,.ReactCrop .ord-w{display:none}.ReactCrop__drag-handle{width:24px;height:24px}}

/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[11].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[11].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[11].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[11].use[5]!./node_modules/@payloadcms/ui/dist/elements/Banner/index.scss ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@layer payload-default {
  .banner {
    font-size: 1rem;
    line-height: 20px;
    border: 0;
    vertical-align: middle;
    background: var(--theme-elevation-100);
    color: var(--theme-elevation-800);
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 1.5384615385rem;
  }
  .banner--has-action {
    cursor: pointer;
    text-decoration: none;
  }
  .banner--has-icon {
    display: flex;
  }
  .banner--has-icon svg {
    display: block;
  }
  .banner--type-default.button--has-action:hover {
    background: var(--theme-elevation-900);
  }
  .banner--type-default.button--has-action:active {
    background: var(--theme-elevation-950);
  }
  .banner--type-error {
    background: var(--theme-error-100);
    color: var(--theme-error-600);
  }
  .banner--type-error svg .stroke {
    stroke: var(--theme-error-600);
    fill: none;
  }
  .banner--type-error svg .fill {
    fill: var(--theme-error-600);
  }
  .banner--type-error.button--has-action:hover {
    background: var(--theme-error-200);
  }
  .banner--type-error.button--has-action:active {
    background: var(--theme-error-300);
  }
  .banner--type-success {
    background: var(--theme-success-100);
    color: var(--theme-success-600);
  }
  .banner--type-success.button--has-action:hover {
    background: var(--theme-success-200);
  }
  .banner--type-success.button--has-action:active {
    background: var(--theme-success-200);
  }}
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[11].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[11].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[11].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[11].use[5]!./node_modules/@payloadcms/ui/dist/elements/FieldDiffContainer/index.scss ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@layer payload-default {
  .field-diff__locale-label {
    background: var(--theme-elevation-100);
    border-radius: var(--style-radius-s);
    padding: calc(var(--base) * 0.2);
  }
  [dir=ltr] .field-diff__locale-label {
    margin-right: calc(var(--base) * 0.25);
  }
  [dir=rtl] .field-diff__locale-label {
    margin-left: calc(var(--base) * 0.25);
  }
  .field-diff-container {
    position: relative;
    /*
    &::after {
      content: '';
      position: absolute;
      top: 0;
      bottom: 0;
      left: var(--left-offset);
      width: 1px;
      background-color: var(--theme-elevation-100);
      transform: translateX(-50%); // Center the line
    }*/
  }
  .field-diff-content {
    display: grid;
    grid-template-columns: calc(50% - 10px) calc(50% - 10px);
    gap: 20px;
    background: var(--theme-elevation-50);
    padding: 10px;
  }}
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[11].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[11].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[11].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[11].use[5]!./node_modules/@payloadcms/ui/dist/elements/FieldDiffLabel/index.scss ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@layer payload-default {
  .field-diff-label {
    margin-bottom: calc(var(--base) * 0.35);
    font-weight: 600;
    display: flex;
    flex-direction: row;
    height: 100%;
    align-items: center;
    line-height: normal;
  }
}
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[11].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[11].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[11].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[11].use[5]!./node_modules/@payloadcms/ui/dist/elements/FolderView/FolderField/index.scss ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@layer payload-default {
  .folder-edit-field {
    display: none;
  }
  .edit-many-bulk-uploads__main .folder-edit-field,
  .edit-many__main .folder-edit-field {
    display: initial;
  }
}
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[11].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[11].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[11].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[11].use[5]!./node_modules/@payloadcms/ui/dist/elements/HTMLDiff/index.scss ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@layer payload-default {
  :root {
    --diff-delete-pill-bg: var(--theme-error-200);
    --diff-delete-pill-color: var(--theme-error-600);
    --diff-delete-pill-border: var(--theme-error-400);
    --diff-delete-parent-bg: var(--theme-error-100);
    --diff-delete-parent-color: var(--theme-error-800);
    --diff-delete-link-color: var(--theme-error-600);
    --diff-create-pill-bg: var(--theme-success-200);
    --diff-create-pill-color: var(--theme-success-600);
    --diff-create-pill-border: var(--theme-success-400);
    --diff-create-parent-bg: var(--theme-success-100);
    --diff-create-parent-color: var(--theme-success-800);
    --diff-create-link-color: var(--theme-success-600);
  }
  html[data-theme=dark] {
    --diff-delete-pill-bg: var(--theme-error-200);
    --diff-delete-pill-color: var(--theme-error-650);
    --diff-delete-pill-border: var(--theme-error-400);
    --diff-delete-parent-bg: var(--theme-error-100);
    --diff-delete-parent-color: var(--theme-error-900);
    --diff-delete-link-color: var(--theme-error-750);
    --diff-create-pill-bg: var(--theme-success-200);
    --diff-create-pill-color: var(--theme-success-650);
    --diff-create-pill-border: var(--theme-success-400);
    --diff-create-parent-bg: var(--theme-success-100);
    --diff-create-parent-color: var(--theme-success-900);
    --diff-create-link-color: var(--theme-success-750);
  }
  .html-diff {
    font-size: 14px;
    letter-spacing: 0.02em;
  }
  .html-diff-no-value {
    color: var(--theme-elevation-400);
  }
  .html-diff pre {
    margin-top: 0;
    margin-bottom: 0;
  }
  .html-diff p:not([data-enable-match=false]):has([data-match-type=create]),
  .html-diff h1:not([data-enable-match=false]):has([data-match-type=create]),
  .html-diff h2:not([data-enable-match=false]):has([data-match-type=create]),
  .html-diff h3:not([data-enable-match=false]):has([data-match-type=create]),
  .html-diff h4:not([data-enable-match=false]):has([data-match-type=create]),
  .html-diff h5:not([data-enable-match=false]):has([data-match-type=create]),
  .html-diff blockquote:not([data-enable-match=false]):has([data-match-type=create]),
  .html-diff pre:not([data-enable-match=false]):has([data-match-type=create]),
  .html-diff h6:not([data-enable-match=false]):has([data-match-type=create]) {
    position: relative;
    z-index: 1;
  }
  .html-diff p:not([data-enable-match=false]):has([data-match-type=create])::before,
  .html-diff h1:not([data-enable-match=false]):has([data-match-type=create])::before,
  .html-diff h2:not([data-enable-match=false]):has([data-match-type=create])::before,
  .html-diff h3:not([data-enable-match=false]):has([data-match-type=create])::before,
  .html-diff h4:not([data-enable-match=false]):has([data-match-type=create])::before,
  .html-diff h5:not([data-enable-match=false]):has([data-match-type=create])::before,
  .html-diff blockquote:not([data-enable-match=false]):has([data-match-type=create])::before,
  .html-diff pre:not([data-enable-match=false]):has([data-match-type=create])::before,
  .html-diff h6:not([data-enable-match=false]):has([data-match-type=create])::before {
    content: "";
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
    display: block;
    background-color: var(--diff-create-parent-bg);
    color: var(--diff-create-parent-color);
    z-index: -1; /* Place behind the text */
  }
  .html-diff p:not([data-enable-match=false]):has([data-match-type=delete]),
  .html-diff h1:not([data-enable-match=false]):has([data-match-type=delete]),
  .html-diff h2:not([data-enable-match=false]):has([data-match-type=delete]),
  .html-diff h3:not([data-enable-match=false]):has([data-match-type=delete]),
  .html-diff h4:not([data-enable-match=false]):has([data-match-type=delete]),
  .html-diff h5:not([data-enable-match=false]):has([data-match-type=delete]),
  .html-diff blockquote:not([data-enable-match=false]):has([data-match-type=delete]),
  .html-diff pre:not([data-enable-match=false]):has([data-match-type=delete]),
  .html-diff h6:not([data-enable-match=false]):has([data-match-type=delete]) {
    position: relative;
    z-index: 1;
  }
  .html-diff p:not([data-enable-match=false]):has([data-match-type=delete])::before,
  .html-diff h1:not([data-enable-match=false]):has([data-match-type=delete])::before,
  .html-diff h2:not([data-enable-match=false]):has([data-match-type=delete])::before,
  .html-diff h3:not([data-enable-match=false]):has([data-match-type=delete])::before,
  .html-diff h4:not([data-enable-match=false]):has([data-match-type=delete])::before,
  .html-diff h5:not([data-enable-match=false]):has([data-match-type=delete])::before,
  .html-diff blockquote:not([data-enable-match=false]):has([data-match-type=delete])::before,
  .html-diff pre:not([data-enable-match=false]):has([data-match-type=delete])::before,
  .html-diff h6:not([data-enable-match=false]):has([data-match-type=delete])::before {
    content: "";
    position: absolute;
    top: -10px;
    bottom: -10px;
    left: -10px;
    right: -10px;
    display: block;
    background-color: var(--diff-delete-parent-bg);
    color: var(--diff-delete-parent-color);
    z-index: -1; /* Place behind the text */
  }
  .html-diff li:not([data-enable-match=false]):has([data-match-type=create]) {
    position: relative;
    z-index: 1;
  }
  .html-diff li:not([data-enable-match=false]):has([data-match-type=create])::before {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    left: -10px;
    right: -10px;
    display: block;
    background-color: var(--diff-create-parent-bg);
    color: var(--diff-create-parent-color);
    z-index: -1; /* Place behind the text */
  }
  .html-diff li:not([data-enable-match=false]):has([data-match-type=delete]) {
    position: relative;
    z-index: 1;
  }
  .html-diff li:not([data-enable-match=false]):has([data-match-type=delete])::before {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    left: -10px;
    right: -10px;
    display: block;
    background-color: var(--diff-delete-parent-bg);
    color: var(--diff-delete-parent-color);
    z-index: -1; /* Place behind the text */
  }
  .html-diff li::marker {
    color: var(--theme-text);
  }
  .html-diff [data-match-type=delete]:not([data-enable-match=false]):not(:is([data-enable-match=false] *)) {
    color: var(--diff-delete-pill-color);
    text-decoration-color: var(--diff-delete-pill-color);
    text-decoration-line: line-through;
    background-color: var(--diff-delete-pill-bg);
    border-radius: 4px;
    text-decoration-thickness: 1px;
  }
  .html-diff a[data-match-type=delete] :not([data-enable-match=false]) :not(:is([data-enable-match=false] *)) {
    color: var(--diff-delete-link-color);
  }
  .html-diff a[data-match-type=create]:not(img) :not([data-enable-match=false]) :not(:is([data-enable-match=false] *)) {
    color: var(--diff-create-link-color);
  }
  .html-diff [data-match-type=create]:not(img):not([data-enable-match=false]):not(:is([data-enable-match=false] *)) {
    background-color: var(--diff-create-pill-bg);
    color: var(--diff-create-pill-color);
    border-radius: 4px;
  }
  .html-diff .html-diff-create-inline-wrapper, .html-diff .html-diff-delete-inline-wrapper {
    display: inline-flex;
  }
  .html-diff .html-diff-create-block-wrapper, .html-diff .html-diff-delete-block-wrapper {
    display: flex;
  }
  .html-diff .html-diff-create-inline-wrapper, .html-diff .html-diff-delete-inline-wrapper, .html-diff .html-diff-create-block-wrapper, .html-diff .html-diff-delete-block-wrapper {
    position: relative;
    align-items: center;
    flex-direction: row;
  }
  .html-diff .html-diff-create-inline-wrapper::after, .html-diff .html-diff-delete-inline-wrapper::after, .html-diff .html-diff-create-block-wrapper::after, .html-diff .html-diff-delete-block-wrapper::after {
    position: absolute;
    top: 0;
    left: 0;
    display: block;
    width: 100%;
    height: 100%;
    content: "";
  }}
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[11].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[11].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[11].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[11].use[5]!./node_modules/@payloadcms/ui/dist/icons/Check/index.scss ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@layer payload-default {
  .icon--check {
    height: 1.5384615385rem;
    width: 1.5384615385rem;
  }
  .icon--check .stroke {
    fill: none;
    stroke: currentColor;
    stroke-width: 2px;
  }}
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[11].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[11].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[11].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[11].use[5]!./node_modules/@payloadcms/ui/dist/scss/styles.scss ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@layer payload-default {}
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[11].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[11].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[11].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[11].use[5]!./src/app/(payload)/custom.scss ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
/* Кастомные стили для админ-панели PayloadCMS */
:root {
  --theme-bg: #fafafa;
  --theme-input-bg: #ffffff;
  --theme-elevation-0: #ffffff;
  --theme-elevation-50: #f9fafb;
  --theme-elevation-100: #f3f4f6;
  --theme-elevation-200: #e5e7eb;
  --theme-elevation-300: #d1d5db;
  --theme-elevation-400: #9ca3af;
  --theme-elevation-500: #6b7280;
  --theme-elevation-600: #4b5563;
  --theme-elevation-700: #374151;
  --theme-elevation-800: #1f2937;
  --theme-elevation-900: #111827;
  --theme-text: #111827;
  --theme-success-50: #f0f9ff;
  --theme-success-100: #e0f2fe;
  --theme-success-200: #bae6fd;
  --theme-success-300: #7dd3fc;
  --theme-success-400: #38bdf8;
  --theme-success-500: #0ea5e9;
  --theme-success-600: #0284c7;
  --theme-success-700: #0369a1;
  --theme-success-800: #075985;
  --theme-success-900: #0c4a6e;
}

/* Кастомизация навигации */
.nav {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
}

.nav__brand {
  font-weight: 700;
  font-size: 1.25rem;
}

/* Кастомизация карточек */
.card {
  border-radius: 12px;
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Кастомизация кнопок */
.btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn--style-primary {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
  border: none;
}

.btn--style-primary:hover {
  background: linear-gradient(135deg, #0284c7 0%, #0369a1 100%);
  transform: translateY(-1px);
}

/* Кастомизация форм */
.field-type {
  margin-bottom: 1.5rem;
}

.field-type__label {
  font-weight: 600;
  color: var(--theme-elevation-800);
  margin-bottom: 0.5rem;
}

.text-input,
.textarea,
.select {
  border-radius: 8px;
  border: 1px solid var(--theme-elevation-300);
  transition: all 0.2s ease;
}

.text-input:focus,
.textarea:focus,
.select:focus {
  border-color: var(--theme-success-500);
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

/* Кастомизация таблиц */
.table {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07);
}

.table__header {
  background: var(--theme-elevation-50);
  font-weight: 600;
}

.table__row:hover {
  background: var(--theme-elevation-50);
}

/* Кастомизация дашборда */
.dashboard {
  padding: 2rem;
}

.dashboard__welcome {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
  color: white;
  padding: 2rem;
  border-radius: 12px;
  margin-bottom: 2rem;
}

.dashboard__stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07);
  transition: all 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Адаптивность */
@media (max-width: 768px) {
  .dashboard {
    padding: 1rem;
  }
  .dashboard__stats {
    grid-template-columns: 1fr;
  }
}
/* Темная тема */
@media (prefers-color-scheme: dark) {
  :root {
    --theme-bg: #111827;
    --theme-input-bg: #1f2937;
    --theme-elevation-0: #1f2937;
    --theme-elevation-50: #374151;
    --theme-text: #f9fafb;
  }
}
