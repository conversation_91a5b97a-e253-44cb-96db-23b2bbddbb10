"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@dnd-kit";
exports.ids = ["vendor-chunks/@dnd-kit"];
exports.modules = {

/***/ "(ssr)/./node_modules/@dnd-kit/accessibility/dist/accessibility.esm.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@dnd-kit/accessibility/dist/accessibility.esm.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HiddenText: () => (/* binding */ HiddenText),\n/* harmony export */   LiveRegion: () => (/* binding */ LiveRegion),\n/* harmony export */   useAnnouncement: () => (/* binding */ useAnnouncement)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\nconst hiddenStyles = {\n  display: 'none'\n};\nfunction HiddenText(_ref) {\n  let {\n    id,\n    value\n  } = _ref;\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    id: id,\n    style: hiddenStyles\n  }, value);\n}\n\nfunction LiveRegion(_ref) {\n  let {\n    id,\n    announcement,\n    ariaLiveType = \"assertive\"\n  } = _ref;\n  // Hide element visually but keep it readable by screen readers\n  const visuallyHidden = {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    width: 1,\n    height: 1,\n    margin: -1,\n    border: 0,\n    padding: 0,\n    overflow: 'hidden',\n    clip: 'rect(0 0 0 0)',\n    clipPath: 'inset(100%)',\n    whiteSpace: 'nowrap'\n  };\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    id: id,\n    style: visuallyHidden,\n    role: \"status\",\n    \"aria-live\": ariaLiveType,\n    \"aria-atomic\": true\n  }, announcement);\n}\n\nfunction useAnnouncement() {\n  const [announcement, setAnnouncement] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n  const announce = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(value => {\n    if (value != null) {\n      setAnnouncement(value);\n    }\n  }, []);\n  return {\n    announce,\n    announcement\n  };\n}\n\n\n//# sourceMappingURL=accessibility.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@dnd-kit/accessibility/dist/accessibility.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@dnd-kit/core/dist/core.esm.js":
/*!*****************************************************!*\
  !*** ./node_modules/@dnd-kit/core/dist/core.esm.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AutoScrollActivator: () => (/* binding */ AutoScrollActivator),\n/* harmony export */   DndContext: () => (/* binding */ DndContext),\n/* harmony export */   DragOverlay: () => (/* binding */ DragOverlay),\n/* harmony export */   KeyboardCode: () => (/* binding */ KeyboardCode),\n/* harmony export */   KeyboardSensor: () => (/* binding */ KeyboardSensor),\n/* harmony export */   MeasuringFrequency: () => (/* binding */ MeasuringFrequency),\n/* harmony export */   MeasuringStrategy: () => (/* binding */ MeasuringStrategy),\n/* harmony export */   MouseSensor: () => (/* binding */ MouseSensor),\n/* harmony export */   PointerSensor: () => (/* binding */ PointerSensor),\n/* harmony export */   TouchSensor: () => (/* binding */ TouchSensor),\n/* harmony export */   TraversalOrder: () => (/* binding */ TraversalOrder),\n/* harmony export */   applyModifiers: () => (/* binding */ applyModifiers),\n/* harmony export */   closestCenter: () => (/* binding */ closestCenter),\n/* harmony export */   closestCorners: () => (/* binding */ closestCorners),\n/* harmony export */   defaultAnnouncements: () => (/* binding */ defaultAnnouncements),\n/* harmony export */   defaultCoordinates: () => (/* binding */ defaultCoordinates),\n/* harmony export */   defaultDropAnimation: () => (/* binding */ defaultDropAnimationConfiguration),\n/* harmony export */   defaultDropAnimationSideEffects: () => (/* binding */ defaultDropAnimationSideEffects),\n/* harmony export */   defaultScreenReaderInstructions: () => (/* binding */ defaultScreenReaderInstructions),\n/* harmony export */   getClientRect: () => (/* binding */ getClientRect),\n/* harmony export */   getFirstCollision: () => (/* binding */ getFirstCollision),\n/* harmony export */   getScrollableAncestors: () => (/* binding */ getScrollableAncestors),\n/* harmony export */   pointerWithin: () => (/* binding */ pointerWithin),\n/* harmony export */   rectIntersection: () => (/* binding */ rectIntersection),\n/* harmony export */   useDndContext: () => (/* binding */ useDndContext),\n/* harmony export */   useDndMonitor: () => (/* binding */ useDndMonitor),\n/* harmony export */   useDraggable: () => (/* binding */ useDraggable),\n/* harmony export */   useDroppable: () => (/* binding */ useDroppable),\n/* harmony export */   useSensor: () => (/* binding */ useSensor),\n/* harmony export */   useSensors: () => (/* binding */ useSensors)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @dnd-kit/utilities */ \"(ssr)/./node_modules/@dnd-kit/utilities/dist/utilities.esm.js\");\n/* harmony import */ var _dnd_kit_accessibility__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @dnd-kit/accessibility */ \"(ssr)/./node_modules/@dnd-kit/accessibility/dist/accessibility.esm.js\");\n\n\n\n\n\nconst DndMonitorContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\n\nfunction useDndMonitor(listener) {\n  const registerListener = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(DndMonitorContext);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!registerListener) {\n      throw new Error('useDndMonitor must be used within a children of <DndContext>');\n    }\n\n    const unsubscribe = registerListener(listener);\n    return unsubscribe;\n  }, [listener, registerListener]);\n}\n\nfunction useDndMonitorProvider() {\n  const [listeners] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new Set());\n  const registerListener = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(listener => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  }, [listeners]);\n  const dispatch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(_ref => {\n    let {\n      type,\n      event\n    } = _ref;\n    listeners.forEach(listener => {\n      var _listener$type;\n\n      return (_listener$type = listener[type]) == null ? void 0 : _listener$type.call(listener, event);\n    });\n  }, [listeners]);\n  return [dispatch, registerListener];\n}\n\nconst defaultScreenReaderInstructions = {\n  draggable: \"\\n    To pick up a draggable item, press the space bar.\\n    While dragging, use the arrow keys to move the item.\\n    Press space again to drop the item in its new position, or press escape to cancel.\\n  \"\n};\nconst defaultAnnouncements = {\n  onDragStart(_ref) {\n    let {\n      active\n    } = _ref;\n    return \"Picked up draggable item \" + active.id + \".\";\n  },\n\n  onDragOver(_ref2) {\n    let {\n      active,\n      over\n    } = _ref2;\n\n    if (over) {\n      return \"Draggable item \" + active.id + \" was moved over droppable area \" + over.id + \".\";\n    }\n\n    return \"Draggable item \" + active.id + \" is no longer over a droppable area.\";\n  },\n\n  onDragEnd(_ref3) {\n    let {\n      active,\n      over\n    } = _ref3;\n\n    if (over) {\n      return \"Draggable item \" + active.id + \" was dropped over droppable area \" + over.id;\n    }\n\n    return \"Draggable item \" + active.id + \" was dropped.\";\n  },\n\n  onDragCancel(_ref4) {\n    let {\n      active\n    } = _ref4;\n    return \"Dragging was cancelled. Draggable item \" + active.id + \" was dropped.\";\n  }\n\n};\n\nfunction Accessibility(_ref) {\n  let {\n    announcements = defaultAnnouncements,\n    container,\n    hiddenTextDescribedById,\n    screenReaderInstructions = defaultScreenReaderInstructions\n  } = _ref;\n  const {\n    announce,\n    announcement\n  } = (0,_dnd_kit_accessibility__WEBPACK_IMPORTED_MODULE_3__.useAnnouncement)();\n  const liveRegionId = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useUniqueId)(\"DndLiveRegion\");\n  const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    setMounted(true);\n  }, []);\n  useDndMonitor((0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    onDragStart(_ref2) {\n      let {\n        active\n      } = _ref2;\n      announce(announcements.onDragStart({\n        active\n      }));\n    },\n\n    onDragMove(_ref3) {\n      let {\n        active,\n        over\n      } = _ref3;\n\n      if (announcements.onDragMove) {\n        announce(announcements.onDragMove({\n          active,\n          over\n        }));\n      }\n    },\n\n    onDragOver(_ref4) {\n      let {\n        active,\n        over\n      } = _ref4;\n      announce(announcements.onDragOver({\n        active,\n        over\n      }));\n    },\n\n    onDragEnd(_ref5) {\n      let {\n        active,\n        over\n      } = _ref5;\n      announce(announcements.onDragEnd({\n        active,\n        over\n      }));\n    },\n\n    onDragCancel(_ref6) {\n      let {\n        active,\n        over\n      } = _ref6;\n      announce(announcements.onDragCancel({\n        active,\n        over\n      }));\n    }\n\n  }), [announce, announcements]));\n\n  if (!mounted) {\n    return null;\n  }\n\n  const markup = react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_dnd_kit_accessibility__WEBPACK_IMPORTED_MODULE_3__.HiddenText, {\n    id: hiddenTextDescribedById,\n    value: screenReaderInstructions.draggable\n  }), react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_dnd_kit_accessibility__WEBPACK_IMPORTED_MODULE_3__.LiveRegion, {\n    id: liveRegionId,\n    announcement: announcement\n  }));\n  return container ? (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(markup, container) : markup;\n}\n\nvar Action;\n\n(function (Action) {\n  Action[\"DragStart\"] = \"dragStart\";\n  Action[\"DragMove\"] = \"dragMove\";\n  Action[\"DragEnd\"] = \"dragEnd\";\n  Action[\"DragCancel\"] = \"dragCancel\";\n  Action[\"DragOver\"] = \"dragOver\";\n  Action[\"RegisterDroppable\"] = \"registerDroppable\";\n  Action[\"SetDroppableDisabled\"] = \"setDroppableDisabled\";\n  Action[\"UnregisterDroppable\"] = \"unregisterDroppable\";\n})(Action || (Action = {}));\n\nfunction noop() {}\n\nfunction useSensor(sensor, options) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    sensor,\n    options: options != null ? options : {}\n  }), // eslint-disable-next-line react-hooks/exhaustive-deps\n  [sensor, options]);\n}\n\nfunction useSensors() {\n  for (var _len = arguments.length, sensors = new Array(_len), _key = 0; _key < _len; _key++) {\n    sensors[_key] = arguments[_key];\n  }\n\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => [...sensors].filter(sensor => sensor != null), // eslint-disable-next-line react-hooks/exhaustive-deps\n  [...sensors]);\n}\n\nconst defaultCoordinates = /*#__PURE__*/Object.freeze({\n  x: 0,\n  y: 0\n});\n\n/**\r\n * Returns the distance between two points\r\n */\nfunction distanceBetween(p1, p2) {\n  return Math.sqrt(Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2));\n}\n\nfunction getRelativeTransformOrigin(event, rect) {\n  const eventCoordinates = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getEventCoordinates)(event);\n\n  if (!eventCoordinates) {\n    return '0 0';\n  }\n\n  const transformOrigin = {\n    x: (eventCoordinates.x - rect.left) / rect.width * 100,\n    y: (eventCoordinates.y - rect.top) / rect.height * 100\n  };\n  return transformOrigin.x + \"% \" + transformOrigin.y + \"%\";\n}\n\n/**\r\n * Sort collisions from smallest to greatest value\r\n */\nfunction sortCollisionsAsc(_ref, _ref2) {\n  let {\n    data: {\n      value: a\n    }\n  } = _ref;\n  let {\n    data: {\n      value: b\n    }\n  } = _ref2;\n  return a - b;\n}\n/**\r\n * Sort collisions from greatest to smallest value\r\n */\n\nfunction sortCollisionsDesc(_ref3, _ref4) {\n  let {\n    data: {\n      value: a\n    }\n  } = _ref3;\n  let {\n    data: {\n      value: b\n    }\n  } = _ref4;\n  return b - a;\n}\n/**\r\n * Returns the coordinates of the corners of a given rectangle:\r\n * [TopLeft {x, y}, TopRight {x, y}, BottomLeft {x, y}, BottomRight {x, y}]\r\n */\n\nfunction cornersOfRectangle(_ref5) {\n  let {\n    left,\n    top,\n    height,\n    width\n  } = _ref5;\n  return [{\n    x: left,\n    y: top\n  }, {\n    x: left + width,\n    y: top\n  }, {\n    x: left,\n    y: top + height\n  }, {\n    x: left + width,\n    y: top + height\n  }];\n}\nfunction getFirstCollision(collisions, property) {\n  if (!collisions || collisions.length === 0) {\n    return null;\n  }\n\n  const [firstCollision] = collisions;\n  return property ? firstCollision[property] : firstCollision;\n}\n\n/**\r\n * Returns the coordinates of the center of a given ClientRect\r\n */\n\nfunction centerOfRectangle(rect, left, top) {\n  if (left === void 0) {\n    left = rect.left;\n  }\n\n  if (top === void 0) {\n    top = rect.top;\n  }\n\n  return {\n    x: left + rect.width * 0.5,\n    y: top + rect.height * 0.5\n  };\n}\n/**\r\n * Returns the closest rectangles from an array of rectangles to the center of a given\r\n * rectangle.\r\n */\n\n\nconst closestCenter = _ref => {\n  let {\n    collisionRect,\n    droppableRects,\n    droppableContainers\n  } = _ref;\n  const centerRect = centerOfRectangle(collisionRect, collisionRect.left, collisionRect.top);\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const distBetween = distanceBetween(centerOfRectangle(rect), centerRect);\n      collisions.push({\n        id,\n        data: {\n          droppableContainer,\n          value: distBetween\n        }\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n\n/**\r\n * Returns the closest rectangles from an array of rectangles to the corners of\r\n * another rectangle.\r\n */\n\nconst closestCorners = _ref => {\n  let {\n    collisionRect,\n    droppableRects,\n    droppableContainers\n  } = _ref;\n  const corners = cornersOfRectangle(collisionRect);\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const rectCorners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner, index) => {\n        return accumulator + distanceBetween(rectCorners[index], corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n      collisions.push({\n        id,\n        data: {\n          droppableContainer,\n          value: effectiveDistance\n        }\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n\n/**\r\n * Returns the intersecting rectangle area between two rectangles\r\n */\n\nfunction getIntersectionRatio(entry, target) {\n  const top = Math.max(target.top, entry.top);\n  const left = Math.max(target.left, entry.left);\n  const right = Math.min(target.left + target.width, entry.left + entry.width);\n  const bottom = Math.min(target.top + target.height, entry.top + entry.height);\n  const width = right - left;\n  const height = bottom - top;\n\n  if (left < right && top < bottom) {\n    const targetArea = target.width * target.height;\n    const entryArea = entry.width * entry.height;\n    const intersectionArea = width * height;\n    const intersectionRatio = intersectionArea / (targetArea + entryArea - intersectionArea);\n    return Number(intersectionRatio.toFixed(4));\n  } // Rectangles do not overlap, or overlap has an area of zero (edge/corner overlap)\n\n\n  return 0;\n}\n/**\r\n * Returns the rectangles that has the greatest intersection area with a given\r\n * rectangle in an array of rectangles.\r\n */\n\nconst rectIntersection = _ref => {\n  let {\n    collisionRect,\n    droppableRects,\n    droppableContainers\n  } = _ref;\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const intersectionRatio = getIntersectionRatio(rect, collisionRect);\n\n      if (intersectionRatio > 0) {\n        collisions.push({\n          id,\n          data: {\n            droppableContainer,\n            value: intersectionRatio\n          }\n        });\n      }\n    }\n  }\n\n  return collisions.sort(sortCollisionsDesc);\n};\n\n/**\r\n * Check if a given point is contained within a bounding rectangle\r\n */\n\nfunction isPointWithinRect(point, rect) {\n  const {\n    top,\n    left,\n    bottom,\n    right\n  } = rect;\n  return top <= point.y && point.y <= bottom && left <= point.x && point.x <= right;\n}\n/**\r\n * Returns the rectangles that the pointer is hovering over\r\n */\n\n\nconst pointerWithin = _ref => {\n  let {\n    droppableContainers,\n    droppableRects,\n    pointerCoordinates\n  } = _ref;\n\n  if (!pointerCoordinates) {\n    return [];\n  }\n\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect && isPointWithinRect(pointerCoordinates, rect)) {\n      /* There may be more than a single rectangle intersecting\r\n       * with the pointer coordinates. In order to sort the\r\n       * colliding rectangles, we measure the distance between\r\n       * the pointer and the corners of the intersecting rectangle\r\n       */\n      const corners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner) => {\n        return accumulator + distanceBetween(pointerCoordinates, corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n      collisions.push({\n        id,\n        data: {\n          droppableContainer,\n          value: effectiveDistance\n        }\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n\nfunction adjustScale(transform, rect1, rect2) {\n  return { ...transform,\n    scaleX: rect1 && rect2 ? rect1.width / rect2.width : 1,\n    scaleY: rect1 && rect2 ? rect1.height / rect2.height : 1\n  };\n}\n\nfunction getRectDelta(rect1, rect2) {\n  return rect1 && rect2 ? {\n    x: rect1.left - rect2.left,\n    y: rect1.top - rect2.top\n  } : defaultCoordinates;\n}\n\nfunction createRectAdjustmentFn(modifier) {\n  return function adjustClientRect(rect) {\n    for (var _len = arguments.length, adjustments = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      adjustments[_key - 1] = arguments[_key];\n    }\n\n    return adjustments.reduce((acc, adjustment) => ({ ...acc,\n      top: acc.top + modifier * adjustment.y,\n      bottom: acc.bottom + modifier * adjustment.y,\n      left: acc.left + modifier * adjustment.x,\n      right: acc.right + modifier * adjustment.x\n    }), { ...rect\n    });\n  };\n}\nconst getAdjustedRect = /*#__PURE__*/createRectAdjustmentFn(1);\n\nfunction parseTransform(transform) {\n  if (transform.startsWith('matrix3d(')) {\n    const transformArray = transform.slice(9, -1).split(/, /);\n    return {\n      x: +transformArray[12],\n      y: +transformArray[13],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[5]\n    };\n  } else if (transform.startsWith('matrix(')) {\n    const transformArray = transform.slice(7, -1).split(/, /);\n    return {\n      x: +transformArray[4],\n      y: +transformArray[5],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[3]\n    };\n  }\n\n  return null;\n}\n\nfunction inverseTransform(rect, transform, transformOrigin) {\n  const parsedTransform = parseTransform(transform);\n\n  if (!parsedTransform) {\n    return rect;\n  }\n\n  const {\n    scaleX,\n    scaleY,\n    x: translateX,\n    y: translateY\n  } = parsedTransform;\n  const x = rect.left - translateX - (1 - scaleX) * parseFloat(transformOrigin);\n  const y = rect.top - translateY - (1 - scaleY) * parseFloat(transformOrigin.slice(transformOrigin.indexOf(' ') + 1));\n  const w = scaleX ? rect.width / scaleX : rect.width;\n  const h = scaleY ? rect.height / scaleY : rect.height;\n  return {\n    width: w,\n    height: h,\n    top: y,\n    right: x + w,\n    bottom: y + h,\n    left: x\n  };\n}\n\nconst defaultOptions = {\n  ignoreTransform: false\n};\n/**\r\n * Returns the bounding client rect of an element relative to the viewport.\r\n */\n\nfunction getClientRect(element, options) {\n  if (options === void 0) {\n    options = defaultOptions;\n  }\n\n  let rect = element.getBoundingClientRect();\n\n  if (options.ignoreTransform) {\n    const {\n      transform,\n      transformOrigin\n    } = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(element).getComputedStyle(element);\n\n    if (transform) {\n      rect = inverseTransform(rect, transform, transformOrigin);\n    }\n  }\n\n  const {\n    top,\n    left,\n    width,\n    height,\n    bottom,\n    right\n  } = rect;\n  return {\n    top,\n    left,\n    width,\n    height,\n    bottom,\n    right\n  };\n}\n/**\r\n * Returns the bounding client rect of an element relative to the viewport.\r\n *\r\n * @remarks\r\n * The ClientRect returned by this method does not take into account transforms\r\n * applied to the element it measures.\r\n *\r\n */\n\nfunction getTransformAgnosticClientRect(element) {\n  return getClientRect(element, {\n    ignoreTransform: true\n  });\n}\n\nfunction getWindowClientRect(element) {\n  const width = element.innerWidth;\n  const height = element.innerHeight;\n  return {\n    top: 0,\n    left: 0,\n    right: width,\n    bottom: height,\n    width,\n    height\n  };\n}\n\nfunction isFixed(node, computedStyle) {\n  if (computedStyle === void 0) {\n    computedStyle = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(node).getComputedStyle(node);\n  }\n\n  return computedStyle.position === 'fixed';\n}\n\nfunction isScrollable(element, computedStyle) {\n  if (computedStyle === void 0) {\n    computedStyle = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(element).getComputedStyle(element);\n  }\n\n  const overflowRegex = /(auto|scroll|overlay)/;\n  const properties = ['overflow', 'overflowX', 'overflowY'];\n  return properties.some(property => {\n    const value = computedStyle[property];\n    return typeof value === 'string' ? overflowRegex.test(value) : false;\n  });\n}\n\nfunction getScrollableAncestors(element, limit) {\n  const scrollParents = [];\n\n  function findScrollableAncestors(node) {\n    if (limit != null && scrollParents.length >= limit) {\n      return scrollParents;\n    }\n\n    if (!node) {\n      return scrollParents;\n    }\n\n    if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isDocument)(node) && node.scrollingElement != null && !scrollParents.includes(node.scrollingElement)) {\n      scrollParents.push(node.scrollingElement);\n      return scrollParents;\n    }\n\n    if (!(0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isHTMLElement)(node) || (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isSVGElement)(node)) {\n      return scrollParents;\n    }\n\n    if (scrollParents.includes(node)) {\n      return scrollParents;\n    }\n\n    const computedStyle = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(element).getComputedStyle(node);\n\n    if (node !== element) {\n      if (isScrollable(node, computedStyle)) {\n        scrollParents.push(node);\n      }\n    }\n\n    if (isFixed(node, computedStyle)) {\n      return scrollParents;\n    }\n\n    return findScrollableAncestors(node.parentNode);\n  }\n\n  if (!element) {\n    return scrollParents;\n  }\n\n  return findScrollableAncestors(element);\n}\nfunction getFirstScrollableAncestor(node) {\n  const [firstScrollableAncestor] = getScrollableAncestors(node, 1);\n  return firstScrollableAncestor != null ? firstScrollableAncestor : null;\n}\n\nfunction getScrollableElement(element) {\n  if (!_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.canUseDOM || !element) {\n    return null;\n  }\n\n  if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isWindow)(element)) {\n    return element;\n  }\n\n  if (!(0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isNode)(element)) {\n    return null;\n  }\n\n  if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isDocument)(element) || element === (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(element).scrollingElement) {\n    return window;\n  }\n\n  if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isHTMLElement)(element)) {\n    return element;\n  }\n\n  return null;\n}\n\nfunction getScrollXCoordinate(element) {\n  if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isWindow)(element)) {\n    return element.scrollX;\n  }\n\n  return element.scrollLeft;\n}\nfunction getScrollYCoordinate(element) {\n  if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isWindow)(element)) {\n    return element.scrollY;\n  }\n\n  return element.scrollTop;\n}\nfunction getScrollCoordinates(element) {\n  return {\n    x: getScrollXCoordinate(element),\n    y: getScrollYCoordinate(element)\n  };\n}\n\nvar Direction;\n\n(function (Direction) {\n  Direction[Direction[\"Forward\"] = 1] = \"Forward\";\n  Direction[Direction[\"Backward\"] = -1] = \"Backward\";\n})(Direction || (Direction = {}));\n\nfunction isDocumentScrollingElement(element) {\n  if (!_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.canUseDOM || !element) {\n    return false;\n  }\n\n  return element === document.scrollingElement;\n}\n\nfunction getScrollPosition(scrollingContainer) {\n  const minScroll = {\n    x: 0,\n    y: 0\n  };\n  const dimensions = isDocumentScrollingElement(scrollingContainer) ? {\n    height: window.innerHeight,\n    width: window.innerWidth\n  } : {\n    height: scrollingContainer.clientHeight,\n    width: scrollingContainer.clientWidth\n  };\n  const maxScroll = {\n    x: scrollingContainer.scrollWidth - dimensions.width,\n    y: scrollingContainer.scrollHeight - dimensions.height\n  };\n  const isTop = scrollingContainer.scrollTop <= minScroll.y;\n  const isLeft = scrollingContainer.scrollLeft <= minScroll.x;\n  const isBottom = scrollingContainer.scrollTop >= maxScroll.y;\n  const isRight = scrollingContainer.scrollLeft >= maxScroll.x;\n  return {\n    isTop,\n    isLeft,\n    isBottom,\n    isRight,\n    maxScroll,\n    minScroll\n  };\n}\n\nconst defaultThreshold = {\n  x: 0.2,\n  y: 0.2\n};\nfunction getScrollDirectionAndSpeed(scrollContainer, scrollContainerRect, _ref, acceleration, thresholdPercentage) {\n  let {\n    top,\n    left,\n    right,\n    bottom\n  } = _ref;\n\n  if (acceleration === void 0) {\n    acceleration = 10;\n  }\n\n  if (thresholdPercentage === void 0) {\n    thresholdPercentage = defaultThreshold;\n  }\n\n  const {\n    isTop,\n    isBottom,\n    isLeft,\n    isRight\n  } = getScrollPosition(scrollContainer);\n  const direction = {\n    x: 0,\n    y: 0\n  };\n  const speed = {\n    x: 0,\n    y: 0\n  };\n  const threshold = {\n    height: scrollContainerRect.height * thresholdPercentage.y,\n    width: scrollContainerRect.width * thresholdPercentage.x\n  };\n\n  if (!isTop && top <= scrollContainerRect.top + threshold.height) {\n    // Scroll Up\n    direction.y = Direction.Backward;\n    speed.y = acceleration * Math.abs((scrollContainerRect.top + threshold.height - top) / threshold.height);\n  } else if (!isBottom && bottom >= scrollContainerRect.bottom - threshold.height) {\n    // Scroll Down\n    direction.y = Direction.Forward;\n    speed.y = acceleration * Math.abs((scrollContainerRect.bottom - threshold.height - bottom) / threshold.height);\n  }\n\n  if (!isRight && right >= scrollContainerRect.right - threshold.width) {\n    // Scroll Right\n    direction.x = Direction.Forward;\n    speed.x = acceleration * Math.abs((scrollContainerRect.right - threshold.width - right) / threshold.width);\n  } else if (!isLeft && left <= scrollContainerRect.left + threshold.width) {\n    // Scroll Left\n    direction.x = Direction.Backward;\n    speed.x = acceleration * Math.abs((scrollContainerRect.left + threshold.width - left) / threshold.width);\n  }\n\n  return {\n    direction,\n    speed\n  };\n}\n\nfunction getScrollElementRect(element) {\n  if (element === document.scrollingElement) {\n    const {\n      innerWidth,\n      innerHeight\n    } = window;\n    return {\n      top: 0,\n      left: 0,\n      right: innerWidth,\n      bottom: innerHeight,\n      width: innerWidth,\n      height: innerHeight\n    };\n  }\n\n  const {\n    top,\n    left,\n    right,\n    bottom\n  } = element.getBoundingClientRect();\n  return {\n    top,\n    left,\n    right,\n    bottom,\n    width: element.clientWidth,\n    height: element.clientHeight\n  };\n}\n\nfunction getScrollOffsets(scrollableAncestors) {\n  return scrollableAncestors.reduce((acc, node) => {\n    return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)(acc, getScrollCoordinates(node));\n  }, defaultCoordinates);\n}\nfunction getScrollXOffset(scrollableAncestors) {\n  return scrollableAncestors.reduce((acc, node) => {\n    return acc + getScrollXCoordinate(node);\n  }, 0);\n}\nfunction getScrollYOffset(scrollableAncestors) {\n  return scrollableAncestors.reduce((acc, node) => {\n    return acc + getScrollYCoordinate(node);\n  }, 0);\n}\n\nfunction scrollIntoViewIfNeeded(element, measure) {\n  if (measure === void 0) {\n    measure = getClientRect;\n  }\n\n  if (!element) {\n    return;\n  }\n\n  const {\n    top,\n    left,\n    bottom,\n    right\n  } = measure(element);\n  const firstScrollableAncestor = getFirstScrollableAncestor(element);\n\n  if (!firstScrollableAncestor) {\n    return;\n  }\n\n  if (bottom <= 0 || right <= 0 || top >= window.innerHeight || left >= window.innerWidth) {\n    element.scrollIntoView({\n      block: 'center',\n      inline: 'center'\n    });\n  }\n}\n\nconst properties = [['x', ['left', 'right'], getScrollXOffset], ['y', ['top', 'bottom'], getScrollYOffset]];\nclass Rect {\n  constructor(rect, element) {\n    this.rect = void 0;\n    this.width = void 0;\n    this.height = void 0;\n    this.top = void 0;\n    this.bottom = void 0;\n    this.right = void 0;\n    this.left = void 0;\n    const scrollableAncestors = getScrollableAncestors(element);\n    const scrollOffsets = getScrollOffsets(scrollableAncestors);\n    this.rect = { ...rect\n    };\n    this.width = rect.width;\n    this.height = rect.height;\n\n    for (const [axis, keys, getScrollOffset] of properties) {\n      for (const key of keys) {\n        Object.defineProperty(this, key, {\n          get: () => {\n            const currentOffsets = getScrollOffset(scrollableAncestors);\n            const scrollOffsetsDeltla = scrollOffsets[axis] - currentOffsets;\n            return this.rect[key] + scrollOffsetsDeltla;\n          },\n          enumerable: true\n        });\n      }\n    }\n\n    Object.defineProperty(this, 'rect', {\n      enumerable: false\n    });\n  }\n\n}\n\nclass Listeners {\n  constructor(target) {\n    this.target = void 0;\n    this.listeners = [];\n\n    this.removeAll = () => {\n      this.listeners.forEach(listener => {\n        var _this$target;\n\n        return (_this$target = this.target) == null ? void 0 : _this$target.removeEventListener(...listener);\n      });\n    };\n\n    this.target = target;\n  }\n\n  add(eventName, handler, options) {\n    var _this$target2;\n\n    (_this$target2 = this.target) == null ? void 0 : _this$target2.addEventListener(eventName, handler, options);\n    this.listeners.push([eventName, handler, options]);\n  }\n\n}\n\nfunction getEventListenerTarget(target) {\n  // If the `event.target` element is removed from the document events will still be targeted\n  // at it, and hence won't always bubble up to the window or document anymore.\n  // If there is any risk of an element being removed while it is being dragged,\n  // the best practice is to attach the event listeners directly to the target.\n  // https://developer.mozilla.org/en-US/docs/Web/API/EventTarget\n  const {\n    EventTarget\n  } = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(target);\n  return target instanceof EventTarget ? target : (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(target);\n}\n\nfunction hasExceededDistance(delta, measurement) {\n  const dx = Math.abs(delta.x);\n  const dy = Math.abs(delta.y);\n\n  if (typeof measurement === 'number') {\n    return Math.sqrt(dx ** 2 + dy ** 2) > measurement;\n  }\n\n  if ('x' in measurement && 'y' in measurement) {\n    return dx > measurement.x && dy > measurement.y;\n  }\n\n  if ('x' in measurement) {\n    return dx > measurement.x;\n  }\n\n  if ('y' in measurement) {\n    return dy > measurement.y;\n  }\n\n  return false;\n}\n\nvar EventName;\n\n(function (EventName) {\n  EventName[\"Click\"] = \"click\";\n  EventName[\"DragStart\"] = \"dragstart\";\n  EventName[\"Keydown\"] = \"keydown\";\n  EventName[\"ContextMenu\"] = \"contextmenu\";\n  EventName[\"Resize\"] = \"resize\";\n  EventName[\"SelectionChange\"] = \"selectionchange\";\n  EventName[\"VisibilityChange\"] = \"visibilitychange\";\n})(EventName || (EventName = {}));\n\nfunction preventDefault(event) {\n  event.preventDefault();\n}\nfunction stopPropagation(event) {\n  event.stopPropagation();\n}\n\nvar KeyboardCode;\n\n(function (KeyboardCode) {\n  KeyboardCode[\"Space\"] = \"Space\";\n  KeyboardCode[\"Down\"] = \"ArrowDown\";\n  KeyboardCode[\"Right\"] = \"ArrowRight\";\n  KeyboardCode[\"Left\"] = \"ArrowLeft\";\n  KeyboardCode[\"Up\"] = \"ArrowUp\";\n  KeyboardCode[\"Esc\"] = \"Escape\";\n  KeyboardCode[\"Enter\"] = \"Enter\";\n})(KeyboardCode || (KeyboardCode = {}));\n\nconst defaultKeyboardCodes = {\n  start: [KeyboardCode.Space, KeyboardCode.Enter],\n  cancel: [KeyboardCode.Esc],\n  end: [KeyboardCode.Space, KeyboardCode.Enter]\n};\nconst defaultKeyboardCoordinateGetter = (event, _ref) => {\n  let {\n    currentCoordinates\n  } = _ref;\n\n  switch (event.code) {\n    case KeyboardCode.Right:\n      return { ...currentCoordinates,\n        x: currentCoordinates.x + 25\n      };\n\n    case KeyboardCode.Left:\n      return { ...currentCoordinates,\n        x: currentCoordinates.x - 25\n      };\n\n    case KeyboardCode.Down:\n      return { ...currentCoordinates,\n        y: currentCoordinates.y + 25\n      };\n\n    case KeyboardCode.Up:\n      return { ...currentCoordinates,\n        y: currentCoordinates.y - 25\n      };\n  }\n\n  return undefined;\n};\n\nclass KeyboardSensor {\n  constructor(props) {\n    this.props = void 0;\n    this.autoScrollEnabled = false;\n    this.referenceCoordinates = void 0;\n    this.listeners = void 0;\n    this.windowListeners = void 0;\n    this.props = props;\n    const {\n      event: {\n        target\n      }\n    } = props;\n    this.props = props;\n    this.listeners = new Listeners((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(target));\n    this.windowListeners = new Listeners((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(target));\n    this.handleKeyDown = this.handleKeyDown.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n    this.attach();\n  }\n\n  attach() {\n    this.handleStart();\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n    setTimeout(() => this.listeners.add(EventName.Keydown, this.handleKeyDown));\n  }\n\n  handleStart() {\n    const {\n      activeNode,\n      onStart\n    } = this.props;\n    const node = activeNode.node.current;\n\n    if (node) {\n      scrollIntoViewIfNeeded(node);\n    }\n\n    onStart(defaultCoordinates);\n  }\n\n  handleKeyDown(event) {\n    if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isKeyboardEvent)(event)) {\n      const {\n        active,\n        context,\n        options\n      } = this.props;\n      const {\n        keyboardCodes = defaultKeyboardCodes,\n        coordinateGetter = defaultKeyboardCoordinateGetter,\n        scrollBehavior = 'smooth'\n      } = options;\n      const {\n        code\n      } = event;\n\n      if (keyboardCodes.end.includes(code)) {\n        this.handleEnd(event);\n        return;\n      }\n\n      if (keyboardCodes.cancel.includes(code)) {\n        this.handleCancel(event);\n        return;\n      }\n\n      const {\n        collisionRect\n      } = context.current;\n      const currentCoordinates = collisionRect ? {\n        x: collisionRect.left,\n        y: collisionRect.top\n      } : defaultCoordinates;\n\n      if (!this.referenceCoordinates) {\n        this.referenceCoordinates = currentCoordinates;\n      }\n\n      const newCoordinates = coordinateGetter(event, {\n        active,\n        context: context.current,\n        currentCoordinates\n      });\n\n      if (newCoordinates) {\n        const coordinatesDelta = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.subtract)(newCoordinates, currentCoordinates);\n        const scrollDelta = {\n          x: 0,\n          y: 0\n        };\n        const {\n          scrollableAncestors\n        } = context.current;\n\n        for (const scrollContainer of scrollableAncestors) {\n          const direction = event.code;\n          const {\n            isTop,\n            isRight,\n            isLeft,\n            isBottom,\n            maxScroll,\n            minScroll\n          } = getScrollPosition(scrollContainer);\n          const scrollElementRect = getScrollElementRect(scrollContainer);\n          const clampedCoordinates = {\n            x: Math.min(direction === KeyboardCode.Right ? scrollElementRect.right - scrollElementRect.width / 2 : scrollElementRect.right, Math.max(direction === KeyboardCode.Right ? scrollElementRect.left : scrollElementRect.left + scrollElementRect.width / 2, newCoordinates.x)),\n            y: Math.min(direction === KeyboardCode.Down ? scrollElementRect.bottom - scrollElementRect.height / 2 : scrollElementRect.bottom, Math.max(direction === KeyboardCode.Down ? scrollElementRect.top : scrollElementRect.top + scrollElementRect.height / 2, newCoordinates.y))\n          };\n          const canScrollX = direction === KeyboardCode.Right && !isRight || direction === KeyboardCode.Left && !isLeft;\n          const canScrollY = direction === KeyboardCode.Down && !isBottom || direction === KeyboardCode.Up && !isTop;\n\n          if (canScrollX && clampedCoordinates.x !== newCoordinates.x) {\n            const newScrollCoordinates = scrollContainer.scrollLeft + coordinatesDelta.x;\n            const canScrollToNewCoordinates = direction === KeyboardCode.Right && newScrollCoordinates <= maxScroll.x || direction === KeyboardCode.Left && newScrollCoordinates >= minScroll.x;\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.y) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                left: newScrollCoordinates,\n                behavior: scrollBehavior\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.x = scrollContainer.scrollLeft - newScrollCoordinates;\n            } else {\n              scrollDelta.x = direction === KeyboardCode.Right ? scrollContainer.scrollLeft - maxScroll.x : scrollContainer.scrollLeft - minScroll.x;\n            }\n\n            if (scrollDelta.x) {\n              scrollContainer.scrollBy({\n                left: -scrollDelta.x,\n                behavior: scrollBehavior\n              });\n            }\n\n            break;\n          } else if (canScrollY && clampedCoordinates.y !== newCoordinates.y) {\n            const newScrollCoordinates = scrollContainer.scrollTop + coordinatesDelta.y;\n            const canScrollToNewCoordinates = direction === KeyboardCode.Down && newScrollCoordinates <= maxScroll.y || direction === KeyboardCode.Up && newScrollCoordinates >= minScroll.y;\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.x) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                top: newScrollCoordinates,\n                behavior: scrollBehavior\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.y = scrollContainer.scrollTop - newScrollCoordinates;\n            } else {\n              scrollDelta.y = direction === KeyboardCode.Down ? scrollContainer.scrollTop - maxScroll.y : scrollContainer.scrollTop - minScroll.y;\n            }\n\n            if (scrollDelta.y) {\n              scrollContainer.scrollBy({\n                top: -scrollDelta.y,\n                behavior: scrollBehavior\n              });\n            }\n\n            break;\n          }\n        }\n\n        this.handleMove(event, (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.subtract)(newCoordinates, this.referenceCoordinates), scrollDelta));\n      }\n    }\n  }\n\n  handleMove(event, coordinates) {\n    const {\n      onMove\n    } = this.props;\n    event.preventDefault();\n    onMove(coordinates);\n  }\n\n  handleEnd(event) {\n    const {\n      onEnd\n    } = this.props;\n    event.preventDefault();\n    this.detach();\n    onEnd();\n  }\n\n  handleCancel(event) {\n    const {\n      onCancel\n    } = this.props;\n    event.preventDefault();\n    this.detach();\n    onCancel();\n  }\n\n  detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll();\n  }\n\n}\nKeyboardSensor.activators = [{\n  eventName: 'onKeyDown',\n  handler: (event, _ref, _ref2) => {\n    let {\n      keyboardCodes = defaultKeyboardCodes,\n      onActivation\n    } = _ref;\n    let {\n      active\n    } = _ref2;\n    const {\n      code\n    } = event.nativeEvent;\n\n    if (keyboardCodes.start.includes(code)) {\n      const activator = active.activatorNode.current;\n\n      if (activator && event.target !== activator) {\n        return false;\n      }\n\n      event.preventDefault();\n      onActivation == null ? void 0 : onActivation({\n        event: event.nativeEvent\n      });\n      return true;\n    }\n\n    return false;\n  }\n}];\n\nfunction isDistanceConstraint(constraint) {\n  return Boolean(constraint && 'distance' in constraint);\n}\n\nfunction isDelayConstraint(constraint) {\n  return Boolean(constraint && 'delay' in constraint);\n}\n\nclass AbstractPointerSensor {\n  constructor(props, events, listenerTarget) {\n    var _getEventCoordinates;\n\n    if (listenerTarget === void 0) {\n      listenerTarget = getEventListenerTarget(props.event.target);\n    }\n\n    this.props = void 0;\n    this.events = void 0;\n    this.autoScrollEnabled = true;\n    this.document = void 0;\n    this.activated = false;\n    this.initialCoordinates = void 0;\n    this.timeoutId = null;\n    this.listeners = void 0;\n    this.documentListeners = void 0;\n    this.windowListeners = void 0;\n    this.props = props;\n    this.events = events;\n    const {\n      event\n    } = props;\n    const {\n      target\n    } = event;\n    this.props = props;\n    this.events = events;\n    this.document = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(target);\n    this.documentListeners = new Listeners(this.document);\n    this.listeners = new Listeners(listenerTarget);\n    this.windowListeners = new Listeners((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(target));\n    this.initialCoordinates = (_getEventCoordinates = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getEventCoordinates)(event)) != null ? _getEventCoordinates : defaultCoordinates;\n    this.handleStart = this.handleStart.bind(this);\n    this.handleMove = this.handleMove.bind(this);\n    this.handleEnd = this.handleEnd.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n    this.handleKeydown = this.handleKeydown.bind(this);\n    this.removeTextSelection = this.removeTextSelection.bind(this);\n    this.attach();\n  }\n\n  attach() {\n    const {\n      events,\n      props: {\n        options: {\n          activationConstraint\n        }\n      }\n    } = this;\n    this.listeners.add(events.move.name, this.handleMove, {\n      passive: false\n    });\n    this.listeners.add(events.end.name, this.handleEnd);\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.DragStart, preventDefault);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n    this.windowListeners.add(EventName.ContextMenu, preventDefault);\n    this.documentListeners.add(EventName.Keydown, this.handleKeydown);\n\n    if (activationConstraint) {\n      if (isDistanceConstraint(activationConstraint)) {\n        return;\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        this.timeoutId = setTimeout(this.handleStart, activationConstraint.delay);\n        return;\n      }\n    }\n\n    this.handleStart();\n  }\n\n  detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll(); // Wait until the next event loop before removing document listeners\n    // This is necessary because we listen for `click` and `selection` events on the document\n\n    setTimeout(this.documentListeners.removeAll, 50);\n\n    if (this.timeoutId !== null) {\n      clearTimeout(this.timeoutId);\n      this.timeoutId = null;\n    }\n  }\n\n  handleStart() {\n    const {\n      initialCoordinates\n    } = this;\n    const {\n      onStart\n    } = this.props;\n\n    if (initialCoordinates) {\n      this.activated = true; // Stop propagation of click events once activation constraints are met\n\n      this.documentListeners.add(EventName.Click, stopPropagation, {\n        capture: true\n      }); // Remove any text selection from the document\n\n      this.removeTextSelection(); // Prevent further text selection while dragging\n\n      this.documentListeners.add(EventName.SelectionChange, this.removeTextSelection);\n      onStart(initialCoordinates);\n    }\n  }\n\n  handleMove(event) {\n    var _getEventCoordinates2;\n\n    const {\n      activated,\n      initialCoordinates,\n      props\n    } = this;\n    const {\n      onMove,\n      options: {\n        activationConstraint\n      }\n    } = props;\n\n    if (!initialCoordinates) {\n      return;\n    }\n\n    const coordinates = (_getEventCoordinates2 = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getEventCoordinates)(event)) != null ? _getEventCoordinates2 : defaultCoordinates;\n    const delta = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.subtract)(initialCoordinates, coordinates);\n\n    if (!activated && activationConstraint) {\n      // Constraint validation\n      if (isDelayConstraint(activationConstraint)) {\n        if (hasExceededDistance(delta, activationConstraint.tolerance)) {\n          return this.handleCancel();\n        }\n\n        return;\n      }\n\n      if (isDistanceConstraint(activationConstraint)) {\n        if (activationConstraint.tolerance != null && hasExceededDistance(delta, activationConstraint.tolerance)) {\n          return this.handleCancel();\n        }\n\n        if (hasExceededDistance(delta, activationConstraint.distance)) {\n          return this.handleStart();\n        }\n\n        return;\n      }\n    }\n\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n\n    onMove(coordinates);\n  }\n\n  handleEnd() {\n    const {\n      onEnd\n    } = this.props;\n    this.detach();\n    onEnd();\n  }\n\n  handleCancel() {\n    const {\n      onCancel\n    } = this.props;\n    this.detach();\n    onCancel();\n  }\n\n  handleKeydown(event) {\n    if (event.code === KeyboardCode.Esc) {\n      this.handleCancel();\n    }\n  }\n\n  removeTextSelection() {\n    var _this$document$getSel;\n\n    (_this$document$getSel = this.document.getSelection()) == null ? void 0 : _this$document$getSel.removeAllRanges();\n  }\n\n}\n\nconst events = {\n  move: {\n    name: 'pointermove'\n  },\n  end: {\n    name: 'pointerup'\n  }\n};\nclass PointerSensor extends AbstractPointerSensor {\n  constructor(props) {\n    const {\n      event\n    } = props; // Pointer events stop firing if the target is unmounted while dragging\n    // Therefore we attach listeners to the owner document instead\n\n    const listenerTarget = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(event.target);\n    super(props, events, listenerTarget);\n  }\n\n}\nPointerSensor.activators = [{\n  eventName: 'onPointerDown',\n  handler: (_ref, _ref2) => {\n    let {\n      nativeEvent: event\n    } = _ref;\n    let {\n      onActivation\n    } = _ref2;\n\n    if (!event.isPrimary || event.button !== 0) {\n      return false;\n    }\n\n    onActivation == null ? void 0 : onActivation({\n      event\n    });\n    return true;\n  }\n}];\n\nconst events$1 = {\n  move: {\n    name: 'mousemove'\n  },\n  end: {\n    name: 'mouseup'\n  }\n};\nvar MouseButton;\n\n(function (MouseButton) {\n  MouseButton[MouseButton[\"RightClick\"] = 2] = \"RightClick\";\n})(MouseButton || (MouseButton = {}));\n\nclass MouseSensor extends AbstractPointerSensor {\n  constructor(props) {\n    super(props, events$1, (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(props.event.target));\n  }\n\n}\nMouseSensor.activators = [{\n  eventName: 'onMouseDown',\n  handler: (_ref, _ref2) => {\n    let {\n      nativeEvent: event\n    } = _ref;\n    let {\n      onActivation\n    } = _ref2;\n\n    if (event.button === MouseButton.RightClick) {\n      return false;\n    }\n\n    onActivation == null ? void 0 : onActivation({\n      event\n    });\n    return true;\n  }\n}];\n\nconst events$2 = {\n  move: {\n    name: 'touchmove'\n  },\n  end: {\n    name: 'touchend'\n  }\n};\nclass TouchSensor extends AbstractPointerSensor {\n  constructor(props) {\n    super(props, events$2);\n  }\n\n  static setup() {\n    // Adding a non-capture and non-passive `touchmove` listener in order\n    // to force `event.preventDefault()` calls to work in dynamically added\n    // touchmove event handlers. This is required for iOS Safari.\n    window.addEventListener(events$2.move.name, noop, {\n      capture: false,\n      passive: false\n    });\n    return function teardown() {\n      window.removeEventListener(events$2.move.name, noop);\n    }; // We create a new handler because the teardown function of another sensor\n    // could remove our event listener if we use a referentially equal listener.\n\n    function noop() {}\n  }\n\n}\nTouchSensor.activators = [{\n  eventName: 'onTouchStart',\n  handler: (_ref, _ref2) => {\n    let {\n      nativeEvent: event\n    } = _ref;\n    let {\n      onActivation\n    } = _ref2;\n    const {\n      touches\n    } = event;\n\n    if (touches.length > 1) {\n      return false;\n    }\n\n    onActivation == null ? void 0 : onActivation({\n      event\n    });\n    return true;\n  }\n}];\n\nvar AutoScrollActivator;\n\n(function (AutoScrollActivator) {\n  AutoScrollActivator[AutoScrollActivator[\"Pointer\"] = 0] = \"Pointer\";\n  AutoScrollActivator[AutoScrollActivator[\"DraggableRect\"] = 1] = \"DraggableRect\";\n})(AutoScrollActivator || (AutoScrollActivator = {}));\n\nvar TraversalOrder;\n\n(function (TraversalOrder) {\n  TraversalOrder[TraversalOrder[\"TreeOrder\"] = 0] = \"TreeOrder\";\n  TraversalOrder[TraversalOrder[\"ReversedTreeOrder\"] = 1] = \"ReversedTreeOrder\";\n})(TraversalOrder || (TraversalOrder = {}));\n\nfunction useAutoScroller(_ref) {\n  let {\n    acceleration,\n    activator = AutoScrollActivator.Pointer,\n    canScroll,\n    draggingRect,\n    enabled,\n    interval = 5,\n    order = TraversalOrder.TreeOrder,\n    pointerCoordinates,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    delta,\n    threshold\n  } = _ref;\n  const scrollIntent = useScrollIntent({\n    delta,\n    disabled: !enabled\n  });\n  const [setAutoScrollInterval, clearAutoScrollInterval] = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useInterval)();\n  const scrollSpeed = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    x: 0,\n    y: 0\n  });\n  const scrollDirection = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    x: 0,\n    y: 0\n  });\n  const rect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    switch (activator) {\n      case AutoScrollActivator.Pointer:\n        return pointerCoordinates ? {\n          top: pointerCoordinates.y,\n          bottom: pointerCoordinates.y,\n          left: pointerCoordinates.x,\n          right: pointerCoordinates.x\n        } : null;\n\n      case AutoScrollActivator.DraggableRect:\n        return draggingRect;\n    }\n  }, [activator, draggingRect, pointerCoordinates]);\n  const scrollContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const autoScroll = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    const scrollContainer = scrollContainerRef.current;\n\n    if (!scrollContainer) {\n      return;\n    }\n\n    const scrollLeft = scrollSpeed.current.x * scrollDirection.current.x;\n    const scrollTop = scrollSpeed.current.y * scrollDirection.current.y;\n    scrollContainer.scrollBy(scrollLeft, scrollTop);\n  }, []);\n  const sortedScrollableAncestors = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => order === TraversalOrder.TreeOrder ? [...scrollableAncestors].reverse() : scrollableAncestors, [order, scrollableAncestors]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!enabled || !scrollableAncestors.length || !rect) {\n      clearAutoScrollInterval();\n      return;\n    }\n\n    for (const scrollContainer of sortedScrollableAncestors) {\n      if ((canScroll == null ? void 0 : canScroll(scrollContainer)) === false) {\n        continue;\n      }\n\n      const index = scrollableAncestors.indexOf(scrollContainer);\n      const scrollContainerRect = scrollableAncestorRects[index];\n\n      if (!scrollContainerRect) {\n        continue;\n      }\n\n      const {\n        direction,\n        speed\n      } = getScrollDirectionAndSpeed(scrollContainer, scrollContainerRect, rect, acceleration, threshold);\n\n      for (const axis of ['x', 'y']) {\n        if (!scrollIntent[axis][direction[axis]]) {\n          speed[axis] = 0;\n          direction[axis] = 0;\n        }\n      }\n\n      if (speed.x > 0 || speed.y > 0) {\n        clearAutoScrollInterval();\n        scrollContainerRef.current = scrollContainer;\n        setAutoScrollInterval(autoScroll, interval);\n        scrollSpeed.current = speed;\n        scrollDirection.current = direction;\n        return;\n      }\n    }\n\n    scrollSpeed.current = {\n      x: 0,\n      y: 0\n    };\n    scrollDirection.current = {\n      x: 0,\n      y: 0\n    };\n    clearAutoScrollInterval();\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [acceleration, autoScroll, canScroll, clearAutoScrollInterval, enabled, interval, // eslint-disable-next-line react-hooks/exhaustive-deps\n  JSON.stringify(rect), // eslint-disable-next-line react-hooks/exhaustive-deps\n  JSON.stringify(scrollIntent), setAutoScrollInterval, scrollableAncestors, sortedScrollableAncestors, scrollableAncestorRects, // eslint-disable-next-line react-hooks/exhaustive-deps\n  JSON.stringify(threshold)]);\n}\nconst defaultScrollIntent = {\n  x: {\n    [Direction.Backward]: false,\n    [Direction.Forward]: false\n  },\n  y: {\n    [Direction.Backward]: false,\n    [Direction.Forward]: false\n  }\n};\n\nfunction useScrollIntent(_ref2) {\n  let {\n    delta,\n    disabled\n  } = _ref2;\n  const previousDelta = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.usePrevious)(delta);\n  return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLazyMemo)(previousIntent => {\n    if (disabled || !previousDelta || !previousIntent) {\n      // Reset scroll intent tracking when auto-scrolling is disabled\n      return defaultScrollIntent;\n    }\n\n    const direction = {\n      x: Math.sign(delta.x - previousDelta.x),\n      y: Math.sign(delta.y - previousDelta.y)\n    }; // Keep track of the user intent to scroll in each direction for both axis\n\n    return {\n      x: {\n        [Direction.Backward]: previousIntent.x[Direction.Backward] || direction.x === -1,\n        [Direction.Forward]: previousIntent.x[Direction.Forward] || direction.x === 1\n      },\n      y: {\n        [Direction.Backward]: previousIntent.y[Direction.Backward] || direction.y === -1,\n        [Direction.Forward]: previousIntent.y[Direction.Forward] || direction.y === 1\n      }\n    };\n  }, [disabled, delta, previousDelta]);\n}\n\nfunction useCachedNode(draggableNodes, id) {\n  const draggableNode = id !== null ? draggableNodes.get(id) : undefined;\n  const node = draggableNode ? draggableNode.node.current : null;\n  return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLazyMemo)(cachedNode => {\n    var _ref;\n\n    if (id === null) {\n      return null;\n    } // In some cases, the draggable node can unmount while dragging\n    // This is the case for virtualized lists. In those situations,\n    // we fall back to the last known value for that node.\n\n\n    return (_ref = node != null ? node : cachedNode) != null ? _ref : null;\n  }, [node, id]);\n}\n\nfunction useCombineActivators(sensors, getSyntheticHandler) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => sensors.reduce((accumulator, sensor) => {\n    const {\n      sensor: Sensor\n    } = sensor;\n    const sensorActivators = Sensor.activators.map(activator => ({\n      eventName: activator.eventName,\n      handler: getSyntheticHandler(activator.handler, sensor)\n    }));\n    return [...accumulator, ...sensorActivators];\n  }, []), [sensors, getSyntheticHandler]);\n}\n\nvar MeasuringStrategy;\n\n(function (MeasuringStrategy) {\n  MeasuringStrategy[MeasuringStrategy[\"Always\"] = 0] = \"Always\";\n  MeasuringStrategy[MeasuringStrategy[\"BeforeDragging\"] = 1] = \"BeforeDragging\";\n  MeasuringStrategy[MeasuringStrategy[\"WhileDragging\"] = 2] = \"WhileDragging\";\n})(MeasuringStrategy || (MeasuringStrategy = {}));\n\nvar MeasuringFrequency;\n\n(function (MeasuringFrequency) {\n  MeasuringFrequency[\"Optimized\"] = \"optimized\";\n})(MeasuringFrequency || (MeasuringFrequency = {}));\n\nconst defaultValue = /*#__PURE__*/new Map();\nfunction useDroppableMeasuring(containers, _ref) {\n  let {\n    dragging,\n    dependencies,\n    config\n  } = _ref;\n  const [queue, setQueue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const {\n    frequency,\n    measure,\n    strategy\n  } = config;\n  const containersRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(containers);\n  const disabled = isDisabled();\n  const disabledRef = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLatestValue)(disabled);\n  const measureDroppableContainers = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (ids) {\n    if (ids === void 0) {\n      ids = [];\n    }\n\n    if (disabledRef.current) {\n      return;\n    }\n\n    setQueue(value => {\n      if (value === null) {\n        return ids;\n      }\n\n      return value.concat(ids.filter(id => !value.includes(id)));\n    });\n  }, [disabledRef]);\n  const timeoutId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const droppableRects = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLazyMemo)(previousValue => {\n    if (disabled && !dragging) {\n      return defaultValue;\n    }\n\n    if (!previousValue || previousValue === defaultValue || containersRef.current !== containers || queue != null) {\n      const map = new Map();\n\n      for (let container of containers) {\n        if (!container) {\n          continue;\n        }\n\n        if (queue && queue.length > 0 && !queue.includes(container.id) && container.rect.current) {\n          // This container does not need to be re-measured\n          map.set(container.id, container.rect.current);\n          continue;\n        }\n\n        const node = container.node.current;\n        const rect = node ? new Rect(measure(node), node) : null;\n        container.rect.current = rect;\n\n        if (rect) {\n          map.set(container.id, rect);\n        }\n      }\n\n      return map;\n    }\n\n    return previousValue;\n  }, [containers, queue, dragging, disabled, measure]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    containersRef.current = containers;\n  }, [containers]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled) {\n      return;\n    }\n\n    measureDroppableContainers();\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [dragging, disabled]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (queue && queue.length > 0) {\n      setQueue(null);\n    }\n  }, //eslint-disable-next-line react-hooks/exhaustive-deps\n  [JSON.stringify(queue)]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled || typeof frequency !== 'number' || timeoutId.current !== null) {\n      return;\n    }\n\n    timeoutId.current = setTimeout(() => {\n      measureDroppableContainers();\n      timeoutId.current = null;\n    }, frequency);\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [frequency, disabled, measureDroppableContainers, ...dependencies]);\n  return {\n    droppableRects,\n    measureDroppableContainers,\n    measuringScheduled: queue != null\n  };\n\n  function isDisabled() {\n    switch (strategy) {\n      case MeasuringStrategy.Always:\n        return false;\n\n      case MeasuringStrategy.BeforeDragging:\n        return dragging;\n\n      default:\n        return !dragging;\n    }\n  }\n}\n\nfunction useInitialValue(value, computeFn) {\n  return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLazyMemo)(previousValue => {\n    if (!value) {\n      return null;\n    }\n\n    if (previousValue) {\n      return previousValue;\n    }\n\n    return typeof computeFn === 'function' ? computeFn(value) : value;\n  }, [computeFn, value]);\n}\n\nfunction useInitialRect(node, measure) {\n  return useInitialValue(node, measure);\n}\n\n/**\r\n * Returns a new MutationObserver instance.\r\n * If `MutationObserver` is undefined in the execution environment, returns `undefined`.\r\n */\n\nfunction useMutationObserver(_ref) {\n  let {\n    callback,\n    disabled\n  } = _ref;\n  const handleMutations = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useEvent)(callback);\n  const mutationObserver = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (disabled || typeof window === 'undefined' || typeof window.MutationObserver === 'undefined') {\n      return undefined;\n    }\n\n    const {\n      MutationObserver\n    } = window;\n    return new MutationObserver(handleMutations);\n  }, [handleMutations, disabled]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    return () => mutationObserver == null ? void 0 : mutationObserver.disconnect();\n  }, [mutationObserver]);\n  return mutationObserver;\n}\n\n/**\r\n * Returns a new ResizeObserver instance bound to the `onResize` callback.\r\n * If `ResizeObserver` is undefined in the execution environment, returns `undefined`.\r\n */\n\nfunction useResizeObserver(_ref) {\n  let {\n    callback,\n    disabled\n  } = _ref;\n  const handleResize = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useEvent)(callback);\n  const resizeObserver = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (disabled || typeof window === 'undefined' || typeof window.ResizeObserver === 'undefined') {\n      return undefined;\n    }\n\n    const {\n      ResizeObserver\n    } = window;\n    return new ResizeObserver(handleResize);\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [disabled]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    return () => resizeObserver == null ? void 0 : resizeObserver.disconnect();\n  }, [resizeObserver]);\n  return resizeObserver;\n}\n\nfunction defaultMeasure(element) {\n  return new Rect(getClientRect(element), element);\n}\n\nfunction useRect(element, measure, fallbackRect) {\n  if (measure === void 0) {\n    measure = defaultMeasure;\n  }\n\n  const [rect, measureRect] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)(reducer, null);\n  const mutationObserver = useMutationObserver({\n    callback(records) {\n      if (!element) {\n        return;\n      }\n\n      for (const record of records) {\n        const {\n          type,\n          target\n        } = record;\n\n        if (type === 'childList' && target instanceof HTMLElement && target.contains(element)) {\n          measureRect();\n          break;\n        }\n      }\n    }\n\n  });\n  const resizeObserver = useResizeObserver({\n    callback: measureRect\n  });\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    measureRect();\n\n    if (element) {\n      resizeObserver == null ? void 0 : resizeObserver.observe(element);\n      mutationObserver == null ? void 0 : mutationObserver.observe(document.body, {\n        childList: true,\n        subtree: true\n      });\n    } else {\n      resizeObserver == null ? void 0 : resizeObserver.disconnect();\n      mutationObserver == null ? void 0 : mutationObserver.disconnect();\n    }\n  }, [element]);\n  return rect;\n\n  function reducer(currentRect) {\n    if (!element) {\n      return null;\n    }\n\n    if (element.isConnected === false) {\n      var _ref;\n\n      // Fall back to last rect we measured if the element is\n      // no longer connected to the DOM.\n      return (_ref = currentRect != null ? currentRect : fallbackRect) != null ? _ref : null;\n    }\n\n    const newRect = measure(element);\n\n    if (JSON.stringify(currentRect) === JSON.stringify(newRect)) {\n      return currentRect;\n    }\n\n    return newRect;\n  }\n}\n\nfunction useRectDelta(rect) {\n  const initialRect = useInitialValue(rect);\n  return getRectDelta(rect, initialRect);\n}\n\nconst defaultValue$1 = [];\nfunction useScrollableAncestors(node) {\n  const previousNode = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(node);\n  const ancestors = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLazyMemo)(previousValue => {\n    if (!node) {\n      return defaultValue$1;\n    }\n\n    if (previousValue && previousValue !== defaultValue$1 && node && previousNode.current && node.parentNode === previousNode.current.parentNode) {\n      return previousValue;\n    }\n\n    return getScrollableAncestors(node);\n  }, [node]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    previousNode.current = node;\n  }, [node]);\n  return ancestors;\n}\n\nfunction useScrollOffsets(elements) {\n  const [scrollCoordinates, setScrollCoordinates] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const prevElements = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(elements); // To-do: Throttle the handleScroll callback\n\n  const handleScroll = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(event => {\n    const scrollingElement = getScrollableElement(event.target);\n\n    if (!scrollingElement) {\n      return;\n    }\n\n    setScrollCoordinates(scrollCoordinates => {\n      if (!scrollCoordinates) {\n        return null;\n      }\n\n      scrollCoordinates.set(scrollingElement, getScrollCoordinates(scrollingElement));\n      return new Map(scrollCoordinates);\n    });\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const previousElements = prevElements.current;\n\n    if (elements !== previousElements) {\n      cleanup(previousElements);\n      const entries = elements.map(element => {\n        const scrollableElement = getScrollableElement(element);\n\n        if (scrollableElement) {\n          scrollableElement.addEventListener('scroll', handleScroll, {\n            passive: true\n          });\n          return [scrollableElement, getScrollCoordinates(scrollableElement)];\n        }\n\n        return null;\n      }).filter(entry => entry != null);\n      setScrollCoordinates(entries.length ? new Map(entries) : null);\n      prevElements.current = elements;\n    }\n\n    return () => {\n      cleanup(elements);\n      cleanup(previousElements);\n    };\n\n    function cleanup(elements) {\n      elements.forEach(element => {\n        const scrollableElement = getScrollableElement(element);\n        scrollableElement == null ? void 0 : scrollableElement.removeEventListener('scroll', handleScroll);\n      });\n    }\n  }, [handleScroll, elements]);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (elements.length) {\n      return scrollCoordinates ? Array.from(scrollCoordinates.values()).reduce((acc, coordinates) => (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)(acc, coordinates), defaultCoordinates) : getScrollOffsets(elements);\n    }\n\n    return defaultCoordinates;\n  }, [elements, scrollCoordinates]);\n}\n\nfunction useScrollOffsetsDelta(scrollOffsets, dependencies) {\n  if (dependencies === void 0) {\n    dependencies = [];\n  }\n\n  const initialScrollOffsets = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    initialScrollOffsets.current = null;\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  dependencies);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const hasScrollOffsets = scrollOffsets !== defaultCoordinates;\n\n    if (hasScrollOffsets && !initialScrollOffsets.current) {\n      initialScrollOffsets.current = scrollOffsets;\n    }\n\n    if (!hasScrollOffsets && initialScrollOffsets.current) {\n      initialScrollOffsets.current = null;\n    }\n  }, [scrollOffsets]);\n  return initialScrollOffsets.current ? (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.subtract)(scrollOffsets, initialScrollOffsets.current) : defaultCoordinates;\n}\n\nfunction useSensorSetup(sensors) {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.canUseDOM) {\n      return;\n    }\n\n    const teardownFns = sensors.map(_ref => {\n      let {\n        sensor\n      } = _ref;\n      return sensor.setup == null ? void 0 : sensor.setup();\n    });\n    return () => {\n      for (const teardown of teardownFns) {\n        teardown == null ? void 0 : teardown();\n      }\n    };\n  }, // TO-DO: Sensors length could theoretically change which would not be a valid dependency\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  sensors.map(_ref2 => {\n    let {\n      sensor\n    } = _ref2;\n    return sensor;\n  }));\n}\n\nfunction useSyntheticListeners(listeners, id) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    return listeners.reduce((acc, _ref) => {\n      let {\n        eventName,\n        handler\n      } = _ref;\n\n      acc[eventName] = event => {\n        handler(event, id);\n      };\n\n      return acc;\n    }, {});\n  }, [listeners, id]);\n}\n\nfunction useWindowRect(element) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => element ? getWindowClientRect(element) : null, [element]);\n}\n\nconst defaultValue$2 = [];\nfunction useRects(elements, measure) {\n  if (measure === void 0) {\n    measure = getClientRect;\n  }\n\n  const [firstElement] = elements;\n  const windowRect = useWindowRect(firstElement ? (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(firstElement) : null);\n  const [rects, measureRects] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)(reducer, defaultValue$2);\n  const resizeObserver = useResizeObserver({\n    callback: measureRects\n  });\n\n  if (elements.length > 0 && rects === defaultValue$2) {\n    measureRects();\n  }\n\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    if (elements.length) {\n      elements.forEach(element => resizeObserver == null ? void 0 : resizeObserver.observe(element));\n    } else {\n      resizeObserver == null ? void 0 : resizeObserver.disconnect();\n      measureRects();\n    }\n  }, [elements]);\n  return rects;\n\n  function reducer() {\n    if (!elements.length) {\n      return defaultValue$2;\n    }\n\n    return elements.map(element => isDocumentScrollingElement(element) ? windowRect : new Rect(measure(element), element));\n  }\n}\n\nfunction getMeasurableNode(node) {\n  if (!node) {\n    return null;\n  }\n\n  if (node.children.length > 1) {\n    return node;\n  }\n\n  const firstChild = node.children[0];\n  return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isHTMLElement)(firstChild) ? firstChild : node;\n}\n\nfunction useDragOverlayMeasuring(_ref) {\n  let {\n    measure\n  } = _ref;\n  const [rect, setRect] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const handleResize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(entries => {\n    for (const {\n      target\n    } of entries) {\n      if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isHTMLElement)(target)) {\n        setRect(rect => {\n          const newRect = measure(target);\n          return rect ? { ...rect,\n            width: newRect.width,\n            height: newRect.height\n          } : newRect;\n        });\n        break;\n      }\n    }\n  }, [measure]);\n  const resizeObserver = useResizeObserver({\n    callback: handleResize\n  });\n  const handleNodeChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(element => {\n    const node = getMeasurableNode(element);\n    resizeObserver == null ? void 0 : resizeObserver.disconnect();\n\n    if (node) {\n      resizeObserver == null ? void 0 : resizeObserver.observe(node);\n    }\n\n    setRect(node ? measure(node) : null);\n  }, [measure, resizeObserver]);\n  const [nodeRef, setRef] = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useNodeRef)(handleNodeChange);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    nodeRef,\n    rect,\n    setRef\n  }), [rect, nodeRef, setRef]);\n}\n\nconst defaultSensors = [{\n  sensor: PointerSensor,\n  options: {}\n}, {\n  sensor: KeyboardSensor,\n  options: {}\n}];\nconst defaultData = {\n  current: {}\n};\nconst defaultMeasuringConfiguration = {\n  draggable: {\n    measure: getTransformAgnosticClientRect\n  },\n  droppable: {\n    measure: getTransformAgnosticClientRect,\n    strategy: MeasuringStrategy.WhileDragging,\n    frequency: MeasuringFrequency.Optimized\n  },\n  dragOverlay: {\n    measure: getClientRect\n  }\n};\n\nclass DroppableContainersMap extends Map {\n  get(id) {\n    var _super$get;\n\n    return id != null ? (_super$get = super.get(id)) != null ? _super$get : undefined : undefined;\n  }\n\n  toArray() {\n    return Array.from(this.values());\n  }\n\n  getEnabled() {\n    return this.toArray().filter(_ref => {\n      let {\n        disabled\n      } = _ref;\n      return !disabled;\n    });\n  }\n\n  getNodeFor(id) {\n    var _this$get$node$curren, _this$get;\n\n    return (_this$get$node$curren = (_this$get = this.get(id)) == null ? void 0 : _this$get.node.current) != null ? _this$get$node$curren : undefined;\n  }\n\n}\n\nconst defaultPublicContext = {\n  activatorEvent: null,\n  active: null,\n  activeNode: null,\n  activeNodeRect: null,\n  collisions: null,\n  containerNodeRect: null,\n  draggableNodes: /*#__PURE__*/new Map(),\n  droppableRects: /*#__PURE__*/new Map(),\n  droppableContainers: /*#__PURE__*/new DroppableContainersMap(),\n  over: null,\n  dragOverlay: {\n    nodeRef: {\n      current: null\n    },\n    rect: null,\n    setRef: noop\n  },\n  scrollableAncestors: [],\n  scrollableAncestorRects: [],\n  measuringConfiguration: defaultMeasuringConfiguration,\n  measureDroppableContainers: noop,\n  windowRect: null,\n  measuringScheduled: false\n};\nconst defaultInternalContext = {\n  activatorEvent: null,\n  activators: [],\n  active: null,\n  activeNodeRect: null,\n  ariaDescribedById: {\n    draggable: ''\n  },\n  dispatch: noop,\n  draggableNodes: /*#__PURE__*/new Map(),\n  over: null,\n  measureDroppableContainers: noop\n};\nconst InternalContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(defaultInternalContext);\nconst PublicContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(defaultPublicContext);\n\nfunction getInitialState() {\n  return {\n    draggable: {\n      active: null,\n      initialCoordinates: {\n        x: 0,\n        y: 0\n      },\n      nodes: new Map(),\n      translate: {\n        x: 0,\n        y: 0\n      }\n    },\n    droppable: {\n      containers: new DroppableContainersMap()\n    }\n  };\n}\nfunction reducer(state, action) {\n  switch (action.type) {\n    case Action.DragStart:\n      return { ...state,\n        draggable: { ...state.draggable,\n          initialCoordinates: action.initialCoordinates,\n          active: action.active\n        }\n      };\n\n    case Action.DragMove:\n      if (!state.draggable.active) {\n        return state;\n      }\n\n      return { ...state,\n        draggable: { ...state.draggable,\n          translate: {\n            x: action.coordinates.x - state.draggable.initialCoordinates.x,\n            y: action.coordinates.y - state.draggable.initialCoordinates.y\n          }\n        }\n      };\n\n    case Action.DragEnd:\n    case Action.DragCancel:\n      return { ...state,\n        draggable: { ...state.draggable,\n          active: null,\n          initialCoordinates: {\n            x: 0,\n            y: 0\n          },\n          translate: {\n            x: 0,\n            y: 0\n          }\n        }\n      };\n\n    case Action.RegisterDroppable:\n      {\n        const {\n          element\n        } = action;\n        const {\n          id\n        } = element;\n        const containers = new DroppableContainersMap(state.droppable.containers);\n        containers.set(id, element);\n        return { ...state,\n          droppable: { ...state.droppable,\n            containers\n          }\n        };\n      }\n\n    case Action.SetDroppableDisabled:\n      {\n        const {\n          id,\n          key,\n          disabled\n        } = action;\n        const element = state.droppable.containers.get(id);\n\n        if (!element || key !== element.key) {\n          return state;\n        }\n\n        const containers = new DroppableContainersMap(state.droppable.containers);\n        containers.set(id, { ...element,\n          disabled\n        });\n        return { ...state,\n          droppable: { ...state.droppable,\n            containers\n          }\n        };\n      }\n\n    case Action.UnregisterDroppable:\n      {\n        const {\n          id,\n          key\n        } = action;\n        const element = state.droppable.containers.get(id);\n\n        if (!element || key !== element.key) {\n          return state;\n        }\n\n        const containers = new DroppableContainersMap(state.droppable.containers);\n        containers.delete(id);\n        return { ...state,\n          droppable: { ...state.droppable,\n            containers\n          }\n        };\n      }\n\n    default:\n      {\n        return state;\n      }\n  }\n}\n\nfunction RestoreFocus(_ref) {\n  let {\n    disabled\n  } = _ref;\n  const {\n    active,\n    activatorEvent,\n    draggableNodes\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(InternalContext);\n  const previousActivatorEvent = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.usePrevious)(activatorEvent);\n  const previousActiveId = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.usePrevious)(active == null ? void 0 : active.id); // Restore keyboard focus on the activator node\n\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled) {\n      return;\n    }\n\n    if (!activatorEvent && previousActivatorEvent && previousActiveId != null) {\n      if (!(0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isKeyboardEvent)(previousActivatorEvent)) {\n        return;\n      }\n\n      if (document.activeElement === previousActivatorEvent.target) {\n        // No need to restore focus\n        return;\n      }\n\n      const draggableNode = draggableNodes.get(previousActiveId);\n\n      if (!draggableNode) {\n        return;\n      }\n\n      const {\n        activatorNode,\n        node\n      } = draggableNode;\n\n      if (!activatorNode.current && !node.current) {\n        return;\n      }\n\n      requestAnimationFrame(() => {\n        for (const element of [activatorNode.current, node.current]) {\n          if (!element) {\n            continue;\n          }\n\n          const focusableNode = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.findFirstFocusableNode)(element);\n\n          if (focusableNode) {\n            focusableNode.focus();\n            break;\n          }\n        }\n      });\n    }\n  }, [activatorEvent, disabled, draggableNodes, previousActiveId, previousActivatorEvent]);\n  return null;\n}\n\nfunction applyModifiers(modifiers, _ref) {\n  let {\n    transform,\n    ...args\n  } = _ref;\n  return modifiers != null && modifiers.length ? modifiers.reduce((accumulator, modifier) => {\n    return modifier({\n      transform: accumulator,\n      ...args\n    });\n  }, transform) : transform;\n}\n\nfunction useMeasuringConfiguration(config) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    draggable: { ...defaultMeasuringConfiguration.draggable,\n      ...(config == null ? void 0 : config.draggable)\n    },\n    droppable: { ...defaultMeasuringConfiguration.droppable,\n      ...(config == null ? void 0 : config.droppable)\n    },\n    dragOverlay: { ...defaultMeasuringConfiguration.dragOverlay,\n      ...(config == null ? void 0 : config.dragOverlay)\n    }\n  }), // eslint-disable-next-line react-hooks/exhaustive-deps\n  [config == null ? void 0 : config.draggable, config == null ? void 0 : config.droppable, config == null ? void 0 : config.dragOverlay]);\n}\n\nfunction useLayoutShiftScrollCompensation(_ref) {\n  let {\n    activeNode,\n    measure,\n    initialRect,\n    config = true\n  } = _ref;\n  const initialized = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const {\n    x,\n    y\n  } = typeof config === 'boolean' ? {\n    x: config,\n    y: config\n  } : config;\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    const disabled = !x && !y;\n\n    if (disabled || !activeNode) {\n      initialized.current = false;\n      return;\n    }\n\n    if (initialized.current || !initialRect) {\n      // Return early if layout shift scroll compensation was already attempted\n      // or if there is no initialRect to compare to.\n      return;\n    } // Get the most up to date node ref for the active draggable\n\n\n    const node = activeNode == null ? void 0 : activeNode.node.current;\n\n    if (!node || node.isConnected === false) {\n      // Return early if there is no attached node ref or if the node is\n      // disconnected from the document.\n      return;\n    }\n\n    const rect = measure(node);\n    const rectDelta = getRectDelta(rect, initialRect);\n\n    if (!x) {\n      rectDelta.x = 0;\n    }\n\n    if (!y) {\n      rectDelta.y = 0;\n    } // Only perform layout shift scroll compensation once\n\n\n    initialized.current = true;\n\n    if (Math.abs(rectDelta.x) > 0 || Math.abs(rectDelta.y) > 0) {\n      const firstScrollableAncestor = getFirstScrollableAncestor(node);\n\n      if (firstScrollableAncestor) {\n        firstScrollableAncestor.scrollBy({\n          top: rectDelta.y,\n          left: rectDelta.x\n        });\n      }\n    }\n  }, [activeNode, x, y, initialRect, measure]);\n}\n\nconst ActiveDraggableContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({ ...defaultCoordinates,\n  scaleX: 1,\n  scaleY: 1\n});\nvar Status;\n\n(function (Status) {\n  Status[Status[\"Uninitialized\"] = 0] = \"Uninitialized\";\n  Status[Status[\"Initializing\"] = 1] = \"Initializing\";\n  Status[Status[\"Initialized\"] = 2] = \"Initialized\";\n})(Status || (Status = {}));\n\nconst DndContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(function DndContext(_ref) {\n  var _sensorContext$curren, _dragOverlay$nodeRef$, _dragOverlay$rect, _over$rect;\n\n  let {\n    id,\n    accessibility,\n    autoScroll = true,\n    children,\n    sensors = defaultSensors,\n    collisionDetection = rectIntersection,\n    measuring,\n    modifiers,\n    ...props\n  } = _ref;\n  const store = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)(reducer, undefined, getInitialState);\n  const [state, dispatch] = store;\n  const [dispatchMonitorEvent, registerMonitorListener] = useDndMonitorProvider();\n  const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(Status.Uninitialized);\n  const isInitialized = status === Status.Initialized;\n  const {\n    draggable: {\n      active: activeId,\n      nodes: draggableNodes,\n      translate\n    },\n    droppable: {\n      containers: droppableContainers\n    }\n  } = state;\n  const node = activeId ? draggableNodes.get(activeId) : null;\n  const activeRects = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    initial: null,\n    translated: null\n  });\n  const active = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    var _node$data;\n\n    return activeId != null ? {\n      id: activeId,\n      // It's possible for the active node to unmount while dragging\n      data: (_node$data = node == null ? void 0 : node.data) != null ? _node$data : defaultData,\n      rect: activeRects\n    } : null;\n  }, [activeId, node]);\n  const activeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const [activeSensor, setActiveSensor] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [activatorEvent, setActivatorEvent] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const latestProps = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLatestValue)(props, Object.values(props));\n  const draggableDescribedById = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useUniqueId)(\"DndDescribedBy\", id);\n  const enabledDroppableContainers = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => droppableContainers.getEnabled(), [droppableContainers]);\n  const measuringConfiguration = useMeasuringConfiguration(measuring);\n  const {\n    droppableRects,\n    measureDroppableContainers,\n    measuringScheduled\n  } = useDroppableMeasuring(enabledDroppableContainers, {\n    dragging: isInitialized,\n    dependencies: [translate.x, translate.y],\n    config: measuringConfiguration.droppable\n  });\n  const activeNode = useCachedNode(draggableNodes, activeId);\n  const activationCoordinates = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => activatorEvent ? (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getEventCoordinates)(activatorEvent) : null, [activatorEvent]);\n  const autoScrollOptions = getAutoScrollerOptions();\n  const initialActiveNodeRect = useInitialRect(activeNode, measuringConfiguration.draggable.measure);\n  useLayoutShiftScrollCompensation({\n    activeNode: activeId ? draggableNodes.get(activeId) : null,\n    config: autoScrollOptions.layoutShiftCompensation,\n    initialRect: initialActiveNodeRect,\n    measure: measuringConfiguration.draggable.measure\n  });\n  const activeNodeRect = useRect(activeNode, measuringConfiguration.draggable.measure, initialActiveNodeRect);\n  const containerNodeRect = useRect(activeNode ? activeNode.parentElement : null);\n  const sensorContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    activatorEvent: null,\n    active: null,\n    activeNode,\n    collisionRect: null,\n    collisions: null,\n    droppableRects,\n    draggableNodes,\n    draggingNode: null,\n    draggingNodeRect: null,\n    droppableContainers,\n    over: null,\n    scrollableAncestors: [],\n    scrollAdjustedTranslate: null\n  });\n  const overNode = droppableContainers.getNodeFor((_sensorContext$curren = sensorContext.current.over) == null ? void 0 : _sensorContext$curren.id);\n  const dragOverlay = useDragOverlayMeasuring({\n    measure: measuringConfiguration.dragOverlay.measure\n  }); // Use the rect of the drag overlay if it is mounted\n\n  const draggingNode = (_dragOverlay$nodeRef$ = dragOverlay.nodeRef.current) != null ? _dragOverlay$nodeRef$ : activeNode;\n  const draggingNodeRect = isInitialized ? (_dragOverlay$rect = dragOverlay.rect) != null ? _dragOverlay$rect : activeNodeRect : null;\n  const usesDragOverlay = Boolean(dragOverlay.nodeRef.current && dragOverlay.rect); // The delta between the previous and new position of the draggable node\n  // is only relevant when there is no drag overlay\n\n  const nodeRectDelta = useRectDelta(usesDragOverlay ? null : activeNodeRect); // Get the window rect of the dragging node\n\n  const windowRect = useWindowRect(draggingNode ? (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(draggingNode) : null); // Get scrollable ancestors of the dragging node\n\n  const scrollableAncestors = useScrollableAncestors(isInitialized ? overNode != null ? overNode : activeNode : null);\n  const scrollableAncestorRects = useRects(scrollableAncestors); // Apply modifiers\n\n  const modifiedTranslate = applyModifiers(modifiers, {\n    transform: {\n      x: translate.x - nodeRectDelta.x,\n      y: translate.y - nodeRectDelta.y,\n      scaleX: 1,\n      scaleY: 1\n    },\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggingNodeRect,\n    over: sensorContext.current.over,\n    overlayNodeRect: dragOverlay.rect,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    windowRect\n  });\n  const pointerCoordinates = activationCoordinates ? (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)(activationCoordinates, translate) : null;\n  const scrollOffsets = useScrollOffsets(scrollableAncestors); // Represents the scroll delta since dragging was initiated\n\n  const scrollAdjustment = useScrollOffsetsDelta(scrollOffsets); // Represents the scroll delta since the last time the active node rect was measured\n\n  const activeNodeScrollDelta = useScrollOffsetsDelta(scrollOffsets, [activeNodeRect]);\n  const scrollAdjustedTranslate = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)(modifiedTranslate, scrollAdjustment);\n  const collisionRect = draggingNodeRect ? getAdjustedRect(draggingNodeRect, modifiedTranslate) : null;\n  const collisions = active && collisionRect ? collisionDetection({\n    active,\n    collisionRect,\n    droppableRects,\n    droppableContainers: enabledDroppableContainers,\n    pointerCoordinates\n  }) : null;\n  const overId = getFirstCollision(collisions, 'id');\n  const [over, setOver] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null); // When there is no drag overlay used, we need to account for the\n  // window scroll delta\n\n  const appliedTranslate = usesDragOverlay ? modifiedTranslate : (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)(modifiedTranslate, activeNodeScrollDelta);\n  const transform = adjustScale(appliedTranslate, (_over$rect = over == null ? void 0 : over.rect) != null ? _over$rect : null, activeNodeRect);\n  const instantiateSensor = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((event, _ref2) => {\n    let {\n      sensor: Sensor,\n      options\n    } = _ref2;\n\n    if (activeRef.current == null) {\n      return;\n    }\n\n    const activeNode = draggableNodes.get(activeRef.current);\n\n    if (!activeNode) {\n      return;\n    }\n\n    const activatorEvent = event.nativeEvent;\n    const sensorInstance = new Sensor({\n      active: activeRef.current,\n      activeNode,\n      event: activatorEvent,\n      options,\n      // Sensors need to be instantiated with refs for arguments that change over time\n      // otherwise they are frozen in time with the stale arguments\n      context: sensorContext,\n\n      onStart(initialCoordinates) {\n        const id = activeRef.current;\n\n        if (id == null) {\n          return;\n        }\n\n        const draggableNode = draggableNodes.get(id);\n\n        if (!draggableNode) {\n          return;\n        }\n\n        const {\n          onDragStart\n        } = latestProps.current;\n        const event = {\n          active: {\n            id,\n            data: draggableNode.data,\n            rect: activeRects\n          }\n        };\n        (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.unstable_batchedUpdates)(() => {\n          onDragStart == null ? void 0 : onDragStart(event);\n          setStatus(Status.Initializing);\n          dispatch({\n            type: Action.DragStart,\n            initialCoordinates,\n            active: id\n          });\n          dispatchMonitorEvent({\n            type: 'onDragStart',\n            event\n          });\n        });\n      },\n\n      onMove(coordinates) {\n        dispatch({\n          type: Action.DragMove,\n          coordinates\n        });\n      },\n\n      onEnd: createHandler(Action.DragEnd),\n      onCancel: createHandler(Action.DragCancel)\n    });\n    (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.unstable_batchedUpdates)(() => {\n      setActiveSensor(sensorInstance);\n      setActivatorEvent(event.nativeEvent);\n    });\n\n    function createHandler(type) {\n      return async function handler() {\n        const {\n          active,\n          collisions,\n          over,\n          scrollAdjustedTranslate\n        } = sensorContext.current;\n        let event = null;\n\n        if (active && scrollAdjustedTranslate) {\n          const {\n            cancelDrop\n          } = latestProps.current;\n          event = {\n            activatorEvent,\n            active: active,\n            collisions,\n            delta: scrollAdjustedTranslate,\n            over\n          };\n\n          if (type === Action.DragEnd && typeof cancelDrop === 'function') {\n            const shouldCancel = await Promise.resolve(cancelDrop(event));\n\n            if (shouldCancel) {\n              type = Action.DragCancel;\n            }\n          }\n        }\n\n        activeRef.current = null;\n        (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.unstable_batchedUpdates)(() => {\n          dispatch({\n            type\n          });\n          setStatus(Status.Uninitialized);\n          setOver(null);\n          setActiveSensor(null);\n          setActivatorEvent(null);\n          const eventName = type === Action.DragEnd ? 'onDragEnd' : 'onDragCancel';\n\n          if (event) {\n            const handler = latestProps.current[eventName];\n            handler == null ? void 0 : handler(event);\n            dispatchMonitorEvent({\n              type: eventName,\n              event\n            });\n          }\n        });\n      };\n    }\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [draggableNodes]);\n  const bindActivatorToSensorInstantiator = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((handler, sensor) => {\n    return (event, active) => {\n      const nativeEvent = event.nativeEvent;\n      const activeDraggableNode = draggableNodes.get(active);\n\n      if ( // Another sensor is already instantiating\n      activeRef.current !== null || // No active draggable\n      !activeDraggableNode || // Event has already been captured\n      nativeEvent.dndKit || nativeEvent.defaultPrevented) {\n        return;\n      }\n\n      const activationContext = {\n        active: activeDraggableNode\n      };\n      const shouldActivate = handler(event, sensor.options, activationContext);\n\n      if (shouldActivate === true) {\n        nativeEvent.dndKit = {\n          capturedBy: sensor.sensor\n        };\n        activeRef.current = active;\n        instantiateSensor(event, sensor);\n      }\n    };\n  }, [draggableNodes, instantiateSensor]);\n  const activators = useCombineActivators(sensors, bindActivatorToSensorInstantiator);\n  useSensorSetup(sensors);\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    if (activeNodeRect && status === Status.Initializing) {\n      setStatus(Status.Initialized);\n    }\n  }, [activeNodeRect, status]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const {\n      onDragMove\n    } = latestProps.current;\n    const {\n      active,\n      activatorEvent,\n      collisions,\n      over\n    } = sensorContext.current;\n\n    if (!active || !activatorEvent) {\n      return;\n    }\n\n    const event = {\n      active,\n      activatorEvent,\n      collisions,\n      delta: {\n        x: scrollAdjustedTranslate.x,\n        y: scrollAdjustedTranslate.y\n      },\n      over\n    };\n    (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.unstable_batchedUpdates)(() => {\n      onDragMove == null ? void 0 : onDragMove(event);\n      dispatchMonitorEvent({\n        type: 'onDragMove',\n        event\n      });\n    });\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [scrollAdjustedTranslate.x, scrollAdjustedTranslate.y]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const {\n      active,\n      activatorEvent,\n      collisions,\n      droppableContainers,\n      scrollAdjustedTranslate\n    } = sensorContext.current;\n\n    if (!active || activeRef.current == null || !activatorEvent || !scrollAdjustedTranslate) {\n      return;\n    }\n\n    const {\n      onDragOver\n    } = latestProps.current;\n    const overContainer = droppableContainers.get(overId);\n    const over = overContainer && overContainer.rect.current ? {\n      id: overContainer.id,\n      rect: overContainer.rect.current,\n      data: overContainer.data,\n      disabled: overContainer.disabled\n    } : null;\n    const event = {\n      active,\n      activatorEvent,\n      collisions,\n      delta: {\n        x: scrollAdjustedTranslate.x,\n        y: scrollAdjustedTranslate.y\n      },\n      over\n    };\n    (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.unstable_batchedUpdates)(() => {\n      setOver(over);\n      onDragOver == null ? void 0 : onDragOver(event);\n      dispatchMonitorEvent({\n        type: 'onDragOver',\n        event\n      });\n    });\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [overId]);\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    sensorContext.current = {\n      activatorEvent,\n      active,\n      activeNode,\n      collisionRect,\n      collisions,\n      droppableRects,\n      draggableNodes,\n      draggingNode,\n      draggingNodeRect,\n      droppableContainers,\n      over,\n      scrollableAncestors,\n      scrollAdjustedTranslate\n    };\n    activeRects.current = {\n      initial: draggingNodeRect,\n      translated: collisionRect\n    };\n  }, [active, activeNode, collisions, collisionRect, draggableNodes, draggingNode, draggingNodeRect, droppableRects, droppableContainers, over, scrollableAncestors, scrollAdjustedTranslate]);\n  useAutoScroller({ ...autoScrollOptions,\n    delta: translate,\n    draggingRect: collisionRect,\n    pointerCoordinates,\n    scrollableAncestors,\n    scrollableAncestorRects\n  });\n  const publicContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const context = {\n      active,\n      activeNode,\n      activeNodeRect,\n      activatorEvent,\n      collisions,\n      containerNodeRect,\n      dragOverlay,\n      draggableNodes,\n      droppableContainers,\n      droppableRects,\n      over,\n      measureDroppableContainers,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      measuringConfiguration,\n      measuringScheduled,\n      windowRect\n    };\n    return context;\n  }, [active, activeNode, activeNodeRect, activatorEvent, collisions, containerNodeRect, dragOverlay, draggableNodes, droppableContainers, droppableRects, over, measureDroppableContainers, scrollableAncestors, scrollableAncestorRects, measuringConfiguration, measuringScheduled, windowRect]);\n  const internalContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const context = {\n      activatorEvent,\n      activators,\n      active,\n      activeNodeRect,\n      ariaDescribedById: {\n        draggable: draggableDescribedById\n      },\n      dispatch,\n      draggableNodes,\n      over,\n      measureDroppableContainers\n    };\n    return context;\n  }, [activatorEvent, activators, active, activeNodeRect, dispatch, draggableDescribedById, draggableNodes, over, measureDroppableContainers]);\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(DndMonitorContext.Provider, {\n    value: registerMonitorListener\n  }, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(InternalContext.Provider, {\n    value: internalContext\n  }, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(PublicContext.Provider, {\n    value: publicContext\n  }, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(ActiveDraggableContext.Provider, {\n    value: transform\n  }, children)), react__WEBPACK_IMPORTED_MODULE_0___default().createElement(RestoreFocus, {\n    disabled: (accessibility == null ? void 0 : accessibility.restoreFocus) === false\n  })), react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Accessibility, { ...accessibility,\n    hiddenTextDescribedById: draggableDescribedById\n  }));\n\n  function getAutoScrollerOptions() {\n    const activeSensorDisablesAutoscroll = (activeSensor == null ? void 0 : activeSensor.autoScrollEnabled) === false;\n    const autoScrollGloballyDisabled = typeof autoScroll === 'object' ? autoScroll.enabled === false : autoScroll === false;\n    const enabled = isInitialized && !activeSensorDisablesAutoscroll && !autoScrollGloballyDisabled;\n\n    if (typeof autoScroll === 'object') {\n      return { ...autoScroll,\n        enabled\n      };\n    }\n\n    return {\n      enabled\n    };\n  }\n});\n\nconst NullContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\nconst defaultRole = 'button';\nconst ID_PREFIX = 'Droppable';\nfunction useDraggable(_ref) {\n  let {\n    id,\n    data,\n    disabled = false,\n    attributes\n  } = _ref;\n  const key = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useUniqueId)(ID_PREFIX);\n  const {\n    activators,\n    activatorEvent,\n    active,\n    activeNodeRect,\n    ariaDescribedById,\n    draggableNodes,\n    over\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(InternalContext);\n  const {\n    role = defaultRole,\n    roleDescription = 'draggable',\n    tabIndex = 0\n  } = attributes != null ? attributes : {};\n  const isDragging = (active == null ? void 0 : active.id) === id;\n  const transform = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(isDragging ? ActiveDraggableContext : NullContext);\n  const [node, setNodeRef] = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useNodeRef)();\n  const [activatorNode, setActivatorNodeRef] = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useNodeRef)();\n  const listeners = useSyntheticListeners(activators, id);\n  const dataRef = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLatestValue)(data);\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    draggableNodes.set(id, {\n      id,\n      key,\n      node,\n      activatorNode,\n      data: dataRef\n    });\n    return () => {\n      const node = draggableNodes.get(id);\n\n      if (node && node.key === key) {\n        draggableNodes.delete(id);\n      }\n    };\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [draggableNodes, id]);\n  const memoizedAttributes = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    role,\n    tabIndex,\n    'aria-disabled': disabled,\n    'aria-pressed': isDragging && role === defaultRole ? true : undefined,\n    'aria-roledescription': roleDescription,\n    'aria-describedby': ariaDescribedById.draggable\n  }), [disabled, role, tabIndex, isDragging, roleDescription, ariaDescribedById.draggable]);\n  return {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes: memoizedAttributes,\n    isDragging,\n    listeners: disabled ? undefined : listeners,\n    node,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    transform\n  };\n}\n\nfunction useDndContext() {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(PublicContext);\n}\n\nconst ID_PREFIX$1 = 'Droppable';\nconst defaultResizeObserverConfig = {\n  timeout: 25\n};\nfunction useDroppable(_ref) {\n  let {\n    data,\n    disabled = false,\n    id,\n    resizeObserverConfig\n  } = _ref;\n  const key = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useUniqueId)(ID_PREFIX$1);\n  const {\n    active,\n    dispatch,\n    over,\n    measureDroppableContainers\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(InternalContext);\n  const previous = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    disabled\n  });\n  const resizeObserverConnected = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const rect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const callbackId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const {\n    disabled: resizeObserverDisabled,\n    updateMeasurementsFor,\n    timeout: resizeObserverTimeout\n  } = { ...defaultResizeObserverConfig,\n    ...resizeObserverConfig\n  };\n  const ids = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLatestValue)(updateMeasurementsFor != null ? updateMeasurementsFor : id);\n  const handleResize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (!resizeObserverConnected.current) {\n      // ResizeObserver invokes the `handleResize` callback as soon as `observe` is called,\n      // assuming the element is rendered and displayed.\n      resizeObserverConnected.current = true;\n      return;\n    }\n\n    if (callbackId.current != null) {\n      clearTimeout(callbackId.current);\n    }\n\n    callbackId.current = setTimeout(() => {\n      measureDroppableContainers(Array.isArray(ids.current) ? ids.current : [ids.current]);\n      callbackId.current = null;\n    }, resizeObserverTimeout);\n  }, //eslint-disable-next-line react-hooks/exhaustive-deps\n  [resizeObserverTimeout]);\n  const resizeObserver = useResizeObserver({\n    callback: handleResize,\n    disabled: resizeObserverDisabled || !active\n  });\n  const handleNodeChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((newElement, previousElement) => {\n    if (!resizeObserver) {\n      return;\n    }\n\n    if (previousElement) {\n      resizeObserver.unobserve(previousElement);\n      resizeObserverConnected.current = false;\n    }\n\n    if (newElement) {\n      resizeObserver.observe(newElement);\n    }\n  }, [resizeObserver]);\n  const [nodeRef, setNodeRef] = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useNodeRef)(handleNodeChange);\n  const dataRef = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLatestValue)(data);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!resizeObserver || !nodeRef.current) {\n      return;\n    }\n\n    resizeObserver.disconnect();\n    resizeObserverConnected.current = false;\n    resizeObserver.observe(nodeRef.current);\n  }, [nodeRef, resizeObserver]);\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    dispatch({\n      type: Action.RegisterDroppable,\n      element: {\n        id,\n        key,\n        disabled,\n        node: nodeRef,\n        rect,\n        data: dataRef\n      }\n    });\n    return () => dispatch({\n      type: Action.UnregisterDroppable,\n      key,\n      id\n    });\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [id]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled !== previous.current.disabled) {\n      dispatch({\n        type: Action.SetDroppableDisabled,\n        id,\n        key,\n        disabled\n      });\n      previous.current.disabled = disabled;\n    }\n  }, [id, key, disabled, dispatch]);\n  return {\n    active,\n    rect,\n    isOver: (over == null ? void 0 : over.id) === id,\n    node: nodeRef,\n    over,\n    setNodeRef\n  };\n}\n\nfunction AnimationManager(_ref) {\n  let {\n    animation,\n    children\n  } = _ref;\n  const [clonedChildren, setClonedChildren] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [element, setElement] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const previousChildren = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.usePrevious)(children);\n\n  if (!children && !clonedChildren && previousChildren) {\n    setClonedChildren(previousChildren);\n  }\n\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    if (!element) {\n      return;\n    }\n\n    const key = clonedChildren == null ? void 0 : clonedChildren.key;\n    const id = clonedChildren == null ? void 0 : clonedChildren.props.id;\n\n    if (key == null || id == null) {\n      setClonedChildren(null);\n      return;\n    }\n\n    Promise.resolve(animation(id, element)).then(() => {\n      setClonedChildren(null);\n    });\n  }, [animation, clonedChildren, element]);\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, children, clonedChildren ? (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(clonedChildren, {\n    ref: setElement\n  }) : null);\n}\n\nconst defaultTransform = {\n  x: 0,\n  y: 0,\n  scaleX: 1,\n  scaleY: 1\n};\nfunction NullifiedContextProvider(_ref) {\n  let {\n    children\n  } = _ref;\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(InternalContext.Provider, {\n    value: defaultInternalContext\n  }, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(ActiveDraggableContext.Provider, {\n    value: defaultTransform\n  }, children));\n}\n\nconst baseStyles = {\n  position: 'fixed',\n  touchAction: 'none'\n};\n\nconst defaultTransition = activatorEvent => {\n  const isKeyboardActivator = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isKeyboardEvent)(activatorEvent);\n  return isKeyboardActivator ? 'transform 250ms ease' : undefined;\n};\n\nconst PositionedOverlay = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((_ref, ref) => {\n  let {\n    as,\n    activatorEvent,\n    adjustScale,\n    children,\n    className,\n    rect,\n    style,\n    transform,\n    transition = defaultTransition\n  } = _ref;\n\n  if (!rect) {\n    return null;\n  }\n\n  const scaleAdjustedTransform = adjustScale ? transform : { ...transform,\n    scaleX: 1,\n    scaleY: 1\n  };\n  const styles = { ...baseStyles,\n    width: rect.width,\n    height: rect.height,\n    top: rect.top,\n    left: rect.left,\n    transform: _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.CSS.Transform.toString(scaleAdjustedTransform),\n    transformOrigin: adjustScale && activatorEvent ? getRelativeTransformOrigin(activatorEvent, rect) : undefined,\n    transition: typeof transition === 'function' ? transition(activatorEvent) : transition,\n    ...style\n  };\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(as, {\n    className,\n    style: styles,\n    ref\n  }, children);\n});\n\nconst defaultDropAnimationSideEffects = options => _ref => {\n  let {\n    active,\n    dragOverlay\n  } = _ref;\n  const originalStyles = {};\n  const {\n    styles,\n    className\n  } = options;\n\n  if (styles != null && styles.active) {\n    for (const [key, value] of Object.entries(styles.active)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      originalStyles[key] = active.node.style.getPropertyValue(key);\n      active.node.style.setProperty(key, value);\n    }\n  }\n\n  if (styles != null && styles.dragOverlay) {\n    for (const [key, value] of Object.entries(styles.dragOverlay)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      dragOverlay.node.style.setProperty(key, value);\n    }\n  }\n\n  if (className != null && className.active) {\n    active.node.classList.add(className.active);\n  }\n\n  if (className != null && className.dragOverlay) {\n    dragOverlay.node.classList.add(className.dragOverlay);\n  }\n\n  return function cleanup() {\n    for (const [key, value] of Object.entries(originalStyles)) {\n      active.node.style.setProperty(key, value);\n    }\n\n    if (className != null && className.active) {\n      active.node.classList.remove(className.active);\n    }\n  };\n};\n\nconst defaultKeyframeResolver = _ref2 => {\n  let {\n    transform: {\n      initial,\n      final\n    }\n  } = _ref2;\n  return [{\n    transform: _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.CSS.Transform.toString(initial)\n  }, {\n    transform: _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.CSS.Transform.toString(final)\n  }];\n};\n\nconst defaultDropAnimationConfiguration = {\n  duration: 250,\n  easing: 'ease',\n  keyframes: defaultKeyframeResolver,\n  sideEffects: /*#__PURE__*/defaultDropAnimationSideEffects({\n    styles: {\n      active: {\n        opacity: '0'\n      }\n    }\n  })\n};\nfunction useDropAnimation(_ref3) {\n  let {\n    config,\n    draggableNodes,\n    droppableContainers,\n    measuringConfiguration\n  } = _ref3;\n  return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useEvent)((id, node) => {\n    if (config === null) {\n      return;\n    }\n\n    const activeDraggable = draggableNodes.get(id);\n\n    if (!activeDraggable) {\n      return;\n    }\n\n    const activeNode = activeDraggable.node.current;\n\n    if (!activeNode) {\n      return;\n    }\n\n    const measurableNode = getMeasurableNode(node);\n\n    if (!measurableNode) {\n      return;\n    }\n\n    const {\n      transform\n    } = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(node).getComputedStyle(node);\n    const parsedTransform = parseTransform(transform);\n\n    if (!parsedTransform) {\n      return;\n    }\n\n    const animation = typeof config === 'function' ? config : createDefaultDropAnimation(config);\n    scrollIntoViewIfNeeded(activeNode, measuringConfiguration.draggable.measure);\n    return animation({\n      active: {\n        id,\n        data: activeDraggable.data,\n        node: activeNode,\n        rect: measuringConfiguration.draggable.measure(activeNode)\n      },\n      draggableNodes,\n      dragOverlay: {\n        node,\n        rect: measuringConfiguration.dragOverlay.measure(measurableNode)\n      },\n      droppableContainers,\n      measuringConfiguration,\n      transform: parsedTransform\n    });\n  });\n}\n\nfunction createDefaultDropAnimation(options) {\n  const {\n    duration,\n    easing,\n    sideEffects,\n    keyframes\n  } = { ...defaultDropAnimationConfiguration,\n    ...options\n  };\n  return _ref4 => {\n    let {\n      active,\n      dragOverlay,\n      transform,\n      ...rest\n    } = _ref4;\n\n    if (!duration) {\n      // Do not animate if animation duration is zero.\n      return;\n    }\n\n    const delta = {\n      x: dragOverlay.rect.left - active.rect.left,\n      y: dragOverlay.rect.top - active.rect.top\n    };\n    const scale = {\n      scaleX: transform.scaleX !== 1 ? active.rect.width * transform.scaleX / dragOverlay.rect.width : 1,\n      scaleY: transform.scaleY !== 1 ? active.rect.height * transform.scaleY / dragOverlay.rect.height : 1\n    };\n    const finalTransform = {\n      x: transform.x - delta.x,\n      y: transform.y - delta.y,\n      ...scale\n    };\n    const animationKeyframes = keyframes({ ...rest,\n      active,\n      dragOverlay,\n      transform: {\n        initial: transform,\n        final: finalTransform\n      }\n    });\n    const [firstKeyframe] = animationKeyframes;\n    const lastKeyframe = animationKeyframes[animationKeyframes.length - 1];\n\n    if (JSON.stringify(firstKeyframe) === JSON.stringify(lastKeyframe)) {\n      // The start and end keyframes are the same, infer that there is no animation needed.\n      return;\n    }\n\n    const cleanup = sideEffects == null ? void 0 : sideEffects({\n      active,\n      dragOverlay,\n      ...rest\n    });\n    const animation = dragOverlay.node.animate(animationKeyframes, {\n      duration,\n      easing,\n      fill: 'forwards'\n    });\n    return new Promise(resolve => {\n      animation.onfinish = () => {\n        cleanup == null ? void 0 : cleanup();\n        resolve();\n      };\n    });\n  };\n}\n\nlet key = 0;\nfunction useKey(id) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (id == null) {\n      return;\n    }\n\n    key++;\n    return key;\n  }, [id]);\n}\n\nconst DragOverlay = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().memo(_ref => {\n  let {\n    adjustScale = false,\n    children,\n    dropAnimation: dropAnimationConfig,\n    style,\n    transition,\n    modifiers,\n    wrapperElement = 'div',\n    className,\n    zIndex = 999\n  } = _ref;\n  const {\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggableNodes,\n    droppableContainers,\n    dragOverlay,\n    over,\n    measuringConfiguration,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    windowRect\n  } = useDndContext();\n  const transform = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(ActiveDraggableContext);\n  const key = useKey(active == null ? void 0 : active.id);\n  const modifiedTransform = applyModifiers(modifiers, {\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggingNodeRect: dragOverlay.rect,\n    over,\n    overlayNodeRect: dragOverlay.rect,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    transform,\n    windowRect\n  });\n  const initialRect = useInitialValue(activeNodeRect);\n  const dropAnimation = useDropAnimation({\n    config: dropAnimationConfig,\n    draggableNodes,\n    droppableContainers,\n    measuringConfiguration\n  }); // We need to wait for the active node to be measured before connecting the drag overlay ref\n  // otherwise collisions can be computed against a mispositioned drag overlay\n\n  const ref = initialRect ? dragOverlay.setRef : undefined;\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(NullifiedContextProvider, null, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(AnimationManager, {\n    animation: dropAnimation\n  }, active && key ? react__WEBPACK_IMPORTED_MODULE_0___default().createElement(PositionedOverlay, {\n    key: key,\n    id: active.id,\n    ref: ref,\n    as: wrapperElement,\n    activatorEvent: activatorEvent,\n    adjustScale: adjustScale,\n    className: className,\n    transition: transition,\n    rect: initialRect,\n    style: {\n      zIndex,\n      ...style\n    },\n    transform: modifiedTransform\n  }, children) : null));\n});\n\n\n//# sourceMappingURL=core.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@dnd-kit/core/dist/core.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@dnd-kit/utilities/dist/utilities.esm.js":
/*!***************************************************************!*\
  !*** ./node_modules/@dnd-kit/utilities/dist/utilities.esm.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CSS: () => (/* binding */ CSS),\n/* harmony export */   add: () => (/* binding */ add),\n/* harmony export */   canUseDOM: () => (/* binding */ canUseDOM),\n/* harmony export */   findFirstFocusableNode: () => (/* binding */ findFirstFocusableNode),\n/* harmony export */   getEventCoordinates: () => (/* binding */ getEventCoordinates),\n/* harmony export */   getOwnerDocument: () => (/* binding */ getOwnerDocument),\n/* harmony export */   getWindow: () => (/* binding */ getWindow),\n/* harmony export */   hasViewportRelativeCoordinates: () => (/* binding */ hasViewportRelativeCoordinates),\n/* harmony export */   isDocument: () => (/* binding */ isDocument),\n/* harmony export */   isHTMLElement: () => (/* binding */ isHTMLElement),\n/* harmony export */   isKeyboardEvent: () => (/* binding */ isKeyboardEvent),\n/* harmony export */   isNode: () => (/* binding */ isNode),\n/* harmony export */   isSVGElement: () => (/* binding */ isSVGElement),\n/* harmony export */   isTouchEvent: () => (/* binding */ isTouchEvent),\n/* harmony export */   isWindow: () => (/* binding */ isWindow),\n/* harmony export */   subtract: () => (/* binding */ subtract),\n/* harmony export */   useCombinedRefs: () => (/* binding */ useCombinedRefs),\n/* harmony export */   useEvent: () => (/* binding */ useEvent),\n/* harmony export */   useInterval: () => (/* binding */ useInterval),\n/* harmony export */   useIsomorphicLayoutEffect: () => (/* binding */ useIsomorphicLayoutEffect),\n/* harmony export */   useLatestValue: () => (/* binding */ useLatestValue),\n/* harmony export */   useLazyMemo: () => (/* binding */ useLazyMemo),\n/* harmony export */   useNodeRef: () => (/* binding */ useNodeRef),\n/* harmony export */   usePrevious: () => (/* binding */ usePrevious),\n/* harmony export */   useUniqueId: () => (/* binding */ useUniqueId)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\nfunction useCombinedRefs() {\n  for (var _len = arguments.length, refs = new Array(_len), _key = 0; _key < _len; _key++) {\n    refs[_key] = arguments[_key];\n  }\n\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => node => {\n    refs.forEach(ref => ref(node));\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  refs);\n}\n\n// https://github.com/facebook/react/blob/master/packages/shared/ExecutionEnvironment.js\nconst canUseDOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';\n\nfunction isWindow(element) {\n  const elementString = Object.prototype.toString.call(element);\n  return elementString === '[object Window]' || // In Electron context the Window object serializes to [object global]\n  elementString === '[object global]';\n}\n\nfunction isNode(node) {\n  return 'nodeType' in node;\n}\n\nfunction getWindow(target) {\n  var _target$ownerDocument, _target$ownerDocument2;\n\n  if (!target) {\n    return window;\n  }\n\n  if (isWindow(target)) {\n    return target;\n  }\n\n  if (!isNode(target)) {\n    return window;\n  }\n\n  return (_target$ownerDocument = (_target$ownerDocument2 = target.ownerDocument) == null ? void 0 : _target$ownerDocument2.defaultView) != null ? _target$ownerDocument : window;\n}\n\nfunction isDocument(node) {\n  const {\n    Document\n  } = getWindow(node);\n  return node instanceof Document;\n}\n\nfunction isHTMLElement(node) {\n  if (isWindow(node)) {\n    return false;\n  }\n\n  return node instanceof getWindow(node).HTMLElement;\n}\n\nfunction isSVGElement(node) {\n  return node instanceof getWindow(node).SVGElement;\n}\n\nfunction getOwnerDocument(target) {\n  if (!target) {\n    return document;\n  }\n\n  if (isWindow(target)) {\n    return target.document;\n  }\n\n  if (!isNode(target)) {\n    return document;\n  }\n\n  if (isDocument(target)) {\n    return target;\n  }\n\n  if (isHTMLElement(target) || isSVGElement(target)) {\n    return target.ownerDocument;\n  }\n\n  return document;\n}\n\n/**\r\n * A hook that resolves to useEffect on the server and useLayoutEffect on the client\r\n * @param callback {function} Callback function that is invoked when the dependencies of the hook change\r\n */\n\nconst useIsomorphicLayoutEffect = canUseDOM ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\nfunction useEvent(handler) {\n  const handlerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(handler);\n  useIsomorphicLayoutEffect(() => {\n    handlerRef.current = handler;\n  });\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    return handlerRef.current == null ? void 0 : handlerRef.current(...args);\n  }, []);\n}\n\nfunction useInterval() {\n  const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const set = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((listener, duration) => {\n    intervalRef.current = setInterval(listener, duration);\n  }, []);\n  const clear = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (intervalRef.current !== null) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n  }, []);\n  return [set, clear];\n}\n\nfunction useLatestValue(value, dependencies) {\n  if (dependencies === void 0) {\n    dependencies = [value];\n  }\n\n  const valueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(value);\n  useIsomorphicLayoutEffect(() => {\n    if (valueRef.current !== value) {\n      valueRef.current = value;\n    }\n  }, dependencies);\n  return valueRef;\n}\n\nfunction useLazyMemo(callback, dependencies) {\n  const valueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const newValue = callback(valueRef.current);\n    valueRef.current = newValue;\n    return newValue;\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [...dependencies]);\n}\n\nfunction useNodeRef(onChange) {\n  const onChangeHandler = useEvent(onChange);\n  const node = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const setNodeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(element => {\n    if (element !== node.current) {\n      onChangeHandler == null ? void 0 : onChangeHandler(element, node.current);\n    }\n\n    node.current = element;\n  }, //eslint-disable-next-line\n  []);\n  return [node, setNodeRef];\n}\n\nfunction usePrevious(value) {\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    ref.current = value;\n  }, [value]);\n  return ref.current;\n}\n\nlet ids = {};\nfunction useUniqueId(prefix, value) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (value) {\n      return value;\n    }\n\n    const id = ids[prefix] == null ? 0 : ids[prefix] + 1;\n    ids[prefix] = id;\n    return prefix + \"-\" + id;\n  }, [prefix, value]);\n}\n\nfunction createAdjustmentFn(modifier) {\n  return function (object) {\n    for (var _len = arguments.length, adjustments = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      adjustments[_key - 1] = arguments[_key];\n    }\n\n    return adjustments.reduce((accumulator, adjustment) => {\n      const entries = Object.entries(adjustment);\n\n      for (const [key, valueAdjustment] of entries) {\n        const value = accumulator[key];\n\n        if (value != null) {\n          accumulator[key] = value + modifier * valueAdjustment;\n        }\n      }\n\n      return accumulator;\n    }, { ...object\n    });\n  };\n}\n\nconst add = /*#__PURE__*/createAdjustmentFn(1);\nconst subtract = /*#__PURE__*/createAdjustmentFn(-1);\n\nfunction hasViewportRelativeCoordinates(event) {\n  return 'clientX' in event && 'clientY' in event;\n}\n\nfunction isKeyboardEvent(event) {\n  if (!event) {\n    return false;\n  }\n\n  const {\n    KeyboardEvent\n  } = getWindow(event.target);\n  return KeyboardEvent && event instanceof KeyboardEvent;\n}\n\nfunction isTouchEvent(event) {\n  if (!event) {\n    return false;\n  }\n\n  const {\n    TouchEvent\n  } = getWindow(event.target);\n  return TouchEvent && event instanceof TouchEvent;\n}\n\n/**\r\n * Returns the normalized x and y coordinates for mouse and touch events.\r\n */\n\nfunction getEventCoordinates(event) {\n  if (isTouchEvent(event)) {\n    if (event.touches && event.touches.length) {\n      const {\n        clientX: x,\n        clientY: y\n      } = event.touches[0];\n      return {\n        x,\n        y\n      };\n    } else if (event.changedTouches && event.changedTouches.length) {\n      const {\n        clientX: x,\n        clientY: y\n      } = event.changedTouches[0];\n      return {\n        x,\n        y\n      };\n    }\n  }\n\n  if (hasViewportRelativeCoordinates(event)) {\n    return {\n      x: event.clientX,\n      y: event.clientY\n    };\n  }\n\n  return null;\n}\n\nconst CSS = /*#__PURE__*/Object.freeze({\n  Translate: {\n    toString(transform) {\n      if (!transform) {\n        return;\n      }\n\n      const {\n        x,\n        y\n      } = transform;\n      return \"translate3d(\" + (x ? Math.round(x) : 0) + \"px, \" + (y ? Math.round(y) : 0) + \"px, 0)\";\n    }\n\n  },\n  Scale: {\n    toString(transform) {\n      if (!transform) {\n        return;\n      }\n\n      const {\n        scaleX,\n        scaleY\n      } = transform;\n      return \"scaleX(\" + scaleX + \") scaleY(\" + scaleY + \")\";\n    }\n\n  },\n  Transform: {\n    toString(transform) {\n      if (!transform) {\n        return;\n      }\n\n      return [CSS.Translate.toString(transform), CSS.Scale.toString(transform)].join(' ');\n    }\n\n  },\n  Transition: {\n    toString(_ref) {\n      let {\n        property,\n        duration,\n        easing\n      } = _ref;\n      return property + \" \" + duration + \"ms \" + easing;\n    }\n\n  }\n});\n\nconst SELECTOR = 'a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]';\nfunction findFirstFocusableNode(element) {\n  if (element.matches(SELECTOR)) {\n    return element;\n  }\n\n  return element.querySelector(SELECTOR);\n}\n\n\n//# sourceMappingURL=utilities.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGRuZC1raXQvdXRpbGl0aWVzL2Rpc3QvdXRpbGl0aWVzLmVzbS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBaUY7O0FBRWpGO0FBQ0Esc0VBQXNFLGFBQWE7QUFDbkY7QUFDQTs7QUFFQSxTQUFTLDhDQUFPO0FBQ2hCO0FBQ0EsR0FBRztBQUNIO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLG9CQUFvQixVQUFVO0FBQzlCOztBQUVBLDhDQUE4QyxrREFBZSxHQUFHLDRDQUFTOztBQUV6RTtBQUNBLHFCQUFxQiw2Q0FBTTtBQUMzQjtBQUNBO0FBQ0EsR0FBRztBQUNILFNBQVMsa0RBQVc7QUFDcEIsd0VBQXdFLGFBQWE7QUFDckY7QUFDQTs7QUFFQTtBQUNBLEdBQUc7QUFDSDs7QUFFQTtBQUNBLHNCQUFzQiw2Q0FBTTtBQUM1QixjQUFjLGtEQUFXO0FBQ3pCO0FBQ0EsR0FBRztBQUNILGdCQUFnQixrREFBVztBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLG1CQUFtQiw2Q0FBTTtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBOztBQUVBO0FBQ0EsbUJBQW1CLDZDQUFNO0FBQ3pCLFNBQVMsOENBQU87QUFDaEI7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGVBQWUsNkNBQU07QUFDckIscUJBQXFCLGtEQUFXO0FBQ2hDO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxjQUFjLDZDQUFNO0FBQ3BCLEVBQUUsZ0RBQVM7QUFDWDtBQUNBLEdBQUc7QUFDSDtBQUNBOztBQUVBO0FBQ0E7QUFDQSxTQUFTLDhDQUFPO0FBQ2hCO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7O0FBRUE7QUFDQTtBQUNBLGtHQUFrRyxhQUFhO0FBQy9HO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsS0FBSyxJQUFJO0FBQ1QsS0FBSztBQUNMO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBOztBQUVBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBOztBQUVBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUEsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBOztBQUVBO0FBQ0EsQ0FBQzs7QUFFRDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRW1YO0FBQ25YIiwic291cmNlcyI6WyJEOlxcV29ya1xcUGF5bG9hZFxcdGVzdC1wYXlsb2FkLWpzb25cXGNvcnBvcmF0ZS13ZWJzaXRlXFxub2RlX21vZHVsZXNcXEBkbmQta2l0XFx1dGlsaXRpZXNcXGRpc3RcXHV0aWxpdGllcy5lc20uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlTWVtbywgdXNlTGF5b3V0RWZmZWN0LCB1c2VFZmZlY3QsIHVzZVJlZiwgdXNlQ2FsbGJhY2sgfSBmcm9tICdyZWFjdCc7XG5cbmZ1bmN0aW9uIHVzZUNvbWJpbmVkUmVmcygpIHtcbiAgZm9yICh2YXIgX2xlbiA9IGFyZ3VtZW50cy5sZW5ndGgsIHJlZnMgPSBuZXcgQXJyYXkoX2xlbiksIF9rZXkgPSAwOyBfa2V5IDwgX2xlbjsgX2tleSsrKSB7XG4gICAgcmVmc1tfa2V5XSA9IGFyZ3VtZW50c1tfa2V5XTtcbiAgfVxuXG4gIHJldHVybiB1c2VNZW1vKCgpID0+IG5vZGUgPT4ge1xuICAgIHJlZnMuZm9yRWFjaChyZWYgPT4gcmVmKG5vZGUpKTtcbiAgfSwgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL2V4aGF1c3RpdmUtZGVwc1xuICByZWZzKTtcbn1cblxuLy8gaHR0cHM6Ly9naXRodWIuY29tL2ZhY2Vib29rL3JlYWN0L2Jsb2IvbWFzdGVyL3BhY2thZ2VzL3NoYXJlZC9FeGVjdXRpb25FbnZpcm9ubWVudC5qc1xuY29uc3QgY2FuVXNlRE9NID0gdHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgdHlwZW9mIHdpbmRvdy5kb2N1bWVudCAhPT0gJ3VuZGVmaW5lZCcgJiYgdHlwZW9mIHdpbmRvdy5kb2N1bWVudC5jcmVhdGVFbGVtZW50ICE9PSAndW5kZWZpbmVkJztcblxuZnVuY3Rpb24gaXNXaW5kb3coZWxlbWVudCkge1xuICBjb25zdCBlbGVtZW50U3RyaW5nID0gT2JqZWN0LnByb3RvdHlwZS50b1N0cmluZy5jYWxsKGVsZW1lbnQpO1xuICByZXR1cm4gZWxlbWVudFN0cmluZyA9PT0gJ1tvYmplY3QgV2luZG93XScgfHwgLy8gSW4gRWxlY3Ryb24gY29udGV4dCB0aGUgV2luZG93IG9iamVjdCBzZXJpYWxpemVzIHRvIFtvYmplY3QgZ2xvYmFsXVxuICBlbGVtZW50U3RyaW5nID09PSAnW29iamVjdCBnbG9iYWxdJztcbn1cblxuZnVuY3Rpb24gaXNOb2RlKG5vZGUpIHtcbiAgcmV0dXJuICdub2RlVHlwZScgaW4gbm9kZTtcbn1cblxuZnVuY3Rpb24gZ2V0V2luZG93KHRhcmdldCkge1xuICB2YXIgX3RhcmdldCRvd25lckRvY3VtZW50LCBfdGFyZ2V0JG93bmVyRG9jdW1lbnQyO1xuXG4gIGlmICghdGFyZ2V0KSB7XG4gICAgcmV0dXJuIHdpbmRvdztcbiAgfVxuXG4gIGlmIChpc1dpbmRvdyh0YXJnZXQpKSB7XG4gICAgcmV0dXJuIHRhcmdldDtcbiAgfVxuXG4gIGlmICghaXNOb2RlKHRhcmdldCkpIHtcbiAgICByZXR1cm4gd2luZG93O1xuICB9XG5cbiAgcmV0dXJuIChfdGFyZ2V0JG93bmVyRG9jdW1lbnQgPSAoX3RhcmdldCRvd25lckRvY3VtZW50MiA9IHRhcmdldC5vd25lckRvY3VtZW50KSA9PSBudWxsID8gdm9pZCAwIDogX3RhcmdldCRvd25lckRvY3VtZW50Mi5kZWZhdWx0VmlldykgIT0gbnVsbCA/IF90YXJnZXQkb3duZXJEb2N1bWVudCA6IHdpbmRvdztcbn1cblxuZnVuY3Rpb24gaXNEb2N1bWVudChub2RlKSB7XG4gIGNvbnN0IHtcbiAgICBEb2N1bWVudFxuICB9ID0gZ2V0V2luZG93KG5vZGUpO1xuICByZXR1cm4gbm9kZSBpbnN0YW5jZW9mIERvY3VtZW50O1xufVxuXG5mdW5jdGlvbiBpc0hUTUxFbGVtZW50KG5vZGUpIHtcbiAgaWYgKGlzV2luZG93KG5vZGUpKSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG5cbiAgcmV0dXJuIG5vZGUgaW5zdGFuY2VvZiBnZXRXaW5kb3cobm9kZSkuSFRNTEVsZW1lbnQ7XG59XG5cbmZ1bmN0aW9uIGlzU1ZHRWxlbWVudChub2RlKSB7XG4gIHJldHVybiBub2RlIGluc3RhbmNlb2YgZ2V0V2luZG93KG5vZGUpLlNWR0VsZW1lbnQ7XG59XG5cbmZ1bmN0aW9uIGdldE93bmVyRG9jdW1lbnQodGFyZ2V0KSB7XG4gIGlmICghdGFyZ2V0KSB7XG4gICAgcmV0dXJuIGRvY3VtZW50O1xuICB9XG5cbiAgaWYgKGlzV2luZG93KHRhcmdldCkpIHtcbiAgICByZXR1cm4gdGFyZ2V0LmRvY3VtZW50O1xuICB9XG5cbiAgaWYgKCFpc05vZGUodGFyZ2V0KSkge1xuICAgIHJldHVybiBkb2N1bWVudDtcbiAgfVxuXG4gIGlmIChpc0RvY3VtZW50KHRhcmdldCkpIHtcbiAgICByZXR1cm4gdGFyZ2V0O1xuICB9XG5cbiAgaWYgKGlzSFRNTEVsZW1lbnQodGFyZ2V0KSB8fCBpc1NWR0VsZW1lbnQodGFyZ2V0KSkge1xuICAgIHJldHVybiB0YXJnZXQub3duZXJEb2N1bWVudDtcbiAgfVxuXG4gIHJldHVybiBkb2N1bWVudDtcbn1cblxuLyoqXHJcbiAqIEEgaG9vayB0aGF0IHJlc29sdmVzIHRvIHVzZUVmZmVjdCBvbiB0aGUgc2VydmVyIGFuZCB1c2VMYXlvdXRFZmZlY3Qgb24gdGhlIGNsaWVudFxyXG4gKiBAcGFyYW0gY2FsbGJhY2sge2Z1bmN0aW9ufSBDYWxsYmFjayBmdW5jdGlvbiB0aGF0IGlzIGludm9rZWQgd2hlbiB0aGUgZGVwZW5kZW5jaWVzIG9mIHRoZSBob29rIGNoYW5nZVxyXG4gKi9cblxuY29uc3QgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCA9IGNhblVzZURPTSA/IHVzZUxheW91dEVmZmVjdCA6IHVzZUVmZmVjdDtcblxuZnVuY3Rpb24gdXNlRXZlbnQoaGFuZGxlcikge1xuICBjb25zdCBoYW5kbGVyUmVmID0gdXNlUmVmKGhhbmRsZXIpO1xuICB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0KCgpID0+IHtcbiAgICBoYW5kbGVyUmVmLmN1cnJlbnQgPSBoYW5kbGVyO1xuICB9KTtcbiAgcmV0dXJuIHVzZUNhbGxiYWNrKGZ1bmN0aW9uICgpIHtcbiAgICBmb3IgKHZhciBfbGVuID0gYXJndW1lbnRzLmxlbmd0aCwgYXJncyA9IG5ldyBBcnJheShfbGVuKSwgX2tleSA9IDA7IF9rZXkgPCBfbGVuOyBfa2V5KyspIHtcbiAgICAgIGFyZ3NbX2tleV0gPSBhcmd1bWVudHNbX2tleV07XG4gICAgfVxuXG4gICAgcmV0dXJuIGhhbmRsZXJSZWYuY3VycmVudCA9PSBudWxsID8gdm9pZCAwIDogaGFuZGxlclJlZi5jdXJyZW50KC4uLmFyZ3MpO1xuICB9LCBbXSk7XG59XG5cbmZ1bmN0aW9uIHVzZUludGVydmFsKCkge1xuICBjb25zdCBpbnRlcnZhbFJlZiA9IHVzZVJlZihudWxsKTtcbiAgY29uc3Qgc2V0ID0gdXNlQ2FsbGJhY2soKGxpc3RlbmVyLCBkdXJhdGlvbikgPT4ge1xuICAgIGludGVydmFsUmVmLmN1cnJlbnQgPSBzZXRJbnRlcnZhbChsaXN0ZW5lciwgZHVyYXRpb24pO1xuICB9LCBbXSk7XG4gIGNvbnN0IGNsZWFyID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGlmIChpbnRlcnZhbFJlZi5jdXJyZW50ICE9PSBudWxsKSB7XG4gICAgICBjbGVhckludGVydmFsKGludGVydmFsUmVmLmN1cnJlbnQpO1xuICAgICAgaW50ZXJ2YWxSZWYuY3VycmVudCA9IG51bGw7XG4gICAgfVxuICB9LCBbXSk7XG4gIHJldHVybiBbc2V0LCBjbGVhcl07XG59XG5cbmZ1bmN0aW9uIHVzZUxhdGVzdFZhbHVlKHZhbHVlLCBkZXBlbmRlbmNpZXMpIHtcbiAgaWYgKGRlcGVuZGVuY2llcyA9PT0gdm9pZCAwKSB7XG4gICAgZGVwZW5kZW5jaWVzID0gW3ZhbHVlXTtcbiAgfVxuXG4gIGNvbnN0IHZhbHVlUmVmID0gdXNlUmVmKHZhbHVlKTtcbiAgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHZhbHVlUmVmLmN1cnJlbnQgIT09IHZhbHVlKSB7XG4gICAgICB2YWx1ZVJlZi5jdXJyZW50ID0gdmFsdWU7XG4gICAgfVxuICB9LCBkZXBlbmRlbmNpZXMpO1xuICByZXR1cm4gdmFsdWVSZWY7XG59XG5cbmZ1bmN0aW9uIHVzZUxhenlNZW1vKGNhbGxiYWNrLCBkZXBlbmRlbmNpZXMpIHtcbiAgY29uc3QgdmFsdWVSZWYgPSB1c2VSZWYoKTtcbiAgcmV0dXJuIHVzZU1lbW8oKCkgPT4ge1xuICAgIGNvbnN0IG5ld1ZhbHVlID0gY2FsbGJhY2sodmFsdWVSZWYuY3VycmVudCk7XG4gICAgdmFsdWVSZWYuY3VycmVudCA9IG5ld1ZhbHVlO1xuICAgIHJldHVybiBuZXdWYWx1ZTtcbiAgfSwgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL2V4aGF1c3RpdmUtZGVwc1xuICBbLi4uZGVwZW5kZW5jaWVzXSk7XG59XG5cbmZ1bmN0aW9uIHVzZU5vZGVSZWYob25DaGFuZ2UpIHtcbiAgY29uc3Qgb25DaGFuZ2VIYW5kbGVyID0gdXNlRXZlbnQob25DaGFuZ2UpO1xuICBjb25zdCBub2RlID0gdXNlUmVmKG51bGwpO1xuICBjb25zdCBzZXROb2RlUmVmID0gdXNlQ2FsbGJhY2soZWxlbWVudCA9PiB7XG4gICAgaWYgKGVsZW1lbnQgIT09IG5vZGUuY3VycmVudCkge1xuICAgICAgb25DaGFuZ2VIYW5kbGVyID09IG51bGwgPyB2b2lkIDAgOiBvbkNoYW5nZUhhbmRsZXIoZWxlbWVudCwgbm9kZS5jdXJyZW50KTtcbiAgICB9XG5cbiAgICBub2RlLmN1cnJlbnQgPSBlbGVtZW50O1xuICB9LCAvL2VzbGludC1kaXNhYmxlLW5leHQtbGluZVxuICBbXSk7XG4gIHJldHVybiBbbm9kZSwgc2V0Tm9kZVJlZl07XG59XG5cbmZ1bmN0aW9uIHVzZVByZXZpb3VzKHZhbHVlKSB7XG4gIGNvbnN0IHJlZiA9IHVzZVJlZigpO1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHJlZi5jdXJyZW50ID0gdmFsdWU7XG4gIH0sIFt2YWx1ZV0pO1xuICByZXR1cm4gcmVmLmN1cnJlbnQ7XG59XG5cbmxldCBpZHMgPSB7fTtcbmZ1bmN0aW9uIHVzZVVuaXF1ZUlkKHByZWZpeCwgdmFsdWUpIHtcbiAgcmV0dXJuIHVzZU1lbW8oKCkgPT4ge1xuICAgIGlmICh2YWx1ZSkge1xuICAgICAgcmV0dXJuIHZhbHVlO1xuICAgIH1cblxuICAgIGNvbnN0IGlkID0gaWRzW3ByZWZpeF0gPT0gbnVsbCA/IDAgOiBpZHNbcHJlZml4XSArIDE7XG4gICAgaWRzW3ByZWZpeF0gPSBpZDtcbiAgICByZXR1cm4gcHJlZml4ICsgXCItXCIgKyBpZDtcbiAgfSwgW3ByZWZpeCwgdmFsdWVdKTtcbn1cblxuZnVuY3Rpb24gY3JlYXRlQWRqdXN0bWVudEZuKG1vZGlmaWVyKSB7XG4gIHJldHVybiBmdW5jdGlvbiAob2JqZWN0KSB7XG4gICAgZm9yICh2YXIgX2xlbiA9IGFyZ3VtZW50cy5sZW5ndGgsIGFkanVzdG1lbnRzID0gbmV3IEFycmF5KF9sZW4gPiAxID8gX2xlbiAtIDEgOiAwKSwgX2tleSA9IDE7IF9rZXkgPCBfbGVuOyBfa2V5KyspIHtcbiAgICAgIGFkanVzdG1lbnRzW19rZXkgLSAxXSA9IGFyZ3VtZW50c1tfa2V5XTtcbiAgICB9XG5cbiAgICByZXR1cm4gYWRqdXN0bWVudHMucmVkdWNlKChhY2N1bXVsYXRvciwgYWRqdXN0bWVudCkgPT4ge1xuICAgICAgY29uc3QgZW50cmllcyA9IE9iamVjdC5lbnRyaWVzKGFkanVzdG1lbnQpO1xuXG4gICAgICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlQWRqdXN0bWVudF0gb2YgZW50cmllcykge1xuICAgICAgICBjb25zdCB2YWx1ZSA9IGFjY3VtdWxhdG9yW2tleV07XG5cbiAgICAgICAgaWYgKHZhbHVlICE9IG51bGwpIHtcbiAgICAgICAgICBhY2N1bXVsYXRvcltrZXldID0gdmFsdWUgKyBtb2RpZmllciAqIHZhbHVlQWRqdXN0bWVudDtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICByZXR1cm4gYWNjdW11bGF0b3I7XG4gICAgfSwgeyAuLi5vYmplY3RcbiAgICB9KTtcbiAgfTtcbn1cblxuY29uc3QgYWRkID0gLyojX19QVVJFX18qL2NyZWF0ZUFkanVzdG1lbnRGbigxKTtcbmNvbnN0IHN1YnRyYWN0ID0gLyojX19QVVJFX18qL2NyZWF0ZUFkanVzdG1lbnRGbigtMSk7XG5cbmZ1bmN0aW9uIGhhc1ZpZXdwb3J0UmVsYXRpdmVDb29yZGluYXRlcyhldmVudCkge1xuICByZXR1cm4gJ2NsaWVudFgnIGluIGV2ZW50ICYmICdjbGllbnRZJyBpbiBldmVudDtcbn1cblxuZnVuY3Rpb24gaXNLZXlib2FyZEV2ZW50KGV2ZW50KSB7XG4gIGlmICghZXZlbnQpIHtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cblxuICBjb25zdCB7XG4gICAgS2V5Ym9hcmRFdmVudFxuICB9ID0gZ2V0V2luZG93KGV2ZW50LnRhcmdldCk7XG4gIHJldHVybiBLZXlib2FyZEV2ZW50ICYmIGV2ZW50IGluc3RhbmNlb2YgS2V5Ym9hcmRFdmVudDtcbn1cblxuZnVuY3Rpb24gaXNUb3VjaEV2ZW50KGV2ZW50KSB7XG4gIGlmICghZXZlbnQpIHtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cblxuICBjb25zdCB7XG4gICAgVG91Y2hFdmVudFxuICB9ID0gZ2V0V2luZG93KGV2ZW50LnRhcmdldCk7XG4gIHJldHVybiBUb3VjaEV2ZW50ICYmIGV2ZW50IGluc3RhbmNlb2YgVG91Y2hFdmVudDtcbn1cblxuLyoqXHJcbiAqIFJldHVybnMgdGhlIG5vcm1hbGl6ZWQgeCBhbmQgeSBjb29yZGluYXRlcyBmb3IgbW91c2UgYW5kIHRvdWNoIGV2ZW50cy5cclxuICovXG5cbmZ1bmN0aW9uIGdldEV2ZW50Q29vcmRpbmF0ZXMoZXZlbnQpIHtcbiAgaWYgKGlzVG91Y2hFdmVudChldmVudCkpIHtcbiAgICBpZiAoZXZlbnQudG91Y2hlcyAmJiBldmVudC50b3VjaGVzLmxlbmd0aCkge1xuICAgICAgY29uc3Qge1xuICAgICAgICBjbGllbnRYOiB4LFxuICAgICAgICBjbGllbnRZOiB5XG4gICAgICB9ID0gZXZlbnQudG91Y2hlc1swXTtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHgsXG4gICAgICAgIHlcbiAgICAgIH07XG4gICAgfSBlbHNlIGlmIChldmVudC5jaGFuZ2VkVG91Y2hlcyAmJiBldmVudC5jaGFuZ2VkVG91Y2hlcy5sZW5ndGgpIHtcbiAgICAgIGNvbnN0IHtcbiAgICAgICAgY2xpZW50WDogeCxcbiAgICAgICAgY2xpZW50WTogeVxuICAgICAgfSA9IGV2ZW50LmNoYW5nZWRUb3VjaGVzWzBdO1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgeCxcbiAgICAgICAgeVxuICAgICAgfTtcbiAgICB9XG4gIH1cblxuICBpZiAoaGFzVmlld3BvcnRSZWxhdGl2ZUNvb3JkaW5hdGVzKGV2ZW50KSkge1xuICAgIHJldHVybiB7XG4gICAgICB4OiBldmVudC5jbGllbnRYLFxuICAgICAgeTogZXZlbnQuY2xpZW50WVxuICAgIH07XG4gIH1cblxuICByZXR1cm4gbnVsbDtcbn1cblxuY29uc3QgQ1NTID0gLyojX19QVVJFX18qL09iamVjdC5mcmVlemUoe1xuICBUcmFuc2xhdGU6IHtcbiAgICB0b1N0cmluZyh0cmFuc2Zvcm0pIHtcbiAgICAgIGlmICghdHJhbnNmb3JtKSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgY29uc3Qge1xuICAgICAgICB4LFxuICAgICAgICB5XG4gICAgICB9ID0gdHJhbnNmb3JtO1xuICAgICAgcmV0dXJuIFwidHJhbnNsYXRlM2QoXCIgKyAoeCA/IE1hdGgucm91bmQoeCkgOiAwKSArIFwicHgsIFwiICsgKHkgPyBNYXRoLnJvdW5kKHkpIDogMCkgKyBcInB4LCAwKVwiO1xuICAgIH1cblxuICB9LFxuICBTY2FsZToge1xuICAgIHRvU3RyaW5nKHRyYW5zZm9ybSkge1xuICAgICAgaWYgKCF0cmFuc2Zvcm0pIHtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICBjb25zdCB7XG4gICAgICAgIHNjYWxlWCxcbiAgICAgICAgc2NhbGVZXG4gICAgICB9ID0gdHJhbnNmb3JtO1xuICAgICAgcmV0dXJuIFwic2NhbGVYKFwiICsgc2NhbGVYICsgXCIpIHNjYWxlWShcIiArIHNjYWxlWSArIFwiKVwiO1xuICAgIH1cblxuICB9LFxuICBUcmFuc2Zvcm06IHtcbiAgICB0b1N0cmluZyh0cmFuc2Zvcm0pIHtcbiAgICAgIGlmICghdHJhbnNmb3JtKSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIFtDU1MuVHJhbnNsYXRlLnRvU3RyaW5nKHRyYW5zZm9ybSksIENTUy5TY2FsZS50b1N0cmluZyh0cmFuc2Zvcm0pXS5qb2luKCcgJyk7XG4gICAgfVxuXG4gIH0sXG4gIFRyYW5zaXRpb246IHtcbiAgICB0b1N0cmluZyhfcmVmKSB7XG4gICAgICBsZXQge1xuICAgICAgICBwcm9wZXJ0eSxcbiAgICAgICAgZHVyYXRpb24sXG4gICAgICAgIGVhc2luZ1xuICAgICAgfSA9IF9yZWY7XG4gICAgICByZXR1cm4gcHJvcGVydHkgKyBcIiBcIiArIGR1cmF0aW9uICsgXCJtcyBcIiArIGVhc2luZztcbiAgICB9XG5cbiAgfVxufSk7XG5cbmNvbnN0IFNFTEVDVE9SID0gJ2EsZnJhbWUsaWZyYW1lLGlucHV0Om5vdChbdHlwZT1oaWRkZW5dKTpub3QoOmRpc2FibGVkKSxzZWxlY3Q6bm90KDpkaXNhYmxlZCksdGV4dGFyZWE6bm90KDpkaXNhYmxlZCksYnV0dG9uOm5vdCg6ZGlzYWJsZWQpLCpbdGFiaW5kZXhdJztcbmZ1bmN0aW9uIGZpbmRGaXJzdEZvY3VzYWJsZU5vZGUoZWxlbWVudCkge1xuICBpZiAoZWxlbWVudC5tYXRjaGVzKFNFTEVDVE9SKSkge1xuICAgIHJldHVybiBlbGVtZW50O1xuICB9XG5cbiAgcmV0dXJuIGVsZW1lbnQucXVlcnlTZWxlY3RvcihTRUxFQ1RPUik7XG59XG5cbmV4cG9ydCB7IENTUywgYWRkLCBjYW5Vc2VET00sIGZpbmRGaXJzdEZvY3VzYWJsZU5vZGUsIGdldEV2ZW50Q29vcmRpbmF0ZXMsIGdldE93bmVyRG9jdW1lbnQsIGdldFdpbmRvdywgaGFzVmlld3BvcnRSZWxhdGl2ZUNvb3JkaW5hdGVzLCBpc0RvY3VtZW50LCBpc0hUTUxFbGVtZW50LCBpc0tleWJvYXJkRXZlbnQsIGlzTm9kZSwgaXNTVkdFbGVtZW50LCBpc1RvdWNoRXZlbnQsIGlzV2luZG93LCBzdWJ0cmFjdCwgdXNlQ29tYmluZWRSZWZzLCB1c2VFdmVudCwgdXNlSW50ZXJ2YWwsIHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QsIHVzZUxhdGVzdFZhbHVlLCB1c2VMYXp5TWVtbywgdXNlTm9kZVJlZiwgdXNlUHJldmlvdXMsIHVzZVVuaXF1ZUlkIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD11dGlsaXRpZXMuZXNtLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@dnd-kit/utilities/dist/utilities.esm.js\n");

/***/ })

};
;