{"version": 3, "caseSensitive": false, "basePath": "", "rewrites": {"beforeFiles": [], "afterFiles": [{"source": "/media/:path*", "destination": "/api/media/:path*", "regex": "^\\/media(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))?(?:\\/)?$", "check": true}], "fallback": []}, "redirects": [{"source": "/:path+/", "destination": "/:path+", "permanent": true, "internal": true, "regex": "^(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))\\/$"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}], "regex": "^(?:\\/(.*))(?:\\/)?$"}, {"source": "/:path*", "headers": [{"key": "Accept-CH", "value": "Sec-CH-Prefers-Color-Scheme"}, {"key": "Vary", "value": "Sec-CH-Prefers-Color-Scheme"}, {"key": "Critical-CH", "value": "Sec-CH-Prefers-Color-Scheme"}], "regex": "^(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))?(?:\\/)?$"}]}